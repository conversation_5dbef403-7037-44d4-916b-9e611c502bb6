package com.gclife.platform.task;

import com.gclife.platform.service.business.OperationLogBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @description: 消费日志线程池
 * @author: aurong
 * @create: 2022-02-25
 **/
@Slf4j
@Component
public class LogConsumerTask {
    private static final Logger logger = LoggerFactory.getLogger(LogConsumerTask.class);

    /**
     * 线程池的数量
     */
    private static final int DEFAULT_TASK_THREAD_NUM= Runtime.getRuntime().availableProcessors()*2;
    /**
     * 匹配线程调度器
     */
    private ScheduledThreadPoolExecutor taskExecutor;

    @Autowired
    OperationLogBusinessService operationLogBusinessService;

    @PostConstruct
    public void init() {
        logger.info("初始化操作日志消息消费任务线程.");
        taskExecutor = new ScheduledThreadPoolExecutor(DEFAULT_TASK_THREAD_NUM);
        logger.info("初始化{}个任务线程.", DEFAULT_TASK_THREAD_NUM);
        for (int i = 0; i < DEFAULT_TASK_THREAD_NUM; i++) {
            taskExecutor.scheduleWithFixedDelay(new LogHandlerThread(),i * 5, 3, TimeUnit.SECONDS);
        }
    }

    class LogHandlerThread implements Runnable{
        @Override
        public void run() {
            try {
                log.debug("消费操作日志开始");
                execute();
                log.debug("消费操作日志完成");
            } catch (Throwable e) {
                log.error("消费操作日志保存错误 Throwable,{}", ExceptionUtils.getFullStackTrace(e));
            }
        }
    }

    public void execute(){
        operationLogBusinessService.consumeLog();
    }
}

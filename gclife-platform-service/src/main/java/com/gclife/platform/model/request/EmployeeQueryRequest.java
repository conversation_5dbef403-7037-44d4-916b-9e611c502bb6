package com.gclife.platform.model.request;


import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BaseRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by cqh on 17-9-5
 * <AUTHOR>
 */
public class EmployeeQueryRequest extends BaseRequest {

    @ApiModelProperty(value = "用户id集合",example = "[\"USERS_6a18c22a-d6ad-48b8-9d3f-a0d09f651545\",\"wcl\"]")
    String  listUserId;

    public String getListUserId() {
        return listUserId;
    }

    public void setListUserId(String listUserId) {
        this.listUserId = listUserId;
    }
}

package com.gclife.platform.model.request;

import com.gclife.platform.base.model.vo.UsersVo;

import java.util.List;

public class SaveUserRequest extends UsersVo {
    /**
     * 代理人姓名
     */
    private String userId;
    /**
     * 代理人姓名
     */
    private String agentName;
    /**
     * 代理人手机号
     */
    private String mobile;
    /**
     * 代理人编码
     */
    private String agentCode;
    /**
     * 机构编码
     */
    private String branchCode;
    /**
     * 机构Id
     */
    private String branchId;
    /**
     * 性别
     */
    private String sex;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 代理人账户
     */
    private AccountRequest account;
    /**
     * 代理人账户
     */
    private List<AccountRequest> accounts;


    @Override
    public String getEmail() {
        return email;
    }

    @Override
    public void setEmail(String email) {
        this.email = email;
    }

    public AccountRequest getAccount() {
        return account;
    }

    public void setAccount(AccountRequest account) {
        this.account = account;
    }

    public List<AccountRequest> getAccounts() {
        return accounts;
    }

    public void setAccounts(List<AccountRequest> accounts) {
        this.accounts = accounts;
    }

    @Override
    public String getUserId() {
        return userId;
    }

    @Override
    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String getAgentName() {
        return agentName;
    }

    @Override
    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    @Override
    public String getMobile() {
        return mobile;
    }

    @Override
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @Override
    public String getAgentCode() {
        return agentCode;
    }

    @Override
    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    @Override
    public String getBranchCode() {
        return branchCode;
    }

    @Override
    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    @Override
    public String getBranchId() {
        return branchId;
    }

    @Override
    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    @Override
    public String getSex() {
        return sex;
    }

    @Override
    public void setSex(String sex) {
        this.sex = sex;
    }
}



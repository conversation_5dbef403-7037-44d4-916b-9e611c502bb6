package com.gclife.platform.model.request;

import com.alibaba.fastjson.JSON;

import java.io.Serializable;

/**
 * @description: 钉钉机器人消息
 * @author: chenjinrong
 * @create: 2021-09-02
 **/
public class DingRobotSendRequest implements Serializable {
    /**
     * 此消息类型为固定markdown
     */
    private String markdown;

    /**
     * 消息类型
     */
    private String msgtype;

    /**
     * text类型
     */
    private String text;

    public void setMsgtype(String msgtype) {
        this.msgtype = msgtype;
    }

    public String getMsgtype() {
        return this.msgtype;
    }

    public String getMarkdown() {
        return this.markdown;
    }

    public void setMarkdown(String markdown) {
        this.markdown = markdown;
    }

    public void setMarkdown(Markdown markdown) {

        this.markdown = JSON.toJSONString(markdown);
    }

    public void setText(String text) {
        this.text = text;
    }

    public void setText(Text text) {
        this.text =JSON.toJSONString(text);
    }

    public String getText() {
        return this.text;
    }

    /**
     * 此消息类型为固定markdown
     *
     * <AUTHOR> auto create
     * @since 1.0, null
     */
    public static class Markdown implements Serializable {
        private static final long serialVersionUID = 3594458467989136125L;
        /**
         * markdown格式的消息
         */
        private String text;
        /**
         * 首屏会话透出的展示内容
         */
        private String title;

        public String getText() {
            return this.text;
        }
        public void setText(String text) {
            this.text = text;
        }
        public String getTitle() {
            return this.title;
        }
        public void setTitle(String title) {
            this.title = title;
        }
    }

    /**
     * text类型
     *
     * <AUTHOR> auto create
     * @since 1.0, null
     */
    public static class Text implements Serializable  {
        private static final long serialVersionUID = 2227545367812542412L;
        /**
         * text类型
         */
        private String content;

        public String getContent() {
            return this.content;
        }
        public void setContent(String content) {
            this.content = content;
        }
    }
}

package com.gclife.platform.model.request;


import com.gclife.common.model.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * Created by cqh on 17-9-5.
 * <AUTHOR>
 */
public class UserRequest extends BasePageRequest {

    @ApiModelProperty(value = "账户",example = "caoqinghua",required = true)
    String username;

    @ApiModelProperty(value = "姓名",example = "caoqinghua",required = true)
    String name;

    @ApiModelProperty(value = "邮件",example = "<EMAIL>",required = true)
    String email;

    @Pattern(regexp = "\\d")
    @Min(10)
    @Max(120)
    @NotNull
    @ApiModelProperty(value = "年龄",example = "16")
    long age;


    @NotNull
    @ApiModelProperty(value = "性别",example = "MALE",hidden = true)
    String sex;

    @ApiModelProperty(value = "是否启用",example = "ENABLED",required = true)
    String enabled;

    @ApiModelProperty(value = "语言",example = "ZH_CN",required = true)
    String language;



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public long getAge() {
        return age;
    }

    public void setAge(long age) {
        this.age = age;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}

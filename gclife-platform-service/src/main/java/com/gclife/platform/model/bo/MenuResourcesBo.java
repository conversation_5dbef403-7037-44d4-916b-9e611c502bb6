package com.gclife.platform.model.bo;


import com.gclife.common.model.base.Resources;
import com.gclife.platform.core.jooq.tables.pojos.ResourcesPo;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 上午11:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 资源业务对象
 * <AUTHOR>
 */
public class MenuResourcesBo extends ResourcesPo {
    private String parentResourceId;

    private String internationalText;

    private boolean hasChildDomain;

    public String getParentResourceId() {
        return parentResourceId;
    }

    public void setParentResourceId(String parentResourceId) {
        this.parentResourceId = parentResourceId;
    }

    public String getInternationalText() {
        return internationalText;
    }

    public void setInternationalText(String internationalText) {
        this.internationalText = internationalText;
    }

    private boolean hasChildUrl;

    public boolean isHasChildUrl() {
        return hasChildUrl;
    }

    public void setHasChildUrl(boolean hasChildUrl) {
        this.hasChildUrl = hasChildUrl;
    }

    public boolean isHasChildDomain() {
        return hasChildDomain;
    }

    public void setHasChildDomain(boolean hasChildDomain) {
        this.hasChildDomain = hasChildDomain;
    }
}
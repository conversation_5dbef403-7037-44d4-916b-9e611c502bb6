package com.gclife.platform.model.bo;


import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.core.jooq.tables.records.UsersRecord;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 上午11:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户业务对象
 * <AUTHOR>
 */
public class UsersOauthBo extends UsersPo {

    /**用户地址*/
    private String address;

    private List<RolesBo> listRoles;

    public List<RolesBo> getListRoles() {
        return listRoles;
    }

    public void setListRoles(List<RolesBo> listRoles) {
        this.listRoles = listRoles;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }


    /**
     * 构造方法
     * @param value
     */
    public UsersOauthBo(UsersRecord value) {
        if (value == null) {
            return;
        }
        this.setUserId(value.getUserId());
        this.setUsername(value.getUsername());
        this.setPassword(value.getPassword());
        this.setEnabled(value.getEnabled());
        this.setName(value.getName());
        this.setGender(value.getGender());
        this.setEmail(value.getEmail());
        this.setCreatedDate(value.getCreatedDate());
        this.setUpdatedDate(value.getUpdatedDate());
        this.setLastLogin(value.getLastLogin());
    }


}
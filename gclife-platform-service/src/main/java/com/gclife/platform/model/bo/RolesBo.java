package com.gclife.platform.model.bo;


import com.gclife.platform.core.jooq.tables.pojos.RolesPo;
import com.gclife.platform.core.jooq.tables.records.RolesRecord;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 上午11:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 角色业务对象
 * <AUTHOR>
 */
public class RolesBo extends RolesPo {

    private List<PermissionsBo>  listPermissions;

    public List<PermissionsBo> getListPermissions() {
        return listPermissions;
    }

    public void setListPermissions(List<PermissionsBo> listPermissions) {
        this.listPermissions = listPermissions;
    }


}
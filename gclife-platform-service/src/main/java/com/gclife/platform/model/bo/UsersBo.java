package com.gclife.platform.model.bo;

import com.gclife.platform.core.jooq.tables.pojos.UsersPo;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:
 */
public class UsersBo extends UsersPo {
    private String branchId;

    private Long loginLast;

    private Long loginCount;
    private String url;

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getLoginLast() {
        return loginLast;
    }

    public void setLoginLast(Long loginLast) {
        this.loginLast = loginLast;
    }

    public Long getLoginCount() {
        return loginCount;
    }

    public void setLoginCount(Long loginCount) {
        this.loginCount = loginCount;
    }
}
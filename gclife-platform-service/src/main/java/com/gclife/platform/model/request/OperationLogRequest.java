package com.gclife.platform.model.request;

import com.gclife.common.model.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/4/15
 */
@Getter
@Setter
@ApiModel(value = "操作日志请求", description = "操作日志请求")
public class OperationLogRequest extends BasePageRequest {
    @ApiModelProperty(value = "操作人ID", example = "操作人ID")
    private String operationUserId;
    @ApiModelProperty(value = "操作人", example = "操作人")
    private String operationUser;
    @ApiModelProperty(value = "操作开始时间", example = "操作开始时间")
    private String operationStartDate;
    @ApiModelProperty(value = "操作结束时间", example = "操作结束时间")
    private String operationEndDate;
    @ApiModelProperty(value = "操作角色ID", example = "操作角色ID")
    private String operationRoleId;

    private String    userId;
    private String    username;
    private String    serviceName;
    private String    traceId;
    private String    requestUrl;
    private String    requestNote;
    private Long      requestDate;
    private String    requestParam;
    private String    responseBody;
    private Integer   responseTime;
}

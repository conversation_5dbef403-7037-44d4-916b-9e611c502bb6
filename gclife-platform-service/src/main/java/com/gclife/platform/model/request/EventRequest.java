package com.gclife.platform.model.request;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * create 19-3-6
 * description:
 */
public class EventRequest {
    private String businessType;
    private String userId;
    //喜报
    private String insuranceName;
    private String teamLeader;
    private BigDecimal premium;
    private String productName;
    private Long approveDate;
    private String applicantName;
    //增员
    private String recommendAgentName;
    private String refereeAgentName;
    private Long time;
    //计划书
    private String customerName;
    private String productId;

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getInsuranceName() {
        return insuranceName;
    }

    public void setInsuranceName(String insuranceName) {
        this.insuranceName = insuranceName;
    }

    public String getTeamLeader() {
        return teamLeader;
    }

    public void setTeamLeader(String teamLeader) {
        this.teamLeader = teamLeader;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Long approveDate) {
        this.approveDate = approveDate;
    }

    public String getRecommendAgentName() {
        return recommendAgentName;
    }

    public void setRecommendAgentName(String recommendAgentName) {
        this.recommendAgentName = recommendAgentName;
    }

    public String getRefereeAgentName() {
        return refereeAgentName;
    }

    public void setRefereeAgentName(String refereeAgentName) {
        this.refereeAgentName = refereeAgentName;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }
}

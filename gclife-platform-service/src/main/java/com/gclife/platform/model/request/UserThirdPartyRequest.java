package com.gclife.platform.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2022-07-15
 */
@Data
public class UserThirdPartyRequest {
    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "绑定状态")
    private String bindStatus;
    @ApiModelProperty(example = "开放标识")
    private String openId;
    @ApiModelProperty(example = "第三方平台编码")
    private String thirdPartyCode;
    @ApiModelProperty(example = "appId")
    private String appId;
    @ApiModelProperty(example = "昵称")
    private String nickname;
    @ApiModelProperty(example = "手机号")
    private String mobile;
    @ApiModelProperty(example = "姓")
    private String familyName;
    @ApiModelProperty(example = "名")
    private String givenName;
    @ApiModelProperty(example = "性别")
    private String sex;
    @ApiModelProperty(example = "电子邮箱")
    private String email;
    @ApiModelProperty(example = "语言")
    private String language;
    @ApiModelProperty(example = "头像url")
    private String headImgUrl;
}

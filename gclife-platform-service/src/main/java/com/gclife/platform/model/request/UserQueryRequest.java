package com.gclife.platform.model.request;


import com.gclife.common.model.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by cqh on 17-9-5.
 * <AUTHOR>
 */
public class UserQueryRequest extends BasePageRequest {

    @ApiModelProperty(value = "账户",example = "caoqinghua")
    String username;

    @ApiModelProperty(value = "姓名",example = "caoqinghua")
    String name;

    @ApiModelProperty(value = "邮件",example = "<EMAIL>")
    String email;

    @ApiModelProperty(value = "性别",example = "MALE",hidden = true)
    String sex;

    @ApiModelProperty(value = "是否启用",example = "ENABLED",required = true)
    String enabled;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

}

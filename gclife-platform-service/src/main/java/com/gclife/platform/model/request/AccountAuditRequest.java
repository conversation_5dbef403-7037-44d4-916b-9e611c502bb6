package com.gclife.platform.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AccountAuditRequest {
    @ApiModelProperty(value = "账户Id", example = "*********")
    private String accountId;
    @ApiModelProperty(value = "审核结果", example = "通过:AUDIT_PASS;异常:AUDIT_FAILED")
    private String auditStatus;
    @ApiModelProperty(value = "常见问题", example = "*********")
    private String failureReasonCode;
    @ApiModelProperty(value = "备注信息", example = "*********")
    private String failureReason;
}

package com.gclife.platform.model.request;

import com.gclife.platform.core.jooq.tables.pojos.CustomerPo;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-13
 * description:
 */
public class UserCustomerRequest {
    @ApiModelProperty(value = "客户ID",example = "客户ID")
    private String customerId;
    @ApiModelProperty(value = "头像", example = "头像")
    private String avatar;
    @ApiModelProperty(value = "名称", example = "名称")
    private String name;
    @ApiModelProperty(value = "性别", example = "性别")
    private String sex;
    @ApiModelProperty(value = "出生年月日 时间戳", example = "出生年月日 时间戳")
    private Long birthday;
    @ApiModelProperty(value = "证件类型", example = "证件类型")
    private String idType;
    /**
     * 客户模块所有
     * 这里的  phone 对应 数据库的 mobile
     */
    @ApiModelProperty(value = "手机号", example = "手机号")
    private String phone;
    @ApiModelProperty(value = "证件号", example = "证件号")
    private String idNo;
    @ApiModelProperty(value = "邮箱", example = "邮箱")
    private String email;
    @ApiModelProperty(value = "详细地址", example = "详细地址")
    private String homeAddress;
    @ApiModelProperty(value = "区域", example = "区域")
    private String homeAreaCode;
    @ApiModelProperty(value = "微信号",example = "微信号")
    private String wechatNo;

    @ApiModelProperty(example = "脸书号",required = true)
    private String facebookNo;

    public String getWechatNo() {
        return wechatNo;
    }

    public void setWechatNo(String wechatNo) {
        this.wechatNo = wechatNo;
    }

    public String getFacebookNo() {
        return facebookNo;
    }

    public void setFacebookNo(String facebookNo) {
        this.facebookNo = facebookNo;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Long getBirthday() {
        return birthday;
    }

    public void setBirthday(Long birthday) {
        this.birthday = birthday;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getHomeAreaCode() {
        return homeAreaCode;
    }

    public void setHomeAreaCode(String homeAreaCode) {
        this.homeAreaCode = homeAreaCode;
    }
}
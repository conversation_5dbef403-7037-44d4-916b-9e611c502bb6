package com.gclife.platform.model.request;

import com.gclife.common.model.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * author deep
 * create 17-11-14
 * description
 */

public class UserAccountDetailRequest extends BasePageRequest {
    @ApiModelProperty(value = "金额",example = "金额")
    BigDecimal amount;

    @ApiModelProperty(value = "业务类型",example = "业务类型")
    String businessTypeCode;

    @ApiModelProperty(value = "流水类型",example = "流水类型")
    String streamCode;

    @ApiModelProperty(value = "流水状态",example = "流水状态")
    String status;

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getBusinessTypeCode() {
        return businessTypeCode;
    }

    public void setBusinessTypeCode(String businessTypeCode) {
        this.businessTypeCode = businessTypeCode;
    }

    public String getStreamCode() {
        return streamCode;
    }

    public void setStreamCode(String streamCode) {
        this.streamCode = streamCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
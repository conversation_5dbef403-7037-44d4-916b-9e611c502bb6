package com.gclife.platform.model.bo;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:
 */
public class AttendanceBo {
    /**
     * 签到时间
     */
    private  String attendanceDate;
    /**
     * 签到状态
     */
    private  String attendanceStatus;
    /**
     * 签到总天数
     */
    private Long continuouSignDays;

    public String getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(String attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public String getAttendanceStatus() {
        return attendanceStatus;
    }

    public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }

    public Long getContinuouSignDays() {
        return continuouSignDays;
    }

    public void setContinuouSignDays(Long continuouSignDays) {
        this.continuouSignDays = continuouSignDays;
    }
}
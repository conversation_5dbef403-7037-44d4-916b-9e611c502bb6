package com.gclife.platform.model.request;

import com.gclife.common.model.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *         create 17-11-9
 *         description:
 */
@Data
public class UserInfoRequest extends BaseRequest {
    @ApiModelProperty(value = "用户ID", example = "用户ID")
    private String userId;
    @ApiModelProperty(value = "性别", example = "MALE")
    private String gender;
    @ApiModelProperty(value = "电子邮箱", example = "<EMAIL>")
    private String email;
    @ApiModelProperty(value = "手机号码", example = "手机号码")
    private String mobile;
    @ApiModelProperty(value = "姓名", example = "robin")
    private String name;
    @ApiModelProperty(value = "别名", example = "robin")
    private String nikeName;
    @ApiModelProperty(value = "语言", example = "ZH_CN")
    private String language;
    @ApiModelProperty(value = "密码", example = "密码")
    private String password;
}
package com.gclife.platform.model.bo;

import com.gclife.common.util.StringUtil;
import com.gclife.platform.core.jooq.tables.Branch;
import com.gclife.platform.core.jooq.tables.pojos.BranchPo;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午9:17
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构
 * <AUTHOR>
 */
public class BranchLevelBo extends BranchPo {

    private Long level;

    private List<BranchLevelBo> childs;

    public List<BranchLevelBo> getChilds() {
        return childs;
    }

    public void setChilds(List<BranchLevelBo> childs) {
        this.childs = childs;
    }

    public Long getLevel() {
        return level;
    }

    public void setLevel(Long level) {
        this.level = level;
    }
}
package com.gclife.platform.model.query;

import com.gclife.common.model.BasePageRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/20/2022
 */
@Getter
@Setter
public class OperationLogQuery extends BasePageRequest {
    /**
     * 操作人ID
     */
    private String operationUserId;
    /**
     * 操作人
     */
    private String operationUser;
    /**
     * 操作开始时间
     */
    private String operationStartDate;
    /**
     * 操作结束时间
     */
    private String operationEndDate;
    /**
     * 操作角色ID
     */
    private String operationRoleId;
    /**
     * 操作人ID集合
     */
    private List<String> operationUserIds;

}

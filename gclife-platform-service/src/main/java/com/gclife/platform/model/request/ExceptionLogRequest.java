package com.gclife.platform.model.request;

import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

/**
 * @description: 异常日志请求
 * @author: chenjin<PERSON>
 * @create: 2021-08-26
 **/
@Getter
@Setter
public class ExceptionLogRequest {
    private String    userId;
    private String    username;
    private String    serviceName;
    private String    traceId;
    private String    requestUrl;
    private String    requestParam;
    private String    exceptionContent;

}

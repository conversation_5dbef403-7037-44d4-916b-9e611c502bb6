package com.gclife.platform.model.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * 微信用户保存请求对象
 */
public class UserWeixinRequest {

    @ApiModelProperty(value = "微信APPID", example = "gh_12333232")
    private String wechatAppId;
    @ApiModelProperty(value = "是否订阅", example = "MALE")
    private String subscribe;
    @ApiModelProperty(value = "openId", example = "MALE")
    private String openId;
    @ApiModelProperty(value = "别名", example = "MALE")
    private String nickname;
    @ApiModelProperty(value = "性别(MALE,FEMALE)", example = "MALE")
    private String sex;
    @ApiModelProperty(value = "语言", example = "MALE")
    private String language;
    @ApiModelProperty(value = "城市", example = "MALE")
    private String city;
    @ApiModelProperty(value = "省", example = "MALE")
    private String province;
    @ApiModelProperty(value = "国家", example = "MALE")
    private String country;
    @ApiModelProperty(value = "头像", example = "MALE")
    private String headImgUrl;
    @ApiModelProperty(value = "unionId", example = "MALE")
    private String unionId;
    @ApiModelProperty(value = "备注", example = "MALE")
    private String remark;
    @ApiModelProperty(value = "组ID", example = "MALE")
    private Integer groupId;
    @ApiModelProperty(value = "用户ID", example = "用户ID")
    private String userId;
    @ApiModelProperty(value = "用户机构ID", example = "用户机构ID")
    private String branchId;

    public String getSubscribe() {
        return subscribe;
    }

    public void setSubscribe(String subscribe) {
        this.subscribe = subscribe;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getHeadImgUrl() {
        return headImgUrl;
    }

    public void setHeadImgUrl(String headImgUrl) {
        this.headImgUrl = headImgUrl;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getWechatAppId() {
        return wechatAppId;
    }

    public void setWechatAppId(String wechatAppId) {
        this.wechatAppId = wechatAppId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }
}

package com.gclife.platform.model.request;

import com.gclife.common.model.BaseRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *         create 17-11-4
 *         description:
 */
public class UserRegisterRequest extends BaseRequest {
    @ApiModelProperty(value = "手机区号", example = "86", required = true)
    private String countryCode;
    @ApiModelProperty(value = "手机号码", example = "18898617356", required = true)
    private String mobile;

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
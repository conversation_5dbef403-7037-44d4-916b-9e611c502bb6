package com.gclife.platform.model.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-10
 * description:
 */
public class AgentAddressRequest {
    @ApiModelProperty(value = "用户地址ID", example = "000000001")
    private String userAddressId;
    @ApiModelProperty(value = "收件人", example = "罗斌")
    private String recipients;
    @ApiModelProperty(value = "联系电话", example = "18898617356")
    private String contactMobile;
    @ApiModelProperty(value = "地区", example = "440303")
    private String areaCode;
    @ApiModelProperty(value = "详细地址", example = "华南城5号馆")
    private String detailedAddress;
    @ApiModelProperty(value = "邮政编码", example = "518000")
    private String postalCode;
    @ApiModelProperty(value = "默认地址标识", example = "TRUE")
    private String defaultFlag;

    public String getUserAddressId() {
        return userAddressId;
    }

    public void setUserAddressId(String userAddressId) {
        this.userAddressId = userAddressId;
    }

    public String getRecipients() {
        return recipients;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getDetailedAddress() {
        return detailedAddress;
    }

    public void setDetailedAddress(String detailedAddress) {
        this.detailedAddress = detailedAddress;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getDefaultFlag() {
        return defaultFlag;
    }

    public void setDefaultFlag(String defaultFlag) {
        this.defaultFlag = defaultFlag;
    }
}
package com.gclife.platform.model.bo;

import com.gclife.platform.core.jooq.tables.pojos.AccountAuditPo;
import com.gclife.platform.core.jooq.tables.pojos.AccountPo;
import io.swagger.models.auth.In;
import lombok.Data;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:账户信息
 */
@Data
public class AccountBo extends AccountPo{
     private Integer totalLine;
     private AccountAuditPo accountAuditPo;
}
package com.gclife.platform.model.request;

import com.gclife.common.model.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-10
 * description:
 */
public class BranchPagingRequest extends BasePageRequest{
    @ApiModelProperty(value = "关键字", example = "关键字")
    private String keyword;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
}
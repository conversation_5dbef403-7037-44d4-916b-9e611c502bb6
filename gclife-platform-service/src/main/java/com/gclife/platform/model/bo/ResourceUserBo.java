package com.gclife.platform.model.bo;

import com.gclife.platform.core.jooq.tables.pojos.UsersPo;

/**
 * <AUTHOR>
 *         create 17-11-4
 *         description: 用户资源
 */
public class ResourceUserBo {

    private String userId;

    private String branchId;

    private String level;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }
}
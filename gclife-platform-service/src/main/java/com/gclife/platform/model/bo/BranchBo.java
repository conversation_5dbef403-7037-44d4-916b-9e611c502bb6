package com.gclife.platform.model.bo;

import com.gclife.common.util.StringUtil;
import com.gclife.platform.core.jooq.tables.pojos.BranchPo;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午9:17
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构
 * <AUTHOR>
 */
public class BranchBo extends BranchPo {

    /**默认渠道名称*/
    private String channelTypeName;
    /**渠道语言名称*/
    private String channelLTypeName;

    public String getChannelTypeName() {
        if(!StringUtil.isNullString(channelLTypeName)){
        return channelLTypeName;}
        else
        {  return channelTypeName;}
    }

    public void setChannelTypeName(String channelTypeName) {
        this.channelTypeName = channelTypeName;
    }

    public String getChannelLTypeName() {
        return channelLTypeName;
    }

    public void setChannelLTypeName(String channelLTypeName) {
        this.channelLTypeName = channelLTypeName;
    }

}
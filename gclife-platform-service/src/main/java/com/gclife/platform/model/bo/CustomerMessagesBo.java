package com.gclife.platform.model.bo;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
public class CustomerMessagesBo extends BaseQueryBo {
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 客户姓名
     */
    private String name;

    /**
     * 准客户
     */
    private String quasiCustomer;
    /**
     * 客户
     */
    private String customer;
    /**
     * 客户手机号
     */
    private String phone;

    /**
     * 受益人
     */
    private String beneficiary;
    /**
     * 被保人
     */
    private String insured;
    /**
     * 投保人
     */
    private String applicant;

    /**
     * 分组编码
     */
    private String groupCode;

    /**
     * 手机号
     */
    private String mobile;


    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getBeneficiary() {
        return beneficiary;
    }

    public void setBeneficiary(String beneficiary) {
        this.beneficiary = beneficiary;
    }

    public String getInsured() {
        return insured;
    }

    public void setInsured(String insured) {
        this.insured = insured;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getQuasiCustomer() {
        return quasiCustomer;
    }

    public void setQuasiCustomer(String quasiCustomer) {
        this.quasiCustomer = quasiCustomer;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }
}
package com.gclife.platform.model.bo;

import com.gclife.common.util.StringUtil;
import com.gclife.platform.core.jooq.tables.pojos.BranchPo;
import io.swagger.annotations.ApiModelProperty;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午9:17
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 国际化语言bo
 * <AUTHOR>
 */
public class SyscodeBo {
    /**key值*/
    private String  codeKey;
    /**默认名称*/
    private String  codeName;
    /**语言*/
    private String  language;
    /**语言名称*/
    private String  codeLName;
    /**详细描述*/
    private String describe;
    /**详细描述*/
    private String describeL;
    /**详细描述*/
    private String symbol;

    public String getCodeKey() {
        return codeKey;
    }

    public void setCodeKey(String codeKey) {
        this.codeKey = codeKey;
    }

    public String getCodeName() {
        if(!StringUtil.isNullString(codeLName))
        {
            return codeLName;
        }
        else {
            return codeName;
        }
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCodeLName() {
        return codeLName;
    }

    public void setCodeLName(String codeLName) {
        this.codeLName = codeLName;
    }

    public String getDescribe() {
        if(!StringUtil.isNullString(describeL))
        {
            return describeL;
        }
        else {
            return describe;
        }
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getDescribeL() {
        return describeL;
    }

    public void setDescribeL(String describeL) {
        this.describeL = describeL;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
}
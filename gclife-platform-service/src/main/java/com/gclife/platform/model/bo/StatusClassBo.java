package com.gclife.platform.model.bo;

import com.gclife.common.util.StringUtil;

public class StatusClassBo {
    /**key值*/
    private String  statusClassCode;
    /**默认名称*/
    private String  statusClassName;
    /**语言*/
    private String  language;
    /**语言名称*/
    private String  codeName;
    /**排序*/
    private String  codeIndex;

    public String getStatusClassCode() {
        return statusClassCode;
    }

    public void setStatusClassCode(String statusClassCode) {
        this.statusClassCode = statusClassCode;
    }

    public String getStatusClassName() {
        if(!StringUtil.isNullString(codeName))
        {
            return codeName;
        }
        else {
            return statusClassName;
        }
    }

    public void setStatusClassName(String statusClassName) {
        this.statusClassName = statusClassName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public String getCodeIndex() {
        return codeIndex;
    }

    public void setCodeIndex(String codeIndex) {
        this.codeIndex = codeIndex;
    }
}

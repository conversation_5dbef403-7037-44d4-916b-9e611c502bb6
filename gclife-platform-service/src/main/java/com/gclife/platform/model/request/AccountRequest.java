package com.gclife.platform.model.request;

import com.gclife.common.model.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:
 */
@Data
public class AccountRequest extends BaseRequest {
    @ApiModelProperty(value = "账户Id", example = "*********")
    private String accountId;
    @ApiModelProperty(value = "账户用途类: I付费，O退费", example = "I")
    private String useType;
    @ApiModelProperty(value = "账户号码", example = "*********")
    private String accountNo;
    @ApiModelProperty(value = "银行代码", example = "1")
    private String bankCode;
    @ApiModelProperty(value = "支行名称", example = "")
    private String subbranch;
    @ApiModelProperty(value = "账户持有人", example = "龙少爷")
    private String accountOwner;
    @ApiModelProperty(value = "账户持有人证件类型", example = "身份证")
    private String idType;
    @ApiModelProperty(value = "账户持有人证件号码", example = "430521198888887536")
    private String idNo;
    @ApiModelProperty(value = "账户类型【活期储蓄账户/借记卡】", example = "借记卡")
    private String accountType;
    @ApiModelProperty(value = "账户持有人签名是否一致", example = "YES")
    private String acctuserSignStatus;
    @ApiModelProperty(value = "授权日期", example = "**********")
    private Long authorizedDate;
    @ApiModelProperty(example = "开户行所在地区编码")
    private String areaCode;
    @ApiModelProperty(example = "主附卡标识")
    private String primaryFlag;
    @ApiModelProperty(value = "账户所属用户ID", example = "账户所属用户ID")
    private String userId;
    @ApiModelProperty(example = "银行卡附件正面")
    private String bankFrontAttachId;
    @ApiModelProperty(example = "银行卡附件反面")
    private String bankBackAttachId;

}
package com.gclife.platform.model.request;

import com.gclife.common.model.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-14
 * description:
 */
public class CustomerMessagesRequest extends BasePageRequest {

    @ApiModelProperty(value = "客户类型",example = "勋章编码 APPLY投保编码  INSURED 被保人编码  BENEFICIARY 受益人编码 QUASI_CUSTOMER 准客户 CUSTOMER 客户")
    private String medalNo;

    @ApiModelProperty(value = "客户名称",example = "客户名称")
    private String name;

    @ApiModelProperty(value = "关键字",example = "关键字")
    private String keyword;

    public String getMedalNo() {
        return medalNo;
    }

    public void setMedalNo(String medalNo) {
        this.medalNo = medalNo;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
package com.gclife.platform.model.request;


import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BaseRequest;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by cqh on 17-9-5.
 * <AUTHOR>
 */
public class TerminologyRequest extends BaseRequest {

    //术语类型
    @ApiModelProperty(value = "类型",  example = "ERROR_QUESTION",required = true)
    private String type;
    @ApiModelProperty(value = "属于编码集合",example = "",required = true)
    private List<String> listSyscodes=null;
    /***语言*/
    private String language;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getListSyscodes() {
        return listSyscodes;
    }

    public void setListSyscodes(List<String> listSyscodes) {
        this.listSyscodes = listSyscodes;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}

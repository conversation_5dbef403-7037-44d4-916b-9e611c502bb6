package com.gclife.platform.model.bo;


import com.gclife.platform.core.jooq.tables.pojos.PermissionsPo;
import com.gclife.platform.core.jooq.tables.records.PermissionsRecord;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 上午11:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 权限业务对象
 * <AUTHOR>
 */
public class PermissionsBo extends PermissionsPo {


    private List<ResourceBo> listResource;

    public List<ResourceBo> getListResource() {
        return listResource;
    }

    public void setListResource(List<ResourceBo> listResource) {
        this.listResource = listResource;
    }

}
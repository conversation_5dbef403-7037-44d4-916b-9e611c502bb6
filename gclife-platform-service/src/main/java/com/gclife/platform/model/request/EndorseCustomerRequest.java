package com.gclife.platform.model.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 18-9-11
 * description:
 */
public class EndorseCustomerRequest {
    @ApiModelProperty(example = "客户id")
    private String customerId;
    @ApiModelProperty(example = "客户号")
    private String customerNo;
    @ApiModelProperty(example = "家庭固定电话")
    private String homePhone;
    @ApiModelProperty(example = "家庭地址")
    private String homeAddress;
    @ApiModelProperty(example = "家庭邮编")
    private String homeZipCode;
    @ApiModelProperty(example = "客户主键ID")
    private String email;
    @ApiModelProperty(example = "邮件地址")
    private String mobile;
    @ApiModelProperty(example = "家庭地址地区编码")
    private String homeAreaCode;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getHomePhone() {
        return homePhone;
    }

    public void setHomePhone(String homePhone) {
        this.homePhone = homePhone;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getHomeZipCode() {
        return homeZipCode;
    }

    public void setHomeZipCode(String homeZipCode) {
        this.homeZipCode = homeZipCode;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getHomeAreaCode() {
        return homeAreaCode;
    }

    public void setHomeAreaCode(String homeAreaCode) {
        this.homeAreaCode = homeAreaCode;
    }
}

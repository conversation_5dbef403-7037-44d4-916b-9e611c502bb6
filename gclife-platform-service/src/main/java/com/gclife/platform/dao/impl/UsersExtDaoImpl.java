package com.gclife.platform.dao.impl;


import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.Cached;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.base.Resources;
import com.gclife.common.model.config.AuthItemConfigEnum;
import com.gclife.common.model.config.BaseConfigEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.core.jooq.tables.Employee;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.dao.UsersExtDao;
import com.gclife.platform.model.bo.*;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.request.UserQueryRequest;
import com.gclife.platform.model.response.workflow.UsersWfResponse;
import com.gclife.platform.validate.transfer.MenuResourceTransfer;
import org.jooq.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.gclife.platform.core.jooq.Tables.EMPLOYEE;
import static com.gclife.platform.core.jooq.Tables.USER_LOGIN;
import static com.gclife.platform.core.jooq.tables.Permissions.PERMISSIONS;
import static com.gclife.platform.core.jooq.tables.Permissions2roles.PERMISSIONS2ROLES;
import static com.gclife.platform.core.jooq.tables.Resources.RESOURCES;
import static com.gclife.platform.core.jooq.tables.Resources2permissions.RESOURCES2PERMISSIONS;
import static com.gclife.platform.core.jooq.tables.ResourcesRelationship.RESOURCES_RELATIONSHIP;
import static com.gclife.platform.core.jooq.tables.Roles.ROLES;
import static com.gclife.platform.core.jooq.tables.UserBranch.USER_BRANCH;
import static com.gclife.platform.core.jooq.tables.Users.USERS;
import static com.gclife.platform.core.jooq.tables.Users2roles.USERS2ROLES;
import static java.util.stream.Collectors.toList;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午1:57
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */

@Component
public class UsersExtDaoImpl extends BaseDaoImpl implements UsersExtDao {


    @Autowired
    private MenuResourceTransfer menuResourceTransfer;

    /**
     * 查询用户授权业务对象 层级关系嵌套
     *
     * @param userId
     * @return
     */
    @Override
    public UsersOauthBo loadUserDetailById(String userId) {
        UsersOauthBo usersOauthBo = null;
        try {
            List<UsersOauthBo> users = new ArrayList<>();
            Map<String, List<RolesBo>> roles = new HashMap<>(10);
            Map<String, List<PermissionsBo>> permissions = new HashMap<>(10);
            Map<String, List<ResourceBo>> resources = new HashMap<>(10);

            this.getDslContext()
                    .select(USERS.fields())
                    .select(ROLES.fields())
                    .select(PERMISSIONS.fields())
                    .select(RESOURCES.fields())
                    .from(USERS)
                    .leftJoin(USERS2ROLES).on(USERS2ROLES.USER_ID.eq(USERS.USER_ID))
                    .leftJoin(ROLES).on(ROLES.ROLE_ID.eq(USERS2ROLES.ROLE_ID))
                    .leftJoin(PERMISSIONS2ROLES).on(PERMISSIONS2ROLES.ROLE_ID.eq(ROLES.ROLE_ID))
                    .leftJoin(PERMISSIONS).on(PERMISSIONS.PERMISSION_ID.eq(PERMISSIONS2ROLES.PERMISSION_ID))
                    .leftJoin(RESOURCES2PERMISSIONS).on(RESOURCES2PERMISSIONS.PERMISSION_ID.eq(PERMISSIONS.PERMISSION_ID))
                    .leftJoin(RESOURCES).on(RESOURCES.RESOURCE_ID.eq(RESOURCES2PERMISSIONS.RESOURCE_ID))
                    .where(USERS.USER_ID.eq(userId))
                    .fetch()
                    .map(record -> {
                        //获取用户对象
                        UsersOauthBo usersBo = null;
                        //用户
                        if (!users.contains(usersBo)) {
                            users.add(usersBo);
                        }

                        return null;
                    });

            if (users != null && users.size() > 0) {
                usersOauthBo = users.get(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR.getValue());
            new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR);
        }
        return usersOauthBo;
    }


    /**
     * 查询单个对象
     *
     * @param userId
     * @return
     */
    @Override
    public UsersPo loadUserPo(String userId) {
        UsersPo usersPo = null;
        try {
            usersPo = this.getDslContext()
                    .select(USERS.fields())
                    .from(USERS)
                    .where(USERS.USER_ID.eq(userId))
                    .fetchOneInto(UsersPo.class);

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return usersPo;
    }

    @Override
    public List<UsersBo> loadUserDetailBos(List<String> userIds) {
        List<UsersBo> usersBos = new ArrayList<>();
        try {
            usersBos = this.getDslContext()
                    .select(USERS.fields())
                    .select(USER_LOGIN.LOGIN_LAST.as("loginLast"),USER_LOGIN.LOGIN_COUNT)
                    .from(USERS)
                    .leftJoin(USER_LOGIN).on(USERS.USER_ID.eq(USER_LOGIN.USER_ID))
                    .where(USERS.USER_ID.in(userIds))
                    .fetchInto(UsersBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return usersBos;
    }

    @Override
    public List<UsersBo> loadUserBos(List<String> userIds) {
        List<UsersBo> usersBos = new ArrayList<>();
        try {
            usersBos = this.getDslContext()
                    .select(USERS.fields())
                    .select(EMPLOYEE.BRANCH_ID)
                    .from(USERS)
                    .leftJoin(EMPLOYEE).on(USERS.USER_ID.eq(EMPLOYEE.USER_ID))
                    .where(USERS.USER_ID.in(userIds))
                    .fetchInto(UsersBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return usersBos;
    }

    /**
     * 根据用户账户名查询用户信息
     *
     * @param username
     * @return
     */
    @Override
    public UsersPo loadUserByUsersName(String username) {
        try {
            return this.getDslContext()
                    .select(USERS.fields())
                    .from(USERS)
                    .where(USERS.USERNAME.eq(username))
                    .fetchOneInto(UsersPo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return null;
    }


    /**
     * 查询用户列表,分页查询
     *
     * @param userQueryRequest
     * @return
     */
    @Override
    public List<UsersPo> loadUserList(UserQueryRequest userQueryRequest) {
        List<UsersPo> usersPoList = null;
        try {
            SelectJoinStep selectOnStep = this.getDslContext()
                    .select(USERS.fields())
                    .from(USERS);
            /**
             * 动态添加条件
             */
            if (AssertUtils.isNotEmpty(userQueryRequest.getName())) {
                selectOnStep.where(USERS.NAME.like(userQueryRequest.getName()));
            }

            /**
             * 设置分页参数
             */
            selectOnStep.offset(userQueryRequest.getOffset())
                    .limit(userQueryRequest.getPageSize());

            //打印sql
            System.err.println(selectOnStep.getSQL());

            usersPoList = selectOnStep.fetchInto(UsersPo.class);

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return usersPoList;
    }

    /**
     * 查询用户角色集合
     *
     * @param userId
     * @return
     */
    @Override
    public List<RolesBo> loadUserRoles(String userId) {
        List<RolesBo> rolesBos = null;
        try {
            rolesBos = this.getDslContext()
                    .select(ROLES.fields())
                    .from(USERS2ROLES)
                    .innerJoin(ROLES).on(ROLES.ROLE_ID.eq(USERS2ROLES.ROLE_ID))
                    .innerJoin(USERS).on(USERS.USER_ID.eq(USERS2ROLES.USER_ID))
                    .where(USERS.USER_ID.eq(userId))
                    .fetchInto(RolesBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return rolesBos;
    }

    /**
     * 　根据销售机构ID和活动节点
     * 　查询系统所有用户管理的机构　在当前销售机构树往上找的树上
     *
     * @param activityAuid 活动节点
     * @param branchId     机构ID(销售机构)
     * @return
     */
    @Override
    public UsersWfResponse loadWorkflowUser(String activityAuid, String branchId) {
        UsersWfResponse usersWfResponses = new UsersWfResponse();
        try {

            String sql = "SELECT DISTINCT u.user_id\n" +
                    "FROM users u\n" +
                    "  INNER JOIN users2roles u2r ON u2r.user_id = u.user_id\n" +
                    "  INNER JOIN roles r ON r.role_id = u2r.role_id\n" +
                    "  INNER JOIN permissions2roles p2r ON p2r.role_id = r.role_id\n" +
                    "  INNER JOIN permissions p ON p.permission_id = p2r.permission_id\n" +
                    "  INNER JOIN resources2permissions r2p ON r2p.permission_id = p.permission_id\n" +
                    "  INNER JOIN resources r2 ON r2.resource_id = r2p.resource_id\n" +
                    "  INNER JOIN user_branch e ON e.user_id = u.user_id\n" +
                    "WHERE r2.code = '" + activityAuid + "'\n";
            if (AssertUtils.isNotEmpty(branchId)) {
                sql = sql + "   AND exists(\n" +
                        "        WITH RECURSIVE T (branch_id, parent_branch_id) AS (\n" +
                        "                SELECT  branch_id,parent_branch_id  FROM branch WHERE branch_id ='" + branchId + "'\n" +
                        "                UNION ALL\n" +
                        "                SELECT  D.branch_id,D.parent_branch_id  FROM branch D\n" +
                        "                  JOIN T ON D.branch_id = T.parent_branch_id\n" +
                        "            )   SELECT DISTINCT branch_id, parent_branch_id FROM T\n" +
                        "           WHERE T.branch_id<>'ROOT' AND e.branch_id=T.branch_id\n" +
                        "      );";
            }
            System.out.println(sql);
            List<String> strings = this.getDslContext().fetch(sql).getValues(USERS.USER_ID, String.class);
            usersWfResponses.setUserId(strings);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR);
        }
        return usersWfResponses;
    }

    /**
     * 获取资源所属的用户列表
     *
     * @param resourceCode 活动节点
     * @param branchId     机构ID
     * @return
     */
    @Override
    public List<ResourceUserBo> loadResourceUserList(String resourceCode, String branchId) {
        try {
            String sql = "SELECT DISTINCT u.user_id,e.branch_id\n" +
                    "FROM users u\n" +
                    "  INNER JOIN users2roles u2r ON u2r.user_id = u.user_id\n" +
                    "  INNER JOIN roles r ON r.role_id = u2r.role_id\n" +
                    "  INNER JOIN permissions2roles p2r ON p2r.role_id = r.role_id\n" +
                    "  INNER JOIN permissions p ON p.permission_id = p2r.permission_id\n" +
                    "  INNER JOIN resources2permissions r2p ON r2p.permission_id = p.permission_id\n" +
                    "  INNER JOIN resources r2 ON r2.resource_id = r2p.resource_id\n" +
                    "  INNER JOIN user_branch e ON e.user_id = u.user_id\n" +
                    "WHERE r2.code = '" + resourceCode + "'\n";
            if (AssertUtils.isNotEmpty(branchId)) {
                sql = sql + "   AND exists(\n" +
                        "        WITH RECURSIVE T (branch_id, parent_branch_id) AS (\n" +
                        "                SELECT  branch_id,parent_branch_id  FROM branch WHERE branch_id ='" + branchId + "'\n" +
                        "                UNION ALL\n" +
                        "                SELECT  D.branch_id,D.parent_branch_id  FROM branch D\n" +
                        "                  JOIN T ON D.branch_id = T.parent_branch_id\n" +
                        "            )   SELECT DISTINCT branch_id, parent_branch_id FROM T\n" +
                        "           WHERE T.branch_id<>'ROOT' AND e.branch_id=T.branch_id\n" +
                        "      );";
            }
            System.out.println(sql);
            List<ResourceUserBo> resourceUserBoList = this.getDslContext().fetch(sql).stream().map(record -> {
                ResourceUserBo resourceUserBo = new ResourceUserBo();
                resourceUserBo.setUserId(record.getValue(USERS.USER_ID, String.class));
                resourceUserBo.setBranchId(record.getValue(USER_BRANCH.BRANCH_ID, String.class));
                return resourceUserBo;
            }).collect(Collectors.toList());
            return resourceUserBoList;
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR);
        }
    }

    /**
     * 获取业务对应的用户列表
     *
     * @param businessCode 业务编码
     * @param branchId     机构ID
     * @return
     */
    @Override
    public List<BusinessUserBo> loadBusinessUserList(String businessCode, String branchId, String branchMode) {
        try {
            String sql = "";
            if (!AssertUtils.isNotEmpty(branchMode) || PlatformTermEnum.BRANCH_MODE.SALES.name().equals(branchMode)) {
                sql = "SELECT DISTINCT u.user_id,e.branch_id\n" +
                        "FROM users u\n" +
                        "  INNER JOIN users2roles u2r ON u2r.user_id = u.user_id\n" +
                        "  INNER JOIN roles r ON r.role_id = u2r.role_id\n" +
                        "  INNER JOIN role_business rb ON rb.role_id = r.role_id\n" +
                        "  INNER JOIN user_branch e ON e.user_id = u.user_id\n" +
                        "  INNER JOIN employee e2 ON e2.user_id = u.user_id\n" +
                        "WHERE rb.business_code = '" + businessCode + "'\n" +
                        "AND e2.employe_status_code!='" + TerminologyConfigEnum.AGENT_STATUS.DRAG.name() + "'\n";
                if (AssertUtils.isNotEmpty(branchId)) {
                    sql = sql + "   AND exists(\n" +
                            "        WITH RECURSIVE T (branch_id, parent_branch_id) AS (\n" +
                            "                SELECT  branch_id,parent_branch_id  FROM branch WHERE branch_id ='" + branchId + "'\n" +
                            "                UNION ALL\n" +
                            "                SELECT  D.branch_id,D.parent_branch_id  FROM branch D\n" +
                            "                  JOIN T ON D.branch_id = T.parent_branch_id\n" +
                            "            )   SELECT DISTINCT branch_id, parent_branch_id FROM T\n" +
                            "           WHERE T.branch_id<>'ROOT' AND e.branch_id=T.branch_id\n" +
                            "      );";
                }
            } else {
                sql = "SELECT DISTINCT u.user_id,e.branch_id\n" +
                        "FROM users u\n" +
                        "  INNER JOIN users2roles u2r ON u2r.user_id = u.user_id\n" +
                        "  INNER JOIN roles r ON r.role_id = u2r.role_id\n" +
                        "  INNER JOIN role_business rb ON rb.role_id = r.role_id\n" +
                        "  INNER JOIN employee e ON e.user_id = u.user_id\n" +
                        "WHERE rb.business_code = '" + businessCode + "'\n" +
                        "AND e.employe_status_code!='" + TerminologyConfigEnum.AGENT_STATUS.DRAG.name() + "'\n";
                if (AssertUtils.isNotEmpty(branchId)) {
                    sql = sql + "   AND exists(\n" +
                            "        WITH RECURSIVE T (branch_id, parent_branch_id) AS (\n" +
                            "                SELECT  branch_id,parent_branch_id  FROM branch WHERE branch_id ='" + branchId + "'\n" +
                            "                UNION ALL\n" +
                            "                SELECT  D.branch_id,D.parent_branch_id  FROM branch D\n" +
                            "                  JOIN T ON D.branch_id = T.parent_branch_id\n" +
                            "            )   SELECT DISTINCT branch_id, parent_branch_id FROM T\n" +
                            "           WHERE T.branch_id<>'ROOT' AND e.branch_id=T.branch_id\n" +
                            "      );";
                }
            }

            System.out.println(sql);

            List<BusinessUserBo> businessUserBoList = this.getDslContext().fetch(sql).stream().map(record -> {
                BusinessUserBo businessUserBo = new BusinessUserBo();
                businessUserBo.setUserId(record.getValue(USERS.USER_ID, String.class));
                businessUserBo.setBranchId(record.getValue(USER_BRANCH.BRANCH_ID, String.class));
                return businessUserBo;
            }).collect(Collectors.toList());
            return businessUserBoList;
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR);
        }
    }

    @Override
    public List<BusinessUserBo> loadBusinessUserListNew(String businessCode) {
        try {
            String sql = "";

            sql = "SELECT DISTINCT u.user_id,e.branch_id\n" +
                    "FROM users u\n" +
                    "  INNER JOIN users2roles u2r ON u2r.user_id = u.user_id\n" +
                    "  INNER JOIN roles r ON r.role_id = u2r.role_id\n" +
                    "  INNER JOIN role_business rb ON rb.role_id = r.role_id\n" +
                    "  INNER JOIN user_branch e ON e.user_id = u.user_id\n" +
                    "  INNER JOIN employee e2 ON e2.user_id = u.user_id\n" +
                    "WHERE rb.business_code = '" + businessCode + "'\n" +
                    "AND e2.employe_status_code!='" + TerminologyConfigEnum.AGENT_STATUS.DRAG.name() + "'\n";


            System.out.println(sql);

            List<BusinessUserBo> businessUserBoList = this.getDslContext().fetch(sql).stream().map(record -> {
                BusinessUserBo businessUserBo = new BusinessUserBo();
                businessUserBo.setUserId(record.getValue(USERS.USER_ID, String.class));
                businessUserBo.setBranchId(record.getValue(USER_BRANCH.BRANCH_ID, String.class));
                return businessUserBo;
            }).collect(Collectors.toList());
            return businessUserBoList;
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR);
        }
    }

    @Override
    public UsersBo loadMobileVerify(String countryCode, String mobile, String deviceChannel) {
        UsersBo usersBo;
        SelectJoinStep selectJoinStep =
                this.getDslContext()
                        .select(USERS.fields())
                        .from(USERS);
        List<Condition> conditions = new ArrayList<>();
        if (AssertUtils.isNotEmpty(countryCode) && !AssertUtils.isEmail(mobile)) {
            conditions.add(USERS.COUNTRY_CODE.eq(countryCode));
        }
        conditions.add(USERS.MOBILE.eq(mobile).or(USERS.EMAIL.eq(mobile)));
        conditions.add(USERS.DEVICE_CHANNEL_ID.eq(deviceChannel));
        selectJoinStep.where(conditions);
        usersBo = (UsersBo) selectJoinStep.fetchOneInto(UsersBo.class);
//        System.out.println(selectJoinStep.toString());
        return usersBo;
    }

    @Override
    @Cached(name = "loadFrontendResourcesList", key = "#userId + #language", expire = 240, timeUnit = TimeUnit.SECONDS)
    @CacheRefresh(refresh = 120, stopRefreshAfterLastAccess = 240, timeUnit = TimeUnit.SECONDS)
    @CachePenetrationProtect
    public List<MenuResourcesBo> loadFrontendResourcesList(String userId, String language) {
        // 先查询权限ID
        List<String> permissionIdList = this.getDslContext().selectDistinct(PERMISSIONS.PERMISSION_ID)
                .from(ROLES)
                .innerJoin(USERS2ROLES)
                .on(ROLES.ROLE_ID.eq(USERS2ROLES.ROLE_ID))
                .innerJoin(PERMISSIONS2ROLES)
                .on(ROLES.ROLE_ID.eq(PERMISSIONS2ROLES.ROLE_ID))
                .innerJoin(PERMISSIONS)
                .on(PERMISSIONS.PERMISSION_ID.eq(PERMISSIONS2ROLES.PERMISSION_ID))
                .where(USERS2ROLES.USER_ID.eq(userId))
                .fetchInto(String.class);

        // 查询能访问的前端url
        String sql = "  SELECT distinct " +
                "    a.*, " +
                "    CASE " +
                "    WHEN b.resource_id IS NULL " +
                "      THEN CAST('root' AS VARCHAR) " +
                "    ELSE b.resource_id " +
                "    END " +
                "      AS parentResourceId, " +
                " d.value " +
                "  FROM resources a LEFT JOIN resources_relationship b " +
                "      ON (a.resource_id = b.additional_resource_id AND b.relationship = '" +
                AuthItemConfigEnum.RELATIONSHIP_PARENT.getValue() +
                "') " +
                "    LEFT JOIN resources2permissions c ON (a.resource_id = c.resource_id) " +
                " LEFT JOIN base_international_text d on ( a.code = d.key and d.type = '" +
                AuthItemConfigEnum.RESOURCE.getValue() + "'" +
                " and d.language = '" + language + "'" + ")" +
                "where a.type != '" +
                AuthItemConfigEnum.BACKEND_URL.getValue() + "'";
        String additional;
        StringBuilder permissionsSb = new StringBuilder("(");
        permissionIdList.forEach(a -> {
            permissionsSb.append("'")
                    .append(a)
                    .append("'")
                    .append(",");
        });
        permissionsSb.deleteCharAt(permissionsSb.length() - 1);
        permissionsSb.append(")");

        if (permissionIdList.size() > 0) {
            additional = " and (c.permission_id in " + permissionsSb.toString() +
                    " or a.access_control_type = '" +
                    AuthItemConfigEnum.NO_ACCESS_CONTROL.getValue() +
                    "')";
        } else {
            additional = " and (1 = 0 " +
                    " or a.access_control_type = '" +
                    AuthItemConfigEnum.NO_ACCESS_CONTROL.getValue() +
                    "')";
        }
        additional += " order by a.type asc, a.index asc";
        Result<Record> noBackendRecords = this.getDslContext().fetch(sql + additional);
        List<MenuResourcesBo> resourcesList = new ArrayList<>();

        noBackendRecords.forEach(a -> {
            resourcesList.add(menuResourceTransfer.transMenuResourcesBo(a));
        });

        return resourcesList;
    }

    @Override
    public List<String> queryWorkflowNodePermissionUsers(String activityAuid, String branchId, String branchMode) {
        List<String> userIds = null;
        try {
            if (!AssertUtils.isNotEmpty(branchMode) || PlatformTermEnum.BRANCH_MODE.SALES.name().equals(branchMode)) {
                String sql = "SELECT DISTINCT u.user_id\n" +
                        "FROM users u\n" +
                        "  INNER JOIN users2roles u2r ON u2r.user_id = u.user_id\n" +
                        "  INNER JOIN roles r ON r.role_id = u2r.role_id\n" +
                        "  INNER JOIN permissions2roles p2r ON p2r.role_id = r.role_id\n" +
                        "  INNER JOIN permissions p ON p.permission_id = p2r.permission_id\n" +
                        "  INNER JOIN resources2permissions r2p ON r2p.permission_id = p.permission_id\n" +
                        "  INNER JOIN resources r2 ON r2.resource_id = r2p.resource_id\n" +
                        "  INNER JOIN user_branch e ON e.user_id = u.user_id\n" +
                        "WHERE r2.code = '" + activityAuid + "'\n";
                if (AssertUtils.isNotEmpty(branchId)) {
                    sql = sql + "   AND exists(\n" +
                            "        WITH RECURSIVE T (branch_id, parent_branch_id) AS (\n" +
                            "                SELECT  branch_id,parent_branch_id  FROM branch WHERE branch_id ='" + branchId + "'\n" +
                            "                UNION ALL\n" +
                            "                SELECT  D.branch_id,D.parent_branch_id  FROM branch D\n" +
                            "                  JOIN T ON D.branch_id = T.parent_branch_id\n" +
                            "            )   SELECT DISTINCT branch_id, parent_branch_id FROM T\n" +
                            "           WHERE T.branch_id<>'ROOT' AND e.branch_id=T.branch_id\n" +
                            "      );";
                }
                System.out.println(sql);
                userIds = this.getDslContext().fetch(sql).getValues(USERS.USER_ID, String.class);
            } else if (PlatformTermEnum.BRANCH_MODE.MANAGER.name().equals(branchMode)) {
                String sql = "SELECT DISTINCT u.user_id\n" +
                        "  FROM users u\n" +
                        "    INNER JOIN users2roles u2r ON u2r.user_id = u.user_id\n" +
                        "    INNER JOIN roles r ON r.role_id = u2r.role_id\n" +
                        "    INNER JOIN permissions2roles p2r ON p2r.role_id = r.role_id\n" +
                        "    INNER JOIN permissions p ON p.permission_id = p2r.permission_id\n" +
                        "    INNER JOIN resources2permissions r2p ON r2p.permission_id = p.permission_id\n" +
                        "    INNER JOIN resources r2 ON r2.resource_id = r2p.resource_id\n" +
                        "    INNER JOIN employee e on e.user_id= u.user_id\n" +
                        "  WHERE r2.code ='" + activityAuid + "'";
                if (AssertUtils.isNotEmpty(branchId)) {
                    sql = sql + " and  exists (\n" +
                            "    WITH RECURSIVE T (branch_id, parent_branch_id) AS (\n" +
                            "                                            SELECT  branch_id,parent_branch_id  FROM branch WHERE branch_id ='" + branchId + "'\n" +
                            "                                            UNION ALL\n" +
                            "                                            SELECT  D.branch_id,D.parent_branch_id  FROM branch D\n" +
                            "                                              JOIN T ON D.branch_id = T.parent_branch_id\n" +
                            "                                        )   SELECT DISTINCT branch_id, parent_branch_id FROM T\n" +
                            "                                       WHERE T.branch_id<>'ROOT' AND e.branch_id=T.branch_id\n" +
                            "  );";
                }
                System.out.println(sql);
                userIds = this.getDslContext().fetch(sql).getValues(USERS.USER_ID, String.class);
            }

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USER_LOADUSERAUTHS_ERROR);
        }
        return userIds;
    }

    @Override
    @Cached(name = "loadBackendResourcesList", key = "#userId", expire = 240, timeUnit = TimeUnit.SECONDS)
    @CacheRefresh(refresh = 120, stopRefreshAfterLastAccess = 240, timeUnit = TimeUnit.SECONDS)
    @CachePenetrationProtect
    public List<MenuResourcesBo> loadBackendResourcesList(String userId) {
        // 先获取NO_ACCESS_CONTROL的后台资源
        SelectOrderByStep noAccessControlRes = this.getDslContext()
                .select(RESOURCES.fields())
                .from(RESOURCES)
                .where(RESOURCES.ACCESS_CONTROL_TYPE.eq(AuthItemConfigEnum.NO_ACCESS_CONTROL.getValue()))
                .and(RESOURCES.TYPE.eq(AuthItemConfigEnum.BACKEND_URL.getValue()));


        // 先获取前端资源，再根据前端资源去查询
        List<MenuResourcesBo> frontEndRes = loadFrontendResourcesList(userId, PlatformTermEnum.LANGUAGE_TYPE.ZH_CN.code());
        List<String> parentResourceIds = new ArrayList<>();
        if (AssertUtils.isNotEmpty(frontEndRes)) {
            parentResourceIds = frontEndRes.stream().filter(a ->
                            a.getType().equals(AuthItemConfigEnum.FRONTEND_URL.getValue()) ||
                                    a.getType().equals(AuthItemConfigEnum.FRONTEND_BUTTON.getValue()))
                    .collect(toList())
                    .stream()
                    .map(a -> a.getResourceId())
                    .collect(toList());
        }
        SelectOrderByStep allSelect;
        if (parentResourceIds.size() > 0) {
            allSelect = noAccessControlRes
                    .union(this.getDslContext()
                            .selectDistinct(RESOURCES.fields())
                            .from(RESOURCES)
                            .innerJoin(RESOURCES_RELATIONSHIP)
                            .on(RESOURCES.RESOURCE_ID.eq(RESOURCES_RELATIONSHIP.ADDITIONAL_RESOURCE_ID))
                            .and(RESOURCES.TYPE.eq(AuthItemConfigEnum.BACKEND_URL.getValue()))
                            .and(RESOURCES_RELATIONSHIP.RESOURCE_ID.in(parentResourceIds)));
        } else {
            allSelect = noAccessControlRes;
        }
        List<MenuResourcesBo> resourcesList = allSelect.fetchInto(MenuResourcesBo.class);

        this.getLogger().info(allSelect.toString());

        return resourcesList;
    }

    @Override
    public List<UsersPo> loadTemporarilyOverTimeUsers() {
        List<UsersPo> usersPos = this.getDslContext()
                .select(USERS.fields())
                .from(USERS)
                .where(USERS.USER_TYPE.eq("TEMPORARY").and(USERS.USER_INVALID_DATE.le(DateUtils.getCurrentTime())).and(USERS.ENABLED.eq(TerminologyConfigEnum.ENABLED.ENABLED.name())))
                .fetchInto(UsersPo.class);
        return usersPos;
    }
}
package com.gclife.platform.dao;

import com.gclife.common.dao.base.BaseDao;
import com.gclife.platform.core.jooq.tables.pojos.UserDingRelationPo;
import com.gclife.platform.core.jooq.tables.pojos.UserWeixinRelationPo;

public interface UserDingExDao extends BaseDao {
    /**
     * 查询用户钉钉信息
     *
     * @param userId 用户ID
     * @return UserDingPo
     */
    public UserDingRelationPo loadUserDingRelationPoByUserId(String userId);
}

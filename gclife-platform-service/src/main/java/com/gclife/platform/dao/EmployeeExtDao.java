package com.gclife.platform.dao;


import com.gclife.common.dao.base.BaseDao;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.platform.model.bo.BranchBo;
import com.gclife.platform.model.bo.BranchLevelBo;
import com.gclife.platform.model.bo.ChannelsBo;
import com.gclife.platform.model.bo.EmployeeBo;

import java.util.List;
//

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10.
 * \* Time: 下午1:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * <AUTHOR>
 */
public interface EmployeeExtDao extends BaseDao {

    /**
     * 查询用户管理的销售渠道列表
     * @param userId 用户id
     * @param language 语言
     * @return
     */
    public List<ChannelsBo> loadUsersManagerChannels(String userId, String language);


    /**
     * 加载职员新
     * @param userId 用户id
     * @param language 语言
     * @return
     */
    public EmployeeBo loadEmployeeByUserId(String userId, String language);


    /**
     * 加载职员新
     * @param userIds 用户id集合
     * @param language 语言
     * @return
     */
    public List<EmployeeBo> loadEmployeeByUserIds(List<String> userIds,String language);



}

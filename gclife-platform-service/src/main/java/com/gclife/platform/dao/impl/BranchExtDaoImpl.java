package com.gclife.platform.dao.impl;


import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.pojo.BasePojo;
import com.gclife.common.util.StringUtil;
import com.gclife.platform.base.model.bo.TopBranchConfigDo;
import com.gclife.platform.core.jooq.tables.pojos.BranchPo;
import com.gclife.platform.core.jooq.tables.pojos.TopBranchConfigPo;
import com.gclife.platform.core.jooq.tables.records.BranchRecord;
import com.gclife.platform.dao.BranchExtDao;
import com.gclife.platform.model.bo.*;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.response.BranchNameTreeResponse;
import org.jooq.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.platform.core.jooq.Tables.EMPLOYEE;
import static com.gclife.platform.core.jooq.tables.BaseInternationalText.BASE_INTERNATIONAL_TEXT;
import static com.gclife.platform.core.jooq.tables.BaseSyscode.BASE_SYSCODE;
import static com.gclife.platform.core.jooq.tables.Branch.BRANCH;
import static com.gclife.platform.core.jooq.tables.UserBranch.USER_BRANCH;
import static com.gclife.platform.core.jooq.tables.TopBranchConfig.TOP_BRANCH_CONFIG;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午1:57
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */

@Component
public class BranchExtDaoImpl extends BaseDaoImpl implements BranchExtDao {


    /**
     * 查询用户管理的机构列表(顶层节点)
     *
     * @param userId
     * @return
     */
    @Override
    public List<BranchBo> loadManagerRootBranchs(String userId) {
        List<BranchBo> branchBos = null;
        try {
            branchBos =
                    this.getDslContext()
                            .select(BRANCH.fields())
                            .from(USER_BRANCH)
                            .innerJoin(BRANCH).on(USER_BRANCH.BRANCH_ID.eq(BRANCH.BRANCH_ID))
                            .where(USER_BRANCH.USER_ID.eq(userId))
                            .fetchInto(BranchBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }

        return branchBos;
    }


    /**
     * 查询用户管理的销售渠道列表
     *
     * @param branchId
     * @return
     */
    @Override
    public BranchBo loadBranchsById(String branchId, String language) {
        BranchBo branchBo = null;
        try {
            SelectOnConditionStep selectConditionStep =
                    this.getDslContext()
                            .select(BRANCH.fields())
                            .select(BASE_SYSCODE.CODE_NAME.as("channelTypeName"))
                            .select(BASE_INTERNATIONAL_TEXT.VALUE.as("channelLTypeName"))
                            .from(BRANCH)
                            .leftJoin(BASE_SYSCODE).on(BRANCH.CHANNEL_TYPE_CODE.eq(BASE_SYSCODE.CODE).and(BASE_SYSCODE.CODE_TYPE.eq(TerminologyTypeEnum.CHANNEL_TYPE.name())))
                            .leftJoin(BASE_INTERNATIONAL_TEXT).on(BRANCH.CHANNEL_TYPE_CODE.eq(BASE_INTERNATIONAL_TEXT.KEY).and(BASE_INTERNATIONAL_TEXT.TYPE.eq(TerminologyTypeEnum.CHANNEL_TYPE.name())));

            SelectConditionStep selectOConditionStep = null;
            //判断语言条件
            if (StringUtil.isNullString(language)) {
                selectOConditionStep = selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.isNull())
                        .where(BRANCH.BRANCH_ID.eq(branchId));

            } else {
                selectOConditionStep = selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq(language))
                        .where(BRANCH.BRANCH_ID.eq(branchId));
            }
            System.out.println(selectOConditionStep.toString());

            branchBo = (BranchBo) selectOConditionStep.fetchOneInto(BranchBo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }

        return branchBo;
    }

    /**
     * 查询用户管理的销售渠道列表
     *
     * @param branchIds
     * @return
     */
    @Override
    public List<BranchBo> loadBranchsByIds(List<String> branchIds, String language) {
        List<BranchBo> branchBos = null;
        try {
            SelectOnConditionStep selectConditionStep =
                    this.getDslContext()
                            .select(BRANCH.fields())
                            .select(BASE_SYSCODE.CODE_NAME.as("channelTypeName"))
                            .select(BASE_INTERNATIONAL_TEXT.VALUE.as("channelLTypeName"))
                            .from(BRANCH)
                            .leftJoin(BASE_SYSCODE).on(BRANCH.CHANNEL_TYPE_CODE.eq(BASE_SYSCODE.CODE).and(BASE_SYSCODE.CODE_TYPE.eq(TerminologyTypeEnum.CHANNEL_TYPE.name())))
                            .leftJoin(BASE_INTERNATIONAL_TEXT).on(BRANCH.CHANNEL_TYPE_CODE.eq(BASE_INTERNATIONAL_TEXT.KEY).and(BASE_INTERNATIONAL_TEXT.TYPE.eq(TerminologyTypeEnum.CHANNEL_TYPE.name())));
            //判断语言条件
            if (StringUtil.isNullString(language)) {
                branchBos = selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.isNull())
                        .where(BRANCH.BRANCH_ID.in(branchIds).or(BRANCH.BRANCH_CODE.in(branchIds)))
                        .fetchInto(BranchBo.class);
            } else {
                branchBos = selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq(language))
                        .where(BRANCH.BRANCH_ID.in(branchIds).or(BRANCH.BRANCH_CODE.in(branchIds)))
                        .fetchInto(BranchBo.class);
            }

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return branchBos;
    }


    /**
     * 用户管理的机构列表
     *
     * @param userId 用户ID
     * @return List<BranchLevelBo>
     */
    @Override
    public List<BranchLevelBo> loadUserManagerBranchList(String userId) {
        List<BranchLevelBo> allBranchList = new ArrayList<>();
        try {
            String sql = " WITH RECURSIVE T (branch_id,branch_code, parent_branch_id, branch_name,branch_shortname,belong_branch_id, depth)  AS ( " +
                    "        SELECT branch_id, branch_code,parent_branch_id, branch_name,branch_shortname,belong_branch_id, 1 AS DEPTH " +
                    "        FROM branch " +
                    "        WHERE branch_id  in( " +
                    "          SELECT e.branch_id FROM  user_branch e " +
                    "          WHERE e.user_id='" + userId + "' " +
                    "        ) " +
                    "       AND channel_type_code <> 'MANAGER' " +
                    "     " +
                    "        UNION  " +
                    "     " +
                    "        SELECT  D.branch_id,D.branch_code, D.parent_branch_id, D.branch_name,D.branch_shortname,D.belong_branch_id,T.DEPTH + 1 AS DEPTH " +
                    "        FROM branch D  " +
                    "        JOIN T ON D.parent_branch_id = T.branch_id" +
                    "        where D.channel_type_code <> 'MANAGER'  " +
                    "        ) " +
                    "        SELECT DISTINCT  branch_id, branch_code,parent_branch_id, branch_name,branch_shortname,belong_branch_id, depth FROM T " +
                    "    ORDER BY  T.depth  asc";
            System.err.println(sql);
            //执行树查询
            executeBranchQuery(allBranchList, sql);

        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
        }
        return allBranchList;
    }

    private void executeBranchQuery(List<BranchLevelBo> allBranchList, String sql) {
        this.getDslContext().fetch(sql).stream().map(record -> {
                    BranchLevelBo branchLevelBo = BasePojo.getInstance(BranchLevelBo.class, record.into(BranchRecord.class));
                    long level = (Integer) record.getValue("depth");
                    branchLevelBo.setLevel(level);
                    //添加
                    if (!allBranchList.contains(branchLevelBo)) {
                        allBranchList.add(branchLevelBo);
                    }
                    return null;
                }
        ).collect(Collectors.toList());
    }


    /**
     * 用户管理的机构列表
     *
     * @param branchId 查询机构的所有的子机列表
     * @return List<BranchLevelBo>
     */
    @Override
    public List<BranchLevelBo> loadBranchAllChildsBranchList(String branchId) {
        List<BranchLevelBo> allBranchList = new ArrayList<>();
        try {
            String sql = " WITH RECURSIVE T (branch_id,branch_code, parent_branch_id, branch_name,branch_shortname,belong_branch_id, depth)  AS ( " +
                    "        SELECT branch_id, branch_code,parent_branch_id, branch_name,branch_shortname,belong_branch_id, 1 AS DEPTH " +
                    "        FROM branch " +
                    "        WHERE branch_id ='" + branchId + "' " +
                    "     " +
                    "        UNION ALL " +
                    "     " +
                    "        SELECT  D.branch_id,D.branch_code, D.parent_branch_id, D.branch_name,D.branch_shortname,D.belong_branch_id,T.DEPTH + 1 AS DEPTH " +
                    "        FROM branch D  " +
                    "        JOIN T ON D.parent_branch_id = T.branch_id " +
                    "        ) " +
                    "        SELECT DISTINCT  branch_id, branch_code,parent_branch_id, branch_name,branch_shortname,belong_branch_id, depth FROM T " +
                    "    ORDER BY  T.depth  asc";
            //执行树查询
            executeBranchQuery(allBranchList, sql);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
        }
        return allBranchList;
    }

    /**
     * 加载销售叶子机构集合
     *
     * @param branchIds 用户ID
     * @return
     */
    @Override
    public List<BranchLevelBo> loadBranchAllChildsBranchList(List<String> branchIds) {
        //机构集合
        List<BranchLevelBo> allBranchList = new ArrayList<>();
        try {
            String branchIdStr = "";
            for (String branchId : branchIds) {
                branchIdStr = branchIdStr + "'" + branchId + "',";
            }
            branchIdStr = branchIdStr.substring(0, branchIdStr.length() - 1);
            String sql = " WITH RECURSIVE T (branch_id,branch_code, parent_branch_id, branch_name,branch_shortname,belong_branch_id, depth)  AS ( " +
                    "        SELECT branch_id, branch_code,parent_branch_id, branch_name,branch_shortname, belong_branch_id,1 AS DEPTH " +
                    "        FROM branch " +
                    "        WHERE branch_id  in (  " +
                    " " + branchIdStr + "\n" +
                    "        ) " +
                    "     " +
                    "        UNION ALL " +
                    "     " +
                    "        SELECT  D.branch_id,D.branch_code, D.parent_branch_id, D.branch_name,D.branch_shortname,D.belong_branch_id,T.DEPTH + 1 AS DEPTH " +
                    "        FROM branch D  " +
                    "        JOIN T ON D.parent_branch_id = T.branch_id " +
                    "        ) " +
                    "        SELECT DISTINCT  branch_id, branch_code,parent_branch_id, branch_name,branch_shortname,belong_branch_id, depth FROM T " +
                    "    ORDER BY  T.depth  asc";
            //执行树查询
            System.err.println(sql);
            executeBranchQuery(allBranchList, sql);

        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
        }
        return allBranchList;
    }


    /**
     * 查询机构的父机构列表
     *
     * @param branchId 　销售机构
     * @return
     */
    @Override
    public List<BranchLevelBo> loadParentBranchs(String branchId) {
        List<BranchLevelBo> branchBos = null;
        try {
            String sql = "WITH RECURSIVE T (branch_id,branch_code, parent_branch_id, branch_name,branch_shortname,belong_branch_id, depth)  AS (\n" +
                    "  SELECT branch_id, branch_code,parent_branch_id, branch_name,branch_shortname,belong_branch_id, 1 AS DEPTH\n" +
                    "  FROM branch\n" +
                    "   WHERE branch_id ='" + branchId + "'\n" +
                    "\n" +
                    "UNION ALL\n" +
                    "        SELECT  D.branch_id,D.branch_code, D.parent_branch_id, D.branch_name,D.branch_shortname,D.belong_branch_id,T.DEPTH + 1 AS DEPTH\n" +
                    "  FROM branch D\n" +
                    "  JOIN T ON D.branch_id = T.parent_branch_id\n" +
                    "  )\n" +
                    " SELECT DISTINCT  branch_id, branch_code,parent_branch_id, branch_name,branch_shortname, belong_branch_id,depth FROM T\n" +
                    "WHERE branch_id<>'ROOT'\n" +
                    "ORDER BY  T.depth  asc";
            branchBos = this.getDslContext()
                    .fetch(sql).map(record -> {
                        BranchLevelBo branchLevelBo = BasePojo.getInstance(BranchLevelBo.class, record.into(BranchRecord.class));
                        long level = (Integer) record.getValue("depth");
                        branchLevelBo.setLevel(level);
                        return branchLevelBo;
                    });
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }

        return branchBos;
    }


    /**
     * 根据机构IDS查询机构的父机构列表
     *
     * @param branchIds 　销售机构集合
     * @return
     */
    @Override
    public List<BranchLevelBo> loadParentBranchs(List<String> branchIds) {
        List<BranchLevelBo> branchBos = null;
        try {
            String branchIdStr = "";
            for (String branchId : branchIds) {
                branchIdStr = branchIdStr + "'" + branchId + "',";
            }
            branchIdStr = branchIdStr.substring(0, branchIdStr.length() - 1);

            String sql = "WITH RECURSIVE T (branch_id,branch_code, parent_branch_id, branch_name,branch_shortname,belong_branch_id)  AS (\n" +
                    "  SELECT branch_id, branch_code,parent_branch_id, branch_name,branch_shortname,belong_branch_id\n" +
                    "  FROM branch\n" +
                    "   WHERE branch_id in (" +
                    " " + branchIdStr + "\n" +
                    "   )\n" +
                    "UNION ALL\n" +
                    "        SELECT  D.branch_id,D.branch_code, D.parent_branch_id, D.branch_name,D.branch_shortname,D.belong_branch_id\n" +
                    "  FROM branch D\n" +
                    "  JOIN T ON D.branch_id = T.parent_branch_id\n" +
                    "  )\n" +
                    " SELECT DISTINCT  branch_id, branch_code,parent_branch_id, branch_name, branch_shortname,belong_branch_id FROM T\n" +
                    "WHERE branch_id<>'ROOT'\n";
            System.err.println(sql);
            branchBos = this.getDslContext()
                    .fetch(sql).map(record -> {
                        BranchLevelBo branchLevelBo = BasePojo.getInstance(BranchLevelBo.class, record.into(BranchRecord.class));
                        return branchLevelBo;
                    });
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }

        return branchBos;
    }


    @Override
    public List<BranchNameTreeResponse> loadParentBranchName(String branchId) {
        List<BranchNameTreeResponse> branchNameTreeResponses = new ArrayList<>();
        try {
            String sql = "WITH RECURSIVE T (branch_id, parent_branch_id, branch_name, branch_shortname, depth) AS (\n" +
                    "  SELECT\n" +
                    "    branch_id,\n" +
                    "    parent_branch_id,\n" +
                    "    branch_name,\n" +
                    "    branch_shortname,\n" +
                    "    1 AS DEPTH\n" +
                    "  FROM branch\n" +
                    "  WHERE branch_id = '" + branchId + "'\n" +
                    "\n" +
                    "  UNION ALL\n" +
                    "  SELECT\n" +
                    "    D.branch_id,\n" +
                    "    D.parent_branch_id,\n" +
                    "    D.branch_name,\n" +
                    "    D.branch_shortname,\n" +
                    "    T.DEPTH + 1 AS DEPTH\n" +
                    "  FROM branch D\n" +
                    "    JOIN T ON D.branch_id = T.parent_branch_id\n" +
                    ")\n" +
                    "SELECT DISTINCT\n" +
                    "  branch_id,\n" +
                    "  parent_branch_id,\n" +
                    "  branch_name,\n" +
                    "  branch_shortname,\n" +
                    "  depth\n" +
                    "FROM T\n" +
                    "WHERE branch_id <> 'ROOT'\n" +
                    "ORDER BY T.depth DESC;";
            System.out.println(sql);
            this.getDslContext().fetch(sql).stream().map(record -> {
                BranchPo branchPo = BasePojo.getInstance(BranchPo.class, record.into(BranchRecord.class));
                BranchNameTreeResponse branchNameTreeResponse = new BranchNameTreeResponse();
                branchNameTreeResponse.setBranchId(branchPo.getBranchId());
                branchNameTreeResponse.setBranchCode(branchPo.getBranchCode());
                branchNameTreeResponse.setBranchName(branchPo.getBranchName());
                branchNameTreeResponse.setBranchShortname(branchPo.getBranchShortname());
                branchNameTreeResponse.setDepth(record.getValue("depth") + "");
                branchNameTreeResponses.add(branchNameTreeResponse);
                return null;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }

        return branchNameTreeResponses;
    }


    @Override
    public BranchBo getBranchByUserId(String userId) {
        BranchBo branchBo = null;
        try {
            SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                    .select(BRANCH.fields())
                    .from(EMPLOYEE)
                    .leftJoin(BRANCH).on(BRANCH.BRANCH_ID.eq(EMPLOYEE.BRANCH_ID));
            List<Condition> conditionList = new ArrayList<>();
            conditionList.add(EMPLOYEE.USER_ID.eq(userId));

            selectOnConditionStep.where(conditionList);
            System.out.println(selectOnConditionStep.toString());
            branchBo = selectOnConditionStep.fetchOneInto(BranchBo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_BRANCH_ERROR);
        }
        return branchBo;
    }

    @Override
    public TopBranchConfigPo loadBranchConfig(String branchId, String configType) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .selectDistinct(TOP_BRANCH_CONFIG.fields())
                .select(BRANCH.BRANCH_CODE)
                .from(TOP_BRANCH_CONFIG)
                .innerJoin(BRANCH)
                .on(BRANCH.BELONG_BRANCH_ID.eq(TOP_BRANCH_CONFIG.TOP_BRANCH_ID));
        selectJoinStep.where(BRANCH.BRANCH_ID.eq(branchId)).and(TOP_BRANCH_CONFIG.CONFIG_TYPE.eq(configType));
        System.out.println(selectJoinStep.toString());
        return (TopBranchConfigPo) selectJoinStep.fetchOneInto(TopBranchConfigPo.class);
    }

}
package com.gclife.platform.dao;

import com.gclife.common.dao.base.BaseDao;
import com.gclife.platform.core.jooq.tables.pojos.AccountAuditPo;
import com.gclife.platform.model.bo.AccountBo;
import com.gclife.platform.model.request.AccountQueryRequest;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-14
 * description:账户信息扩展接口
 */
public interface AccountExtDao extends BaseDao{
    /**
     * 查询账户信息
     * @param accountId
     * @return
     */
    public AccountBo loadAccountById(String accountId);

    /**
     * 根据账户号和证件号码查询是否已存在记录
     * @param accountNo
     * @param accountId
     * @return
     */
    public AccountBo loadAccountExist(String accountNo,String accountId,String useType);

    /**
     * 根据用户id查询账户信息
     * @param userId
     * @return
     */
    public List<AccountBo> loadAccountByUserId(String userId);


    /**
     * 根据姓名和银行卡号查询是否已存在记录
     * @param accountOwner
     * @param accountOwner
     * @return
     */
    public AccountBo loadAccountInfoExist(String accountOwner,String accountNo);

    /**
     * 查询账户集合
     * @param accountIdList
     * @return
     */
    List<AccountBo> loadAccountById(List<String> accountIdList);

    AccountAuditPo queryAccountAuditPoByAccountId(String accountId);


    /**
     * @param accountIds
     * @return
     */
    List<AccountAuditPo> queryAccountAuditPoByAccountIds(List<String> accountIds);

    /**
     *
     * @param accountQueryRequest
     * @param userIds
     * @return
     */
    List<AccountBo> queryAccountAudits(AccountQueryRequest accountQueryRequest, List<String> userIds, List<String> keywordUserIds);

    /**
     * 查询审核通过银行卡列表
     * @param userId
     * @return
     */
    List<AccountBo> queryAccountAuditBanks(String userId);

    /**
     * 查询审核通过银行卡列表
     * @param userId
     * @return
     */
    List<AccountBo> queryAccountAuditBanksNew(String userId);

    AccountAuditPo queryAccountAuditPoByAccountIdAndStatus(String accountId,String auditStatus);

    /**
     * 根据用户id批量查询账户集合
     * @param userIds
     * @return
     */
    List<AccountBo> loadAccountByUserIds(List<String> userIds);
}
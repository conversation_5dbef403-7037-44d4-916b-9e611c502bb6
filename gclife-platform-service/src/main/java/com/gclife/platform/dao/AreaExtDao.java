package com.gclife.platform.dao;

import com.gclife.common.dao.base.BaseDao;
import com.gclife.platform.model.bo.AreaBo;
import com.gclife.platform.model.response.AreaTreeResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-9-27
 * description
 */
public interface AreaExtDao extends BaseDao{
    /**
     * 加载区域地址
     * @param areaId
     * @return
     */
    List<AreaBo> loadArea(String areaId);

    /**
     * 地址树
     *
     * @param areaId
     * @param sortType
     * @return
     */
    List<AreaTreeResponse> loadAreaTree(String areaId, String sortType);

    /**
     * 地址树
     * @param areaId
     * @return
     */
    List<AreaTreeResponse> loadAreaTreeNew(String areaId);

    /**
     * 地址详细信息
     * @param areaId
     * @return
     */
    AreaBo loadAreaInfo(String areaId);

    /**
     * 通过id集合查询
     * @param areaIds
     * @return
     */
    List<AreaBo> loadAreaList(List<String> areaIds);
}
package com.gclife.platform.dao;

import com.gclife.common.dao.base.BaseDao;
import com.gclife.platform.model.bo.OperationLogBo;
import com.gclife.platform.model.bo.OperationRoleBo;
import com.gclife.platform.model.bo.OperationUserBo;
import com.gclife.platform.model.query.OperationLogQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/15
 */
public interface OperationLogExtDao extends BaseDao {

    /**
     * 获取操作日志列表
     *
     * @param operationLogQuery
     * @return
     */
    List<OperationLogBo> listOperationLog(OperationLogQuery operationLogQuery);

    /**
     * 获取操作用户
     *
     * @param keyword
     * @return
     */
    List<OperationUserBo> listOperationUserByKeyword(String keyword);

    /**
     * 获取所有操作角色
     *
     * @return
     */
    List<OperationRoleBo> listOperationRole();

    /**
     * 获取角色下的所有用户
     *
     * @param operationRoleId
     * @return
     */
    List<OperationUserBo> listOperationUserByRoleId(String operationRoleId);
}

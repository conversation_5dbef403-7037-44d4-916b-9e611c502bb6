package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.core.jooq.tables.pojos.UserAppDevicePo;
import com.gclife.platform.dao.UserAppDeviceExtDao;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.gclife.platform.core.jooq.tables.UserAppDevice.USER_APP_DEVICE;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 18-4-3
 */
@Component
public class UserAppDeviceExtDaoImpl extends BaseDaoImpl implements UserAppDeviceExtDao {

    /**
     * 根据条件查询用户设备token表记录
     * @param userAppDevicePo 查询条件
     * @return list
     */
    @Override
    public List<UserAppDevicePo> queryUserAppDeviceList(UserAppDevicePo userAppDevicePo) {
        List<UserAppDevicePo> userAppDevicePos=null;
        try {
            SelectJoinStep<Record> recordSelectJoinStep = this.getDslContext()
                    .select(USER_APP_DEVICE.fields())
                    .from(USER_APP_DEVICE);
                    List<Condition> conditions =new ArrayList<>();
                    if(AssertUtils.isNotEmpty(userAppDevicePo.getUserId())){
                        conditions.add(USER_APP_DEVICE.USER_ID.eq(userAppDevicePo.getUserId()));
                    }
                    if(AssertUtils.isNotEmpty(userAppDevicePo.getDeviceChannelId())){
                        conditions.add(USER_APP_DEVICE.DEVICE_CHANNEL_ID.eq(userAppDevicePo.getDeviceChannelId()));
                    }
                    if(AssertUtils.isNotEmpty(userAppDevicePo.getDeviceTypeCode())){
                        conditions.add(USER_APP_DEVICE.DEVICE_TYPE_CODE.eq(userAppDevicePo.getDeviceTypeCode()));
                    }
            userAppDevicePos=recordSelectJoinStep.where(conditions).fetchInto(UserAppDevicePo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_USER_APP_DEVICE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_USER_APP_DEVICE_ERROR);
        }
        return userAppDevicePos;
    }

    @Override
    public UserAppDevicePo queryUserAppDevicePo(String userId,String deviceChannelId) {
        UserAppDevicePo userAppDevicePo=null;
        try {
            userAppDevicePo = this.getDslContext()
                    .select(USER_APP_DEVICE.fields())
                    .from(USER_APP_DEVICE)
                    .where(USER_APP_DEVICE.USER_ID.eq(userId))
                    .and(USER_APP_DEVICE.DEVICE_CHANNEL_ID.eq(deviceChannelId))
                    .and(USER_APP_DEVICE.IS_ONLINE.eq(PlatformTermEnum.YES_NO.YES.name()))
                    .fetchOneInto(UserAppDevicePo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_USER_APP_DEVICE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_USER_APP_DEVICE_ERROR);
        }
        return userAppDevicePo;
    }
}

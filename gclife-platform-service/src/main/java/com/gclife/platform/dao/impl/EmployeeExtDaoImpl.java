package com.gclife.platform.dao.impl;


import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.platform.core.jooq.tables.Branch;
import com.gclife.platform.dao.EmployeeExtDao;
import com.gclife.platform.model.bo.ChannelsBo;
import com.gclife.platform.model.bo.EmployeeBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import org.jooq.*;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.gclife.platform.core.jooq.tables.BaseSyscode.BASE_SYSCODE;
import static com.gclife.platform.core.jooq.tables.Branch.BRANCH;
import static com.gclife.platform.core.jooq.tables.Employee.EMPLOYEE;
import static com.gclife.platform.core.jooq.tables.UserBranch.USER_BRANCH;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午1:57
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * <AUTHOR>
 */

@Component
public class EmployeeExtDaoImpl extends BaseDaoImpl implements EmployeeExtDao {


    private Table<Record> t;

    /**
     * 查询用户销售渠道集合
     * @param userId
     * @return
     */
    @Override
    public List<ChannelsBo> loadUsersManagerChannels(String userId,String language) {

        List<ChannelsBo> channelsBoList =null;
        Branch t1 = BRANCH.as("a");

        try{
            SelectHavingStep selectHavingStep  = this.getDslContext()
                    .select(t1.CHANNEL_TYPE_CODE,BASE_SYSCODE.CODE_NAME.as("channelTypeName"))
                    .from(USER_BRANCH)
                    .innerJoin(t1).on(t1.BRANCH_ID.eq(USER_BRANCH.BRANCH_ID))
                    .innerJoin(BASE_SYSCODE).on(t1.CHANNEL_TYPE_CODE.eq(BASE_SYSCODE.CODE).and(BASE_SYSCODE.CODE_TYPE.eq(TerminologyTypeEnum.CHANNEL_TYPE.name())))
                    .where(USER_BRANCH.USER_ID.eq(userId))
                    .and(BASE_SYSCODE.CODE.notEqual(PlatformTermEnum.CHANNEL_TYPE.MANAGER.name()))
                    .groupBy(t1.CHANNEL_TYPE_CODE, BASE_SYSCODE.CODE_NAME);
            channelsBoList = selectHavingStep.fetchInto(ChannelsBo.class);;
        }catch (Exception e){
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return channelsBoList;

    }


    /**
     * 加载职员新
     * @param userId
     * @return
     */
    @Override
    public EmployeeBo loadEmployeeByUserId(String userId, String language) {
        EmployeeBo employeeBo =null;
        try{
            employeeBo = this.getDslContext()
                    .select(EMPLOYEE.fields())
                    .from(EMPLOYEE)
                    .where(EMPLOYEE.USER_ID.eq(userId))
                    .fetchOneInto(EmployeeBo.class);
        }catch (Exception e){
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return employeeBo;
    }

    /**
     * 加载职员新
     * @param userIds
     * @param language
     * @return
     */
    @Override
    public List<EmployeeBo> loadEmployeeByUserIds(List<String> userIds, String language) {
        List<EmployeeBo> employeBoList =null;
        try{
            employeBoList = this.getDslContext()
                    .select(EMPLOYEE.fields())
                    .from(EMPLOYEE)
                    .where(EMPLOYEE.USER_ID.in(userIds))
                    .fetchInto(EmployeeBo.class);
        }catch (Exception e){
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return employeBoList;
    }




}

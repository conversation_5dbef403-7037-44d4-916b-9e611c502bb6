package com.gclife.platform.dao;

import com.gclife.common.dao.base.BaseDao;
import com.gclife.platform.model.bo.BankBo;
import com.gclife.platform.model.bo.BankInfoBo;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-17
 * description:
 */
public interface BankExtDao extends BaseDao {
    /**
     * 加载银行信息
     *
     *
     * @param bankType
     * @param bankCode
     * @return
     */
    BankBo loadBankById(String bankType, String bankCode);

    /**
     * 银行列表
     *
     * @param bankType 银行类型
     * @param validFlag
     * @return
     */
    List<BankBo> loadBanks(String bankType, String validFlag);

    /**
     * 银行信息列表(sprint-v4.1.0.********)
     *
     * @param bankId 银行ID
     * @return
     */
    BankInfoBo loadBankInfoById(String bankId);

}
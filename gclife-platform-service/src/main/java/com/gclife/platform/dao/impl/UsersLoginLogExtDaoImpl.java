package com.gclife.platform.dao.impl;


import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.platform.dao.UsersLoginLogExtDao;
import com.gclife.platform.model.bo.*;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.gclife.platform.core.jooq.tables.UserLoginLog.USER_LOGIN_LOG;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午1:57
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */

@Component
public class UsersLoginLogExtDaoImpl extends BaseDaoImpl implements UsersLoginLogExtDao {


    @Override
    public UserLoginLogBo loadUserAppNewestLoginLog(String userId) {
        UserLoginLogBo userLoginLogBo = null;
        try {
            userLoginLogBo = this.getDslContext()
                    .select(USER_LOGIN_LOG.fields())
                    .from(USER_LOGIN_LOG)
                    .where(USER_LOGIN_LOG.USER_ID.eq(userId))
                    .and(USER_LOGIN_LOG.DEVICE_TYPE_CODE.eq(PlatformTermEnum.DEVICE_TYPE.IOS.name()).or(USER_LOGIN_LOG.DEVICE_TYPE_CODE.eq(PlatformTermEnum.DEVICE_TYPE.ANDROID.name())))
                    .orderBy(USER_LOGIN_LOG.LOGIN_DATE.desc())
                    .limit(1)
                    .fetchOneInto(UserLoginLogBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return userLoginLogBo;
    }

    /**
     * 查询用户连续登录失败次数
     *
     * @param userId 用户ID
     * @return  int
     */
    @Override
    public int getAppUserFailCount(String userId){
        int stat = 0;
        try {
            List<UserLoginLogBo> userLoginLogBoList = this.getDslContext()
                    .select(USER_LOGIN_LOG.fields())
                    .from(USER_LOGIN_LOG)
                    .where(USER_LOGIN_LOG.USER_ID.eq(userId))
                    .and(USER_LOGIN_LOG.LOGIN_DATE.lessOrEqual(System.currentTimeMillis()))
                    .and(USER_LOGIN_LOG.LOGIN_DATE.greaterOrEqual(System.currentTimeMillis() - 30 * 60 * 1000))
                    .orderBy(USER_LOGIN_LOG.LOGIN_DATE.desc())
                    .fetchInto(UserLoginLogBo.class);

            //逐个循环查找记录
            for (UserLoginLogBo a : userLoginLogBoList) {
                if ("SUCCESS".equals(a.getError())) {
                    break;
                } else {
                    stat++;
                }
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_USER_LOGIN_LOG_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_USER_LOGIN_LOG_ERROR);
        }

        return stat;
    }

}
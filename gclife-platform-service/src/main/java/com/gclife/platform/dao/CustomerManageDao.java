package com.gclife.platform.dao;

import com.gclife.common.model.base.Users;
import com.gclife.platform.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.platform.core.jooq.tables.pojos.CustomerPo;
import com.gclife.platform.model.bo.CustomerMessageBo;
import com.gclife.platform.model.bo.CustomerMessagesBo;
import com.gclife.platform.model.bo.MedalBo;
import com.gclife.platform.model.request.CustomerMessagesRequest;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
public interface CustomerManageDao {

    /***
     * 根据用户获取  客户信息
     * @param users
     * @return
     */
    List<CustomerMessagesBo> getCustomerMessages(Users users, CustomerMessagesRequest customerMessagesRequest);

    /**
     * 查询用户的  客户的 信息
     *
     * @param medalNo
     * @return
     */
    CustomerMessageBo getCustomerMessage(String medalNo,String userId,String customerId);

    /**
     * 查询客户是否有勋章
     * @param customerId
     * @return
     */
    List<MedalBo> getMedalList(String customerId);

    /**
     * 根据编码 获取 勋章信息
     * @param medalNo
     * @return
     */
    MedalBo getMedalIdByOn(String medalNo);

    /**
     * 根据  身份证号  姓名获取 用户信息
     * @param idNo
     * @return
     */
    List<CustomerAgentPo> findByIdNo(String customerId, String idType,String idNo, String userId);
}
package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.dao.OperationLogExtDao;
import com.gclife.platform.model.bo.OperationLogBo;
import com.gclife.platform.model.bo.OperationRoleBo;
import com.gclife.platform.model.bo.OperationUserBo;
import com.gclife.platform.model.properties.SystemOperationLogProperties;
import com.gclife.platform.model.query.OperationLogQuery;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.gclife.platform.core.jooq.Tables.*;

/**
 * <AUTHOR>
 * @date 2022/4/15
 */
@Repository
public class OperationLogExtDaoImpl extends BaseDaoImpl implements OperationLogExtDao {

    @Autowired
    private SystemOperationLogProperties systemOperationLogProperties;

    @Override
    public List<OperationLogBo> listOperationLog(OperationLogQuery operationLogQuery) {
        SelectConditionStep<Record> conditionStep = getDslContext()
                .select(OPERATION_LOG.OPERATION_LOG_ID)
                .select(OPERATION_LOG.USERNAME)
                .select(OPERATION_LOG.REQUEST_TAG)
                .select(OPERATION_LOG.REQUEST_NOTE)
                .select(OPERATION_LOG.REQUEST_DATE)
                .select(OPERATION_LOG.REQUEST_NOTE_KEY)
                .select(OPERATION_LOG.REQUEST_TAG_KEY)
                .select(EMPLOYEE.EMPLOYE_NAME.as("operationName"))
                .select(OPERATION_LOG.OPERATION_LOG_ID.countOver().as("totalLine"))
                .from(OPERATION_LOG)
                .leftJoin(USERS).on(USERS.USERNAME.eq(OPERATION_LOG.USERNAME))
                .leftJoin(EMPLOYEE).on(EMPLOYEE.USER_ID.eq(USERS.USER_ID))
                .where(OPERATION_LOG.USERNAME.isNotNull())
                // 不显示的URL日志
                .and(OPERATION_LOG.REQUEST_URL.notIn(systemOperationLogProperties.getUrlNotDisplayed()))
                ;

        // 过滤角色下的所有用户
        String operationRoleId = operationLogQuery.getOperationRoleId();
        if (AssertUtils.isNotEmpty(operationRoleId)) {
            conditionStep.and(OPERATION_LOG.USER_ID.in(
                    getDslContext()
                            .select(USERS.USER_ID)
                            .from(USERS)
                            .leftJoin(USERS2ROLES).on(USERS.USER_ID.eq(USERS2ROLES.USER_ID))
                            .where(USERS2ROLES.ROLE_ID.eq(operationRoleId))
                            .fetchInto(String.class)));
        }

        String operationUserId = operationLogQuery.getOperationUserId();
        if (AssertUtils.isNotEmpty(operationUserId)) {
            conditionStep.and(OPERATION_LOG.USER_ID.eq(operationUserId));
        }

        // 操作人过滤
        String operationUser = operationLogQuery.getOperationUser();
        if (AssertUtils.isNotEmpty(operationUser)) {
            conditionStep.and(
                    OPERATION_LOG.USERNAME.eq(operationUser)
                            .or(USERS.NAME.eq(operationUser))
                            .or(USERS.NICK_NAME.eq(operationUser))
                            .or(EMPLOYEE.EMPLOYE_NAME.eq(operationUser)));
        }

        // 操作时间过滤
        // 开始时间
        String operationStartDate = operationLogQuery.getOperationStartDate();
        if (AssertUtils.isNotEmpty(operationStartDate)) {
            conditionStep.and(OPERATION_LOG.REQUEST_DATE.ge(DateUtils.timeToTimeLow(
                    DateUtils.stringToTime(operationStartDate, DateUtils.FORMATE3)
            )));
        }
        // 结束时间
        String operationEndDate = operationLogQuery.getOperationEndDate();
        if (AssertUtils.isNotEmpty(operationEndDate)) {
            conditionStep.and(OPERATION_LOG.REQUEST_DATE.le(DateUtils.timeToTimeTop(
                    DateUtils.stringToTime(operationEndDate, DateUtils.FORMATE3)
            )));
        }

        conditionStep.orderBy(OPERATION_LOG.REQUEST_DATE.desc());

        // 分页
        if (AssertUtils.isNotNull(operationLogQuery)) {
            conditionStep.offset(operationLogQuery.getOffset())
                    .limit(operationLogQuery.getPageSize());
        }
        return conditionStep.fetchInto(OperationLogBo.class);
    }

    @Override
    public List<OperationUserBo> listOperationUserByKeyword(String keyword) {
        SelectConditionStep<Record> conditionStep = getDslContext()
                .select(USERS.USER_ID)
                .select(USERS.USERNAME)
                .select(USERS.NICK_NAME)
                .select(USERS.MOBILE)
                .select(USERS.GENDER)
                .select(USERS.EMAIL)
                .select(EMPLOYEE.EMPLOYE_NAME.as("name"))
                .from(USERS)
                .leftJoin(EMPLOYEE).on(EMPLOYEE.USER_ID.eq(USERS.USER_ID))
                .where(USERS.DEVICE_CHANNEL_ID.eq("gclife_bmp_pc"));

        if (AssertUtils.isNotEmpty(keyword)) {
            conditionStep
                    .and(USERS.USERNAME.likeRegex(keyword)
                            .or(USERS.NAME.likeRegex(keyword))
                            .or(USERS.NICK_NAME.likeRegex(keyword))
                            .or(USERS.MOBILE.likeRegex(keyword))
                            .or(EMPLOYEE.EMPLOYE_NAME.likeRegex(keyword)))
            ;
        }
        return conditionStep
                .limit(20)
                .fetchInto(OperationUserBo.class);
    }

    @Override
    public List<OperationRoleBo> listOperationRole() {
        return getDslContext()
                .selectFrom(ROLES)
                .where(ROLES.ENABLED.eq(TerminologyTypeEnum.ENABLED.name()))
                .fetchInto(OperationRoleBo.class);
    }

    @Override
    public List<OperationUserBo> listOperationUserByRoleId(String operationRoleId) {
        return getDslContext()
                .select(USERS.fields())
                .from(USERS)
                .leftJoin(USERS2ROLES).on(USERS.USER_ID.eq(USERS2ROLES.USER_ID))
                .where(USERS2ROLES.ROLE_ID.eq(operationRoleId))
                .fetchInto(OperationUserBo.class);
    }
}

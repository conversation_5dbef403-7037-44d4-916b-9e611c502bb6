package com.gclife.platform.dao;

import com.gclife.common.model.base.Users;
import com.gclife.platform.model.bo.AttendanceBo;
import com.gclife.platform.model.bo.AttendancesMessageBo;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-4
 * description:
 */
public interface AttendanceExtDao {


    /**
     * 获取用户 签到信息
     *
     * @return
     */
    AttendanceBo getAttendance(Users users, Long startTime, Long enfTime,  String activeScenesCode);

    /**
     * 获取 用户的 摸个时间段  签到信息
     *
     * @param users
     * @param startTime
     * @param enfTime
     * @param activeScenesCode
     * @return
     */
    List<AttendancesMessageBo> getAttendancesMessage(Users users, Long startTime, Long enfTime, String activeScenesCode);

}
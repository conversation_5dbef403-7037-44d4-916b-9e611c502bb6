package com.gclife.platform.dao;


import com.gclife.common.dao.base.BaseDao;
import com.gclife.platform.model.bo.StatusClassBo;
import com.gclife.platform.model.bo.SyscodeBo;

import java.util.List;
//

/**
 * \* Created with IntelliJ IDEA.
 * <AUTHOR>
 * \* Date: 17-9-10.
 * \* Time: 下午1:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * \
 */
public interface BaseSysCodeExtDao extends BaseDao {


    /**
     * 查询术语默认语言
     * @param type 术语类型
     * @param key 术语key
     * @param language 语言
     * @return
     */
    public SyscodeBo loadBaseSyscode(String type, String key,String language);



    /**
     * 查询术语默认语言
     * @param type 术语类型
     * @param language 语言
     * @return
     */
    public  List<SyscodeBo>  loadBaseSyscodes(String type,String language);


    /**
     * 查询术语默认语言
     * @param type 术语类型
     * @param sysCodes 术语key集合
     * @param language 语言
     * @return
     */
    public  List<SyscodeBo>  loadBaseSyscodes(String type,List<String> sysCodes,String language);

    /**
     * 根据类型查询  国际化数据
     * @param type
     * @return
     */
    public List<SyscodeBo> internationalTextGet(String type,String language);

    /**
     * 根据类型 key 查询 查询国剧化数据 One
     * @param key
     * @param type
     * @return
     */
    SyscodeBo internationalTextGetOne(String key,String type,String language);

    /**
     * 国际化
     * @param codes　国际化key集合
     * @param type　类型
     * @return  List<SyscodeBo>
     */
    public List<SyscodeBo> loadInternationalListByCodes(List<String> codes,String type,String language);

    /**
     * 根据类型查询  状态分类的国际化数据
     * @param type
     * @return
     */
    List<StatusClassBo> loadBaseSyscodesStatusClass(String type, String lang);
}
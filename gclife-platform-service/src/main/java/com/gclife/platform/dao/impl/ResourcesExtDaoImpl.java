package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.core.jooq.tables.pojos.ResourcesPo;
import com.gclife.platform.dao.ResourcesExtDao;
import com.gclife.platform.model.bo.RolesBo;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.gclife.platform.core.jooq.tables.Resources.RESOURCES;

/**
 * @description:
 * @author: aurong
 * @create: 2022-03-02
 **/
@Component
public class ResourcesExtDaoImpl extends BaseDaoImpl implements ResourcesExtDao {
    @Override
    public List<ResourcesPo> findAll() {
       return this.getDslContext()
                    .select(RESOURCES.fields())
                    .from(RESOURCES)
               .where(RESOURCES.TYPE.eq("BACKEND_URL"))
                    .fetchInto(ResourcesPo.class);

    }
}

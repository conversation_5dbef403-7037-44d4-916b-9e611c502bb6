package com.gclife.platform.dao.impl;


import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.StringUtil;
import com.gclife.platform.dao.BaseSysCodeExtDao;
import com.gclife.platform.model.bo.StatusClassBo;
import com.gclife.platform.model.bo.SyscodeBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import org.jooq.Condition;
import org.jooq.SelectJoinStep;
import org.jooq.SelectOnConditionStep;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.gclife.platform.core.jooq.tables.BaseInternationalText.BASE_INTERNATIONAL_TEXT;
import static com.gclife.platform.core.jooq.tables.BaseSyscode.BASE_SYSCODE;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午1:57
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */

@Component
public class BaseSysCodeExtDaoImpl extends BaseDaoImpl implements BaseSysCodeExtDao {

    /**
     * 1.根据用户默认语言来查询
     * 2.用户默认语言为空的话，使用传进来的语言
     * 需要调整(等待)
     * 查询术语默认语言
     *
     * @param type
     * @param key
     * @return
     */
    @Override
    public SyscodeBo loadBaseSyscode(String type, String key, String language) {
        SyscodeBo syscodeBo = null;
        try {
            SelectOnConditionStep selectConditionStep =
                    this.getDslContext()
                            .select(BASE_SYSCODE.CODE.as("codeKey"), BASE_SYSCODE.CODE_NAME.as("codeName"), BASE_SYSCODE.DESCRIBE, BASE_SYSCODE.SYMBOL.as("symbol"))
                            .select(BASE_INTERNATIONAL_TEXT.VALUE.as("codeLName"), BASE_INTERNATIONAL_TEXT.LANGUAGE, BASE_INTERNATIONAL_TEXT.DESCRIBE.as("describeL"))
                            .from(BASE_SYSCODE)
                            .leftJoin(BASE_INTERNATIONAL_TEXT).on(BASE_SYSCODE.CODE.eq(BASE_INTERNATIONAL_TEXT.KEY).and(BASE_SYSCODE.CODE_TYPE.eq(BASE_INTERNATIONAL_TEXT.TYPE)).and(BASE_INTERNATIONAL_TEXT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
            //判断语言条件
            if (StringUtil.isNullString(language)) {
                syscodeBo = (SyscodeBo) selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.isNull())
                        .where(BASE_SYSCODE.CODE_TYPE.eq(type))
                        .and(BASE_SYSCODE.CODE.eq(key))
                        .and(BASE_SYSCODE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .fetchOneInto(SyscodeBo.class);
            } else {
                syscodeBo = (SyscodeBo) selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq(language))
                        .where(BASE_SYSCODE.CODE_TYPE.eq(type))
                        .and(BASE_SYSCODE.CODE.eq(key))
                        .and(BASE_SYSCODE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .fetchOneInto(SyscodeBo.class);
            }
            return syscodeBo;
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR);
        }
    }


    /**
     * 1.根据用户默认语言来查询
     * 2.用户默认语言为空的话，使用传进来的语言
     * 需要调整(等待)
     * 查询术语默认语言
     *
     * @param type
     * @return
     */
    @Override
    public List<SyscodeBo> loadBaseSyscodes(String type, String language) {
        List<SyscodeBo> syscodeBoList = null;
        try {
            SelectOnConditionStep selectConditionStep =
                    this.getDslContext()
                            .select(BASE_SYSCODE.CODE.as("codeKey"), BASE_SYSCODE.CODE_NAME.as("codeName"), BASE_SYSCODE.DESCRIBE, BASE_SYSCODE.SYMBOL.as("symbol"))
                            .select(BASE_INTERNATIONAL_TEXT.VALUE.as("codeLName"), BASE_INTERNATIONAL_TEXT.LANGUAGE, BASE_INTERNATIONAL_TEXT.DESCRIBE.as("describeL"))
                            .from(BASE_SYSCODE)
                            .leftJoin(BASE_INTERNATIONAL_TEXT)
                            .on(BASE_SYSCODE.CODE.eq(BASE_INTERNATIONAL_TEXT.KEY).and(BASE_SYSCODE.CODE_TYPE.eq(BASE_INTERNATIONAL_TEXT.TYPE)).and(BASE_INTERNATIONAL_TEXT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
            //判断语言条件
            if (StringUtil.isNullString(language)) {
                syscodeBoList = selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.isNull())
                        .where(BASE_SYSCODE.CODE_TYPE.eq(type))
                        .and(BASE_SYSCODE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .orderBy(BASE_SYSCODE.CODE_INDEX.asc())
                        .fetchInto(SyscodeBo.class);
            } else {
                syscodeBoList = selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq(language))
                        .where(BASE_SYSCODE.CODE_TYPE.eq(type))
                        .and(BASE_SYSCODE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .orderBy(BASE_SYSCODE.CODE_INDEX.asc())
                        .fetchInto(SyscodeBo.class);
            }
            return syscodeBoList;
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR);
        }

    }

    @Override
    public List<SyscodeBo> loadBaseSyscodes(String type, List<String> sysCodes, String language) {
        List<SyscodeBo> syscodeBoList = null;
        try {
            SelectOnConditionStep selectConditionStep =
                    this.getDslContext()
                            .select(BASE_SYSCODE.CODE.as("codeKey"), BASE_SYSCODE.CODE_NAME.as("codeName"), BASE_SYSCODE.DESCRIBE, BASE_SYSCODE.SYMBOL.as("symbol"))
                            .select(BASE_INTERNATIONAL_TEXT.VALUE.as("codeLName"), BASE_INTERNATIONAL_TEXT.LANGUAGE, BASE_INTERNATIONAL_TEXT.DESCRIBE.as("describeL"))
                            .from(BASE_SYSCODE)
                            .leftJoin(BASE_INTERNATIONAL_TEXT).on(BASE_SYSCODE.CODE.eq(BASE_INTERNATIONAL_TEXT.KEY).and(BASE_SYSCODE.CODE_TYPE.eq(BASE_INTERNATIONAL_TEXT.TYPE)).and(BASE_INTERNATIONAL_TEXT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
            //判断语言条件
            if (StringUtil.isNullString(language)) {
                syscodeBoList = selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.isNull())
                        .where(BASE_SYSCODE.CODE_TYPE.eq(type))
                        .and(BASE_SYSCODE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(BASE_SYSCODE.CODE.in(sysCodes))
                        .orderBy(BASE_SYSCODE.CODE_INDEX.asc())
                        .fetchInto(SyscodeBo.class);
            } else {
                syscodeBoList = selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq(language))
                        .where(BASE_SYSCODE.CODE_TYPE.eq(type))
                        .and(BASE_SYSCODE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(BASE_SYSCODE.CODE.in(sysCodes))
                        .orderBy(BASE_SYSCODE.CODE_INDEX.asc())
                        .fetchInto(SyscodeBo.class);
            }

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR);
        }
        return syscodeBoList;
    }

    @Override
    public List<SyscodeBo> internationalTextGet(String type, String language) {
        List<SyscodeBo> syscodeBoList = new ArrayList<>();
        try {
            SelectJoinStep selectJoinStep = getDslContext()
                    .select(BASE_INTERNATIONAL_TEXT.KEY.as("codeKey"))
                    .select(BASE_INTERNATIONAL_TEXT.VALUE.as("codeName"))
                    .select(BASE_INTERNATIONAL_TEXT.LANGUAGE.as("language"))
                    .select(BASE_INTERNATIONAL_TEXT.DESCRIBE.as("describe"))
                    .from(BASE_INTERNATIONAL_TEXT);
            List<Condition> conditions = new ArrayList<Condition>();
            if(AssertUtils.isNotEmpty(language)){
                conditions.add(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq(language));
            }else {
                conditions.add(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq("ZH_CN"));
            }

            conditions.add(BASE_INTERNATIONAL_TEXT.TYPE.eq(type));
            conditions.add(BASE_INTERNATIONAL_TEXT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
            selectJoinStep.where(conditions);

            //打印sql
            System.err.println(selectJoinStep.getSQL());

            syscodeBoList = selectJoinStep.fetchInto(SyscodeBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR);
        }
        return syscodeBoList;
    }

    @Override
    public SyscodeBo internationalTextGetOne(String key, String type, String language) {
        SyscodeBo syscodeBo = null;
        try {
            SelectJoinStep selectJoinStep = getDslContext()
                    .select(BASE_INTERNATIONAL_TEXT.KEY.as("codeKey"))
                    .select(BASE_INTERNATIONAL_TEXT.VALUE.as("codeName"))
                    .select(BASE_INTERNATIONAL_TEXT.LANGUAGE.as("language"))
                    .select(BASE_INTERNATIONAL_TEXT.DESCRIBE.as("describe"))
                    .from(BASE_INTERNATIONAL_TEXT);
            List<Condition> conditions = new ArrayList<Condition>();
            if(AssertUtils.isNotEmpty(language)){
                conditions.add(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq(language));
            }else {
                conditions.add(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq("ZH_CN"));
            }
            conditions.add(BASE_INTERNATIONAL_TEXT.KEY.eq(key));
            conditions.add(BASE_INTERNATIONAL_TEXT.TYPE.eq(type));
            conditions.add(BASE_INTERNATIONAL_TEXT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
            selectJoinStep.where(conditions);

            //打印sql
            System.err.println(selectJoinStep.toString());

            syscodeBo = (SyscodeBo) selectJoinStep.fetchOneInto(SyscodeBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR);
        }

        return syscodeBo;
    }


    @Override
    public List<SyscodeBo> loadInternationalListByCodes(List<String> codes,String type,String language) {
        List<SyscodeBo> syscodeBoList = new ArrayList<>();
        try {
            SelectJoinStep selectJoinStep = getDslContext()
                    .select(BASE_INTERNATIONAL_TEXT.KEY.as("codeKey"))
                    .select(BASE_INTERNATIONAL_TEXT.VALUE.as("codeName"))
                    .select(BASE_INTERNATIONAL_TEXT.LANGUAGE.as("language"))
                    .select(BASE_INTERNATIONAL_TEXT.DESCRIBE.as("describe"))
                    .from(BASE_INTERNATIONAL_TEXT);
            List<Condition> conditions = new ArrayList<Condition>();

            if (AssertUtils.isNotEmpty(type)) {
                conditions.add(BASE_INTERNATIONAL_TEXT.TYPE.eq(type));
            }
            if(AssertUtils.isNotEmpty(language)){
                conditions.add(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq(language));
            }else {
                conditions.add(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq("ZH_CN"));
            }
            if(AssertUtils.isNotEmpty(codes)){
                conditions.add(BASE_INTERNATIONAL_TEXT.KEY.in(codes));
            }
            conditions.add(BASE_INTERNATIONAL_TEXT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
            selectJoinStep.where(conditions);

            //打印sql
            System.err.println(selectJoinStep.getSQL());

            syscodeBoList = selectJoinStep.fetchInto(SyscodeBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR);
        }

        return syscodeBoList;
    }

    @Override
    public List<StatusClassBo> loadBaseSyscodesStatusClass(String type, String language) {
        List<StatusClassBo> statusClassBoList = null;
        try {
            SelectOnConditionStep selectConditionStep =
                    this.getDslContext()
                            .select(BASE_SYSCODE.STATUS_CLASS_CODE.as("statusClassCode"), BASE_SYSCODE.STATUS_CLASS_NAME.as("statusClassName"),BASE_SYSCODE.CODE_INDEX.as("codeIndex"))
                            .select(BASE_INTERNATIONAL_TEXT.VALUE.as("codeName"), BASE_INTERNATIONAL_TEXT.LANGUAGE)
                            .from(BASE_SYSCODE)
                            .leftJoin(BASE_INTERNATIONAL_TEXT)
                            .on(BASE_SYSCODE.STATUS_CLASS_CODE.eq(BASE_INTERNATIONAL_TEXT.KEY).and(BASE_INTERNATIONAL_TEXT.TYPE.eq("STATUS_CLASS")).and(BASE_INTERNATIONAL_TEXT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
            //判断语言条件
            if (StringUtil.isNullString(language)) {
                statusClassBoList = selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.isNull())
                        .where(BASE_SYSCODE.CODE_TYPE.eq(type).and(BASE_SYSCODE.STATUS_CLASS_CODE.isNotNull()))
                        .and(BASE_SYSCODE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .orderBy(BASE_SYSCODE.CODE_INDEX.asc())
                        .fetchInto(StatusClassBo.class);
            } else {
                statusClassBoList = selectConditionStep
                        .and(BASE_INTERNATIONAL_TEXT.LANGUAGE.eq(language))
                        .where(BASE_SYSCODE.CODE_TYPE.eq(type).and(BASE_SYSCODE.STATUS_CLASS_CODE.isNotNull()))
                        .and(BASE_SYSCODE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .orderBy(BASE_SYSCODE.CODE_INDEX.asc())
                        .fetchInto(StatusClassBo.class);
            }
            return statusClassBoList;
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_SYSCODE_ERROR);
        }
    }
}
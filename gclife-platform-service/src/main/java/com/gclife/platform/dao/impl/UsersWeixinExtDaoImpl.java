package com.gclife.platform.dao.impl;


import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.platform.core.jooq.tables.pojos.UserWeixinPo;
import com.gclife.platform.core.jooq.tables.pojos.UserWeixinRelationPo;
import com.gclife.platform.dao.UsersWeixinExtDao;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.gclife.platform.core.jooq.tables.UserWeixin.USER_WEIXIN;
import static com.gclife.platform.core.jooq.tables.UserWeixinRelation.USER_WEIXIN_RELATION;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午1:57
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */

@Component
public class UsersWeixinExtDaoImpl extends BaseDaoImpl implements UsersWeixinExtDao {
    /**
     * 查询用户微信关联关系
     * @param userId userId
     * @return  UserWeixinRelationPo
     */
    @Override
    public UserWeixinRelationPo loadUserWeixinRelationPoByOpenId(String userId) {
        UserWeixinRelationPo userWeixinRelationPo = null;
        try {
            userWeixinRelationPo = this.getDslContext()
                    .select(USER_WEIXIN_RELATION.fields())
                    .from(USER_WEIXIN_RELATION)
                    .where(USER_WEIXIN_RELATION.USER_ID.eq(userId))
                    .fetchOneInto(UserWeixinRelationPo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return userWeixinRelationPo;
    }

    /**
     * 查询用户微信信息
     * @param userId 用户ID
     * @return  UserWeixinPo
     */
    @Override
    public UserWeixinPo loadUserWeixinPo(String userId) {
        UserWeixinPo userWeixinPo = null;
        try {
            userWeixinPo = this.getDslContext()
                    .select(USER_WEIXIN.fields())
                    .from(USER_WEIXIN)
                    .where(USER_WEIXIN.USER_ID.eq(userId))
                    .fetchOneInto(UserWeixinPo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return userWeixinPo;
    }

    @Override
    public UserWeixinRelationPo loadUserWeixinRelationPoByUserId(String userId) {
        UserWeixinRelationPo userWeixinRelationPo = null;
        try {
            userWeixinRelationPo = this.getDslContext()
                    .select(USER_WEIXIN_RELATION.fields())
                    .from(USER_WEIXIN_RELATION)
                    .where(USER_WEIXIN_RELATION.USER_ID.eq(userId))
                    .fetchOneInto(UserWeixinRelationPo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return userWeixinRelationPo;
    }

    @Override
    public List<UserWeixinRelationPo> findUserWeixinRelationPo(String unionId, String channelId) {
        return this.getDslContext()
                .select(USER_WEIXIN_RELATION.fields())
                .from(USER_WEIXIN_RELATION)
                .where(USER_WEIXIN_RELATION.UNIONID.eq(unionId))
                .and(USER_WEIXIN_RELATION.CHANNEL_ID.eq(channelId))
                .fetchInto(UserWeixinRelationPo.class);
    }

}
package com.gclife.platform.dao.impl;


import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.platform.dao.RoleExtDao;
import com.gclife.platform.model.bo.RolesBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import org.springframework.stereotype.Component;

import static com.gclife.platform.core.jooq.tables.Roles.ROLES;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10
 * \* Time: 下午1:57
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * <AUTHOR>
 */

@Component
public class RoleExtDaoImpl extends BaseDaoImpl implements RoleExtDao {


    @Override
    public RolesBo loadRolesBo(String roleId) {
        RolesBo rolesBo =null;
        try{
            rolesBo = this.getDslContext()
                    .select(ROLES.fields())
                    .from(ROLES)
                    .fetchOneInto(RolesBo.class);
        }catch (Exception e){
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return rolesBo;
    }
}
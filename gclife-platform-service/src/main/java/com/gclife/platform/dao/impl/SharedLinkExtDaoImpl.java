package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.platform.dao.SharedLinkExtDao;
import com.gclife.platform.model.bo.SharedLinkBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import org.springframework.stereotype.Component;

import static com.gclife.platform.core.jooq.Tables.SHARED_LINK;
import static com.gclife.platform.core.jooq.Tables.SHARED_LINK_CONFIG;

/**
 * <AUTHOR>
 * create 17-12-15
 * description:　链接分享的表
 */
@Component
public class SharedLinkExtDaoImpl extends BaseDaoImpl implements SharedLinkExtDao {
    @Override
    public SharedLinkBo loadSharedLinkBySignature(String signature) {
        SharedLinkBo sharedLinkBo=null;
        try {
            sharedLinkBo =this.getDslContext()
                    .select(SHARED_LINK.fields())
                    .select(SHARED_LINK_CONFIG.LINK_TYPE,
                            SHARED_LINK_CONFIG.LINK_URL.as("actualLink"),
                            SHARED_LINK_CONFIG.PSEUDO_URL.as("pseudoLink"))
                    .from(SHARED_LINK)
                    .innerJoin(SHARED_LINK_CONFIG)
                    .on(SHARED_LINK.SHARED_LINK_CONFIG_ID.eq(SHARED_LINK_CONFIG.SHARED_LINK_CONFIG_ID))
                    .where(SHARED_LINK.SIGNATURE.eq(signature))
                    .fetchOneInto(SharedLinkBo.class);
            return sharedLinkBo;
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_SHARED_LINK_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_SHARED_LINK_ERROR);
        }
    }
}

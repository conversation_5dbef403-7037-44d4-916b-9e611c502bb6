package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.platform.dao.SharedLinkConfigExtDao;
import com.gclife.platform.model.bo.SharedLinkConfigBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import org.springframework.stereotype.Component;

import static com.gclife.platform.core.jooq.Tables.SHARED_LINK_CONFIG;

/**
 * <AUTHOR>
 * create 17-12-15
 * description:用于从Shared_link_config表中查数据
 */
@Component
public class SharedLinkConfigExtDaoImpl extends BaseDaoImpl implements SharedLinkConfigExtDao {
    @Override
    public SharedLinkConfigBo loadSharedLinkConfigByType(String type) {
        SharedLinkConfigBo sharedLinkConfigBo = null;
        try {
            sharedLinkConfigBo = this.getDslContext()
                    .select(SHARED_LINK_CONFIG.fields())
                    .from(SHARED_LINK_CONFIG)
                    .where(SHARED_LINK_CONFIG.LINK_TYPE.eq(type))
                    .fetchOneInto(SharedLinkConfigBo.class);
            return sharedLinkConfigBo;
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_SHARED_LINK_CONFIG_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_SHARED_LINK_CONFIG_ERROR);
        }
    }
}

package com.gclife.platform.dao;

import com.gclife.platform.core.jooq.tables.pojos.UserAppDevicePo;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 18-4-3
 */
public interface UserAppDeviceExtDao {

    /**
     * 根据条件查询用户设备token表记录
     * @param userAppDevicePo 查询条件
     * @return list
     */
    List<UserAppDevicePo> queryUserAppDeviceList(UserAppDevicePo userAppDevicePo);


    /**
     * 查询用户在线设备token表记录
     * @param userId 用户ID
     * @return list
     */
    UserAppDevicePo queryUserAppDevicePo(String userId,String deviceChannelId);
}

package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.pojo.BasePojo;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.core.jooq.tables.records.BaseCareerRecord;
import com.gclife.platform.dao.CareerExtDao;
import com.gclife.platform.model.bo.CareerBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.response.CareerResponse;
import com.gclife.platform.model.response.CareerTreeResponse;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.platform.core.jooq.tables.BaseCareer.BASE_CAREER;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:职业代码扩展接口实现
 */
@Component
public class CareerExtDaoImpl extends BaseDaoImpl implements CareerExtDao {

    @Override
    public List<CareerResponse> loadCareer(String careerId, String providerId) {
        List<CareerResponse> careerResponses = new ArrayList<>();
        try {
            String sql = "SELECT\n" +
                    "  career.*,\n" +
                    "  (SELECT count(1) AS existchild\n" +
                    "   FROM base_career c\n" +
                    "   WHERE c.parent_career_id = career.career_id)\n" +
                    "FROM base_career career\n" +
                    "WHERE career.parent_career_id = '" + careerId + "'";
            if (AssertUtils.isNotEmpty(providerId)) {
                sql = sql + " and career.provider_id = '" + providerId + "'";
            } else {
                sql = sql + " and career.provider_id = 'PRO8888888888888' ";
            }
            sql = sql + " order by career.career_index";
            System.out.println(sql);
            this.getDslContext().fetch(sql).stream().map(record -> {
                CareerBo careerBo = BasePojo.getInstance(CareerBo.class, record.into(BaseCareerRecord.class));
                CareerResponse careerResponse = (CareerResponse) this.converterObject(careerBo, CareerResponse.class);
                careerResponse.setExistChild(record.getValue("existchild") + "");
                careerResponses.add(careerResponse);
                return null;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
        }
        return careerResponses;
    }

    @Override
    public CareerBo getCareerInfo(String careerId) {
        CareerBo careerBo = null;
        try {
            careerBo =
                    this.getDslContext()
                            .select(BASE_CAREER.fields())
                            .from(BASE_CAREER).where(BASE_CAREER.CAREER_ID.eq(careerId).or(BASE_CAREER.CAREER_CODE.eq(careerId))).fetchOneInto(CareerBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
        }
        return careerBo;
    }

    @Override
    public List<CareerBo> getCareerInfo(List<String> careerIds) {
        List<CareerBo> careerBos = null;
        try {
            careerBos =
                    this.getDslContext()
                            .select(BASE_CAREER.fields())
                            .from(BASE_CAREER).where(BASE_CAREER.CAREER_ID.in(careerIds).or(BASE_CAREER.CAREER_CODE.in(careerIds))).fetchInto(CareerBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
        }
        return careerBos;
    }

    @Override
    public List<CareerTreeResponse> loadCareerTree(String careerId) {
        List<CareerTreeResponse> careerTreeResponses = new ArrayList<>();
        try {
            String sql = "WITH RECURSIVE T (career_id, career_name, parent_career_id, depth) AS (\n" +
                    "  SELECT\n" +
                    "    career_id,\n" +
                    "    career_name,\n" +
                    "    parent_career_id,\n" +
                    "    1 AS DEPTH\n" +
                    "  FROM base_career\n" +
                    "  WHERE career_id IN (\n" +
                    "    SELECT e.career_id\n" +
                    "    FROM base_career e\n" +
                    "    WHERE e.career_id = '" + careerId + "'\n" +
                    "          OR e.career_code = '" + careerId + "'\n" +
                    "  )\n" +
                    "  UNION ALL\n" +
                    "  SELECT\n" +
                    "    D.career_id,\n" +
                    "    D.career_name,\n" +
                    "    D.parent_career_id,\n" +
                    "    T.DEPTH + 1 AS DEPTH\n" +
                    "  FROM base_career D\n" +
                    "    JOIN T ON D.career_id = T.parent_career_id\n" +
                    ")\n" +
                    "SELECT DISTINCT\n" +
                    "  career_id,\n" +
                    "  career_name,\n" +
                    "  parent_career_id,\n" +
                    "  depth\n" +
                    "FROM T\n" +
                    "ORDER BY T.depth DESC;";
            System.out.println(sql);
            this.getDslContext().fetch(sql).stream().map(record -> {
                CareerBo careerBo = BasePojo.getInstance(CareerBo.class, record.into(BaseCareerRecord.class));
                CareerTreeResponse careerTreeResponse = new CareerTreeResponse();
                careerTreeResponse.setCareerId(careerBo.getCareerId());
                careerTreeResponse.setCareerName(careerBo.getCareerName());
                careerTreeResponse.setParentCareerId(careerBo.getParentCareerId());
                careerTreeResponse.setDepth(record.getValue("depth") + "");
                careerTreeResponses.add(careerTreeResponse);
                return null;
            }).collect(Collectors.toList());
            return careerTreeResponses;
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_CAREER_ERROR);
        }
    }

}
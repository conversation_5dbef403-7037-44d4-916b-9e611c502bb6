package com.gclife.platform.dao;


import com.gclife.common.dao.base.BaseDao;
import com.gclife.common.model.base.Resources;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.model.bo.*;
import com.gclife.platform.model.request.UserQueryRequest;
import com.gclife.platform.model.response.workflow.UsersWfResponse;

import java.util.List;
//

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10.
 * \* Time: 下午1:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
public interface UsersExtDao extends BaseDao {

    /**
     * 查询用户权限详细信息
     *
     * @param userId
     * @return
     */
    public UsersOauthBo loadUserDetailById(String userId);


    /**
     * 查询用户列表
     *
     * @param userId
     * @return
     */
    public UsersPo loadUserPo(String userId);

    /**
     * 查询用户列表
     * @param userIds
     * @return
     */
    List<UsersBo> loadUserDetailBos(List<String> userIds);

    /**
     * 查询用户列表
     * @param userIds
     * @return
     */
    List<UsersBo> loadUserBos(List<String> userIds);
    /**
     * 根据用户名称查询用户信息
     *
     * @param username
     * @return
     */
    public UsersPo loadUserByUsersName(String username);


    /**
     * 查询用户列表
     *
     * @param userQueryRequest
     * @return
     */
    public List<UsersPo> loadUserList(UserQueryRequest userQueryRequest);


    /**
     * 查询用户角色集合
     *
     * @param userId
     * @return
     */
    public List<RolesBo> loadUserRoles(String userId);

    /**
     * 根据节点ID和机构ID查看哪些用户拥有对应的操作权限
     *
     * @param activityAuid 活动节点
     * @param branchId     机构ID
     * @return 用户集合
     */
    UsersWfResponse loadWorkflowUser(String activityAuid, String branchId);


    /**
     * 根据节点ID和机构ID查看哪些用户拥有对应的操作权限
     *
     * @param resourceCode 活动节点
     * @param branchId     机构ID
     * @return 用户集合
     */
    List<ResourceUserBo> loadResourceUserList(String resourceCode, String branchId);

    /**
     * 获取业务对应的用户列表
     * @param businessCode 业务编码
     * @param branchId     机构ID
     * @return
     */
    List<BusinessUserBo> loadBusinessUserList(String businessCode, String branchId,String branchMode);

    /**
     * 获取业务对应的用户列表
     * @param businessCode 业务编码
     * @param branchId     机构ID
     * @return
     */
    List<BusinessUserBo> loadBusinessUserListNew(String businessCode);


    /**
     * 手机号验证
     *
     * @param countryCode
     * @param mobile
     * @param deviceChannel
     * @return
     */
    UsersBo loadMobileVerify(String countryCode, String mobile,String deviceChannel);

    /**
     *
     * 查询用户能访问的前端资源
     *
     * @param userId
     * @return
     */
    List<MenuResourcesBo> loadFrontendResourcesList(String userId, String language);

    /**
     * 查询工作流节点有权限操作的用户IDs
     * @param activityAuid 节点编码
     * @param branchId　机构
     * @param branchMode　模式
     * @return  List<String>
     */
    List<String> queryWorkflowNodePermissionUsers(String activityAuid, String branchId,String branchMode);

    /**
     *
     * 查询用户能访问的前端资源
     *
     * @param userId
     * @return List<MenuResourcesBo>
     */
    List<MenuResourcesBo> loadBackendResourcesList(String userId);

    /**
     * 查询所有暂时用户到期用户
     *
     * @return
     */
    List<UsersPo> loadTemporarilyOverTimeUsers();

}
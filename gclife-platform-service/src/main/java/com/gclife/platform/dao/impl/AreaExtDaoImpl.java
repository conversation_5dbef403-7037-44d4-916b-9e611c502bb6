package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.pojo.BasePojo;
import com.gclife.platform.core.jooq.tables.records.BaseAreaRecord;
import com.gclife.platform.dao.AreaExtDao;
import com.gclife.platform.model.bo.AreaBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.response.AreaTreeResponse;
import org.jooq.SelectJoinStep;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.platform.core.jooq.tables.BaseArea.BASE_AREA;

/**
 * <AUTHOR>
 * create 17-9-27
 * description:
 */
@Repository
public class AreaExtDaoImpl extends BaseDaoImpl implements AreaExtDao {

    @Override
    public List<AreaBo> loadArea(String areaId) {
        List<AreaBo> areaBos = new ArrayList<>();
        try {

            String sql = "SELECT\n" +
                    "  area.*,\n" +
                    "  (SELECT count(area_id) AS existchild\n" +
                    "   FROM base_area a\n" +
                    "   WHERE a.parent_area_id = area.area_id)\n" +
                    "FROM base_area area\n" +
                    "WHERE area.parent_area_id = '" + areaId + "' order by area.area_index,area.area_id;";
            System.out.println(sql);
            this.getDslContext().fetch(sql).stream().map(record -> {
                AreaBo areaBo = BasePojo.getInstance(AreaBo.class, record.into(BaseAreaRecord.class));
                areaBo.setExistChild(record.getValue("existchild") + "");
                areaBos.add(areaBo);
                return null;
            }).collect(Collectors.toList());
//            SelectJoinStep selectJoinStep =
//                    this.getDslContext()
//                            .select(BASE_AREA.fields())
//                            .from(BASE_AREA);
//
//            selectJoinStep.where(BASE_AREA.PARENT_AREA_ID.like(areaId)).orderBy(BASE_AREA.AREA_ID);
//
//            //打印SQL
//            System.err.println(selectJoinStep.getSQL());
//
//            areaBoList = selectJoinStep.fetchInto(AreaBo.class);

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
        }
        return areaBos;
    }

    @Override
    public List<AreaTreeResponse> loadAreaTree(String areaId, String sortType) {
        List<AreaTreeResponse> areaTreeResponses = new ArrayList<>();
        try {
            String sql = "WITH RECURSIVE T (area_id, area_name, parent_area_id, depth) AS (\n" +
                    "  SELECT\n" +
                    "    area_id,\n" +
                    "    area_name,\n" +
                    "    parent_area_id,\n" +
                    "    1 AS DEPTH\n" +
                    "  FROM base_area\n" +
                    "  WHERE area_id IN (\n" +
                    "    SELECT e.area_id\n" +
                    "    FROM base_area e\n" +
                    "    WHERE e.area_id = '" + areaId + "'\n" +
                    "  )\n" +
                    "  UNION ALL\n" +
                    "  SELECT\n" +
                    "    D.area_id,\n" +
                    "    D.area_name,\n" +
                    "    D.parent_area_id,\n" +
                    "    T.DEPTH + 1 AS DEPTH\n" +
                    "  FROM base_area D\n" +
                    "    JOIN T ON D.area_id = T.parent_area_id\n" +
                    ")\n" +
                    "SELECT DISTINCT\n" +
                    "  area_id,\n" +
                    "  area_name,\n" +
                    "  parent_area_id,\n" +
                    "  depth\n" +
                    "FROM T\n" +
                    "ORDER BY T.depth";
            String sortSql = " DESC;";
            if ("ASC".equals(sortType)) {
                sortSql = " ASC;";
            }
            sql = sql + sortSql;
            System.out.println(sql);
            this.getDslContext().fetch(sql).stream().map(record -> {
                AreaBo areaBo = BasePojo.getInstance(AreaBo.class, record.into(BaseAreaRecord.class));
                AreaTreeResponse areaTreeResponse = new AreaTreeResponse();
                areaTreeResponse.setAreaId(areaBo.getAreaId());
                areaTreeResponse.setAreaName(areaBo.getAreaName());
                areaTreeResponse.setParentAreaId(areaBo.getParentAreaId());
                areaTreeResponse.setDepth(record.getValue("depth") + "");
                areaTreeResponses.add(areaTreeResponse);
                return null;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
        }
        return areaTreeResponses;
    }

    @Override
    public List<AreaTreeResponse> loadAreaTreeNew(String areaId) {
        List<AreaTreeResponse> areaTreeResponses = new ArrayList<>();
        try {
            String sql = "WITH RECURSIVE T (area_id, area_name, parent_area_id, depth) AS (\n" +
                    "  SELECT\n" +
                    "    area_id,\n" +
                    "    area_name,\n" +
                    "    parent_area_id,\n" +
                    "    1 AS DEPTH\n" +
                    "  FROM base_area\n" +
                    "  WHERE area_id IN (\n" +
                    "    SELECT e.area_id\n" +
                    "    FROM base_area e\n" +
                    "    WHERE e.area_id = '" + areaId + "'\n" +
                    "  )\n" +
                    "  UNION ALL\n" +
                    "  SELECT\n" +
                    "    D.area_id,\n" +
                    "    D.area_name,\n" +
                    "    D.parent_area_id,\n" +
                    "    T.DEPTH + 1 AS DEPTH\n" +
                    "  FROM base_area D\n" +
                    "    JOIN T ON D.area_id = T.parent_area_id\n" +
                    ")\n" +
                    "SELECT DISTINCT\n" +
                    "  area_id,\n" +
                    "  area_name,\n" +
                    "  parent_area_id,\n" +
                    "  depth\n" +
                    "FROM T\n" +
                    "ORDER BY T.depth;";
            System.out.println(sql);
            this.getDslContext().fetch(sql).stream().map(record -> {
                AreaBo areaBo = BasePojo.getInstance(AreaBo.class, record.into(BaseAreaRecord.class));
                AreaTreeResponse areaTreeResponse = new AreaTreeResponse();
                areaTreeResponse.setAreaId(areaBo.getAreaId());
                areaTreeResponse.setAreaName(areaBo.getAreaName());
                areaTreeResponse.setParentAreaId(areaBo.getParentAreaId());
                areaTreeResponse.setDepth(record.getValue("depth") + "");
                areaTreeResponses.add(areaTreeResponse);
                return null;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
        }
        return areaTreeResponses;
    }

    @Override
    public AreaBo loadAreaInfo(String areaId) {
        AreaBo areaBo = new AreaBo();
        try {
            SelectJoinStep selectJoinStep =
                    this.getDslContext()
                            .select(BASE_AREA.fields())
                            .from(BASE_AREA);

            selectJoinStep.where(BASE_AREA.AREA_ID.eq(areaId));

            //打印SQL
            System.err.println(selectJoinStep.getSQL());

            areaBo = (AreaBo) selectJoinStep.fetchOneInto(AreaBo.class);

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_AREA_ERROR);
        }
        return areaBo;
    }

    @Override
    public List<AreaBo> loadAreaList(List<String> areaIds) {
        List<AreaBo> areaBos;
            SelectJoinStep selectJoinStep =
                    this.getDslContext()
                            .select(BASE_AREA.fields())
                            .from(BASE_AREA);

            selectJoinStep.where(BASE_AREA.AREA_ID.in(areaIds));

            //打印SQL
            System.err.println(selectJoinStep.getSQL());
            areaBos = selectJoinStep.fetchInto(AreaBo.class);
        return areaBos;
    }


}
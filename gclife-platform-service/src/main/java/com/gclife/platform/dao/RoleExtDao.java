package com.gclife.platform.dao;


import com.gclife.common.dao.base.BaseDao;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.model.bo.RolesBo;
import com.gclife.platform.model.bo.UsersOauthBo;
import com.gclife.platform.model.request.UserQueryRequest;

import java.util.List;
//

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10.
 * \* Time: 下午1:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * <AUTHOR>
 */
public interface RoleExtDao extends BaseDao {



    /**
     * 查询角色
     * @param roleId
     * @return
     */
    public RolesBo loadRolesBo(String roleId);



}
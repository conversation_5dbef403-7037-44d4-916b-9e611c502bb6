package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.core.jooq.tables.daos.AccountAuditDao;
import com.gclife.platform.core.jooq.tables.pojos.AccountAuditPo;
import com.gclife.platform.dao.AccountExtDao;
import com.gclife.platform.model.bo.AccountBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.request.AccountQueryRequest;
import org.jooq.Condition;
import org.jooq.SelectJoinStep;
import org.springframework.stereotype.Repository;


import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.platform.base.model.config.PlatformTermEnum.ACCOUNT_AUDIT_STATUS.AUDIT_PASS;
import static com.gclife.platform.core.jooq.Tables.ACCOUNT_AUDIT;
import static com.gclife.platform.core.jooq.tables.Account.ACCOUNT;


/**
 * <AUTHOR>
 * create 17-10-14
 * description:账户信息扩展接口实现
 */
@Repository
public class AccountExtDaoImpl extends BaseDaoImpl implements AccountExtDao {

    @Override
    public AccountBo loadAccountById(String accountId) {
        AccountBo accountBo = new AccountBo();
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(ACCOUNT.fields())
                    .from(ACCOUNT);
            selectJoinStep.where(ACCOUNT.ACCOUNT_ID.eq(accountId));

            //打印SQL
            System.err.println("获取账户信息SQL:\n" + selectJoinStep.getSQL());

            accountBo = (AccountBo) selectJoinStep.fetchOneInto(AccountBo.class);
            if (AssertUtils.isNotNull(accountBo)) {
                accountBo.setAccountAuditPo(queryAccountAuditPoByAccountId(accountBo.getAccountId()));
            }
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR.getValue());
            new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR);
        }
        return accountBo;
    }

    @Override
    public AccountBo loadAccountExist(String accountNo, String accountId,String useType) {
        AccountBo accountBo = new AccountBo();
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(ACCOUNT.fields())
                    .from(ACCOUNT);
            List<Condition> conditionList = new ArrayList<>();
            if (AssertUtils.isNotEmpty(accountId)) {
                conditionList.add((ACCOUNT.ACCOUNT_NO.eq(accountNo)).or(ACCOUNT.ACCOUNT_ID.eq(accountId)));
            } else {
                conditionList.add(ACCOUNT.ACCOUNT_NO.eq(accountNo));
            }

            //區分業務員和投保人銀行信息
            if (AssertUtils.isNotEmpty(useType)) {
                conditionList.add(ACCOUNT.USE_TYPE.eq(useType));
            }
            selectJoinStep.where(conditionList);
            selectJoinStep.limit(1);
            //打印SQL
            System.err.println("根据账户号和证件号码查询是否存在账户信息SQL:\n" + selectJoinStep.getSQL());

            accountBo = (AccountBo) selectJoinStep.fetchOneInto(AccountBo.class);

        } catch (Exception e) {

        }
        return accountBo;
    }

    @Override
    public List<AccountBo> loadAccountByUserId(String userId) {
        List<AccountBo> accountBos = new ArrayList<>();
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(ACCOUNT.fields())
                    .from(ACCOUNT);
            selectJoinStep.where(ACCOUNT.USER_ID.eq(userId));
            selectJoinStep.orderBy(ACCOUNT.PRIMARY_FLAG.sortAsc(PlatformTermEnum.ACCOUNT_MAIN_FLAG.MAIN.name()));
            //打印SQL
            System.err.println("获取授权账户信息SQL:\n" + selectJoinStep.getSQL());

            accountBos = (List<AccountBo>) selectJoinStep.fetchInto(AccountBo.class);
            AccountBoSetAccountAuditPo(accountBos);

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR.getValue());
            new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR);
        }
        return accountBos;
    }

    /**
     * 查询账户审核信息，设置账户审核信息
     * @param accountBos
     */
    private void AccountBoSetAccountAuditPo(List<AccountBo> accountBos) {
        if (AssertUtils.isNotEmpty(accountBos)) {
            List<String> accountIds = accountBos.stream().map(AccountBo::getAccountId).distinct().collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(accountIds)) {
                List<AccountAuditPo> accountAuditPos = queryAccountAuditPoByAccountIds(accountIds);
                if (AssertUtils.isNotEmpty(accountAuditPos)) {
                    accountBos.forEach(accountBo -> {
                                for (AccountAuditPo accountAuditPo : accountAuditPos) {
                                    if (accountBo.getAccountId().equals(accountAuditPo.getAccountId())) {
                                        accountBo.setAccountAuditPo(accountAuditPo);
                                        break;
                                    }
                                }
                            }
                    );
                }
            }
        }
    }


    @Override
    public AccountBo loadAccountInfoExist(String accountOwner, String accountNo) {
        AccountBo accountBo = new AccountBo();
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(ACCOUNT.fields())
                    .from(ACCOUNT);
            selectJoinStep.where(ACCOUNT.ACCOUNT_NO.eq(accountNo)).and(ACCOUNT.ACCOUNT_OWNER.eq(accountOwner));

            //打印SQL
            System.err.println("根据姓名和银行卡号查询是否已存在账户信息SQL:\n" + selectJoinStep.getSQL());

            accountBo = (AccountBo) selectJoinStep.fetchOneInto(AccountBo.class);

        } catch (Exception e) {

        }
        return accountBo;
    }

    @Override
    public List<AccountBo> loadAccountById(List<String> accountIdList) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(ACCOUNT.fields())
                .from(ACCOUNT);
        selectJoinStep.where(ACCOUNT.ACCOUNT_ID.in(accountIdList));

        //打印SQL
        System.err.println("获取账户信息SQL:\n" + selectJoinStep.getSQL());


        return selectJoinStep.fetchInto(AccountBo.class);
    }

    @Override
    public AccountAuditPo queryAccountAuditPoByAccountId(String accountId) {
        return this.getDslContext().select(ACCOUNT_AUDIT.fields())
                .from(ACCOUNT_AUDIT)
                .where(ACCOUNT_AUDIT.ACCOUNT_ID.eq(accountId))
                .fetchOneInto(AccountAuditPo.class);
    }

    @Override
    public List<AccountAuditPo> queryAccountAuditPoByAccountIds(List<String> accountIds) {
        return this.getDslContext().select(ACCOUNT_AUDIT.fields())
                .from(ACCOUNT_AUDIT)
                .where(ACCOUNT_AUDIT.ACCOUNT_ID.in(accountIds))
                .fetchInto(AccountAuditPo.class);
    }

    @Override
    public AccountAuditPo queryAccountAuditPoByAccountIdAndStatus(String accountId, String auditStatus) {
        return this.getDslContext().select(ACCOUNT_AUDIT.fields())
                .from(ACCOUNT_AUDIT)
                .where(ACCOUNT_AUDIT.ACCOUNT_ID.eq(accountId).and(ACCOUNT_AUDIT.AUDIT_STATUS.eq(auditStatus)))
                .fetchOneInto(AccountAuditPo.class);
    }

    @Override
    public List<AccountBo> loadAccountByUserIds(List<String> userIds) {
        List<AccountBo> accountBos = new ArrayList<>();
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(ACCOUNT.fields())
                    .from(ACCOUNT)
                    .leftJoin(ACCOUNT_AUDIT).on(ACCOUNT_AUDIT.ACCOUNT_ID.eq(ACCOUNT.ACCOUNT_ID));
            selectJoinStep.where(ACCOUNT.USER_ID.in(userIds),ACCOUNT_AUDIT.AUDIT_STATUS.eq(AUDIT_PASS.name()));
            selectJoinStep.orderBy(ACCOUNT.ACCOUNT_ID.asc(), ACCOUNT.PRIMARY_FLAG.sortAsc(PlatformTermEnum.ACCOUNT_MAIN_FLAG.MAIN.name()));
            //打印SQL
            System.err.println("获取授权账户信息SQL:\n" + selectJoinStep.getSQL());
            accountBos = (List<AccountBo>) selectJoinStep.fetchInto(AccountBo.class);
            AccountBoSetAccountAuditPo(accountBos);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR.getValue());
            new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_ACCOUNT_ERROR);
        }
        return accountBos;
    }

    @Override
    public List<AccountBo> queryAccountAudits(AccountQueryRequest accountQueryRequest, List<String> userIds, List<String> keywordUserIds) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(ACCOUNT.fields())
                .select(ACCOUNT.ACCOUNT_ID.countOver().as("totalLine"))
                .from(ACCOUNT)
                .innerJoin(ACCOUNT_AUDIT).on(ACCOUNT_AUDIT.ACCOUNT_ID.eq(ACCOUNT.ACCOUNT_ID));
        List<Condition> conditions = new ArrayList<>();
        conditions.add(ACCOUNT_AUDIT.AUDIT_STATUS.eq(PlatformTermEnum.ACCOUNT_AUDIT_STATUS.UNDER_REVIEW.name()));
        if (AssertUtils.isNotEmpty(userIds)) {
            conditions.add(ACCOUNT.USER_ID.in(userIds));
        }
        if (AssertUtils.isNotEmpty(keywordUserIds)) {
            conditions.add(ACCOUNT.USER_ID.in(keywordUserIds));
        }
        selectJoinStep.where(conditions);
        selectJoinStep.orderBy(ACCOUNT_AUDIT.AUDIT_DATE.asc());
        selectJoinStep.offset(accountQueryRequest.getOffset())
                .limit(accountQueryRequest.getPageSize());
        List<AccountBo> accountBos = selectJoinStep.fetchInto(AccountBo.class);
        if (AssertUtils.isNotEmpty(accountBos)) {
            accountBos.forEach(accountBo -> accountBo.setAccountAuditPo(queryAccountAuditPoByAccountId(accountBo.getAccountId())));
        }
        return accountBos;
    }

    @Override
    public List<AccountBo> queryAccountAuditBanks(String userId) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(ACCOUNT.fields())
                .from(ACCOUNT)
                .innerJoin(ACCOUNT_AUDIT).on(ACCOUNT_AUDIT.ACCOUNT_ID.eq(ACCOUNT.ACCOUNT_ID));
        List<Condition> conditions = new ArrayList<>();
        //conditions.add(ACCOUNT_AUDIT.AUDIT_STATUS.eq(AUDIT_PASS.name()));
        if (AssertUtils.isNotEmpty(userId)) {
            conditions.add(ACCOUNT.USER_ID.in(userId));
        }
        selectJoinStep.where(conditions);
        selectJoinStep.orderBy(ACCOUNT_AUDIT.AUDIT_DATE.asc());
        List<AccountBo> accountBos = selectJoinStep.fetchInto(AccountBo.class);
        if (AssertUtils.isNotEmpty(accountBos)) {
            accountBos.forEach(accountBo -> accountBo.setAccountAuditPo(queryAccountAuditPoByAccountId(accountBo.getAccountId())));
        }
        return accountBos;
    }

    @Override
    public List<AccountBo> queryAccountAuditBanksNew(String userId) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(ACCOUNT.fields())
                .from(ACCOUNT)
                .innerJoin(ACCOUNT_AUDIT).on(ACCOUNT_AUDIT.ACCOUNT_ID.eq(ACCOUNT.ACCOUNT_ID));
        List<Condition> conditions = new ArrayList<>();
        conditions.add(ACCOUNT_AUDIT.AUDIT_STATUS.eq(AUDIT_PASS.name()));
        if (AssertUtils.isNotEmpty(userId)) {
            conditions.add(ACCOUNT.USER_ID.in(userId));
        }
        selectJoinStep.where(conditions);
        selectJoinStep.orderBy(ACCOUNT_AUDIT.AUDIT_DATE.asc());
        List<AccountBo> accountBos = selectJoinStep.fetchInto(AccountBo.class);
        if (AssertUtils.isNotEmpty(accountBos)) {
            accountBos.forEach(accountBo -> accountBo.setAccountAuditPo(queryAccountAuditPoByAccountId(accountBo.getAccountId())));
        }
        return accountBos;
    }
}
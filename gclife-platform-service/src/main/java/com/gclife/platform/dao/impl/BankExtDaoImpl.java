package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.dao.BankExtDao;
import com.gclife.platform.model.bo.BankBo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.model.bo.BankInfoBo;
import org.codehaus.janino.IClass;
import org.jooq.Condition;
import org.jooq.SelectJoinStep;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.gclife.platform.core.jooq.tables.Bank.BANK;

import static com.gclife.platform.core.jooq.tables.BankInfo.BANK_INFO;

/**
 * <AUTHOR>
 * create 17-10-17
 * description:
 */
@Repository
public class BankExtDaoImpl extends BaseDaoImpl implements BankExtDao {

    @Override
    public BankBo loadBankById(String bankType, String bankCode) {
        BankBo bankBo = new BankBo();
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(BANK.fields())
                    .from(BANK);
            List<Condition> conditions = new ArrayList<>();
            conditions.add(BANK.BANK_CODE.eq(bankCode));
            if (!AssertUtils.isNotEmpty(bankType)) {
                bankType = PlatformTermEnum.BANK_TYPE.RECEIPT.name();
            }
            conditions.add(BANK.BANK_TYPE.eq(bankType));
            selectJoinStep.where(conditions);

            //打印SQL
            System.err.println("获取银行信息SQL:\n" + selectJoinStep.getSQL());

            bankBo = (BankBo) selectJoinStep.fetchOneInto(BankBo.class);

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_BANK_ERROR.getValue());
            new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_BANK_ERROR);
        }
        return bankBo;
    }

    @Override
    public List<BankBo> loadBanks(String bankType, String validFlag) {
        List<Condition> conditions = new ArrayList<>();
        if (AssertUtils.isNotEmpty(bankType)) {
            conditions.add(BANK.BANK_TYPE.eq(bankType));
        }
        if (!AssertUtils.isNotEmpty(validFlag)) {
            // 默认查有效银行
            conditions.add(BANK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        } else if (!"all".equals(validFlag)) {
            conditions.add(BANK.VALID_FLAG.eq(validFlag));
        }
        return this.getDslContext()
                .select(BANK.fields())
                .from(BANK)
                .where(conditions)
                .orderBy(BANK.BANK_INDEX).fetchInto(BankBo.class);
    }

    @Override
    public BankInfoBo loadBankInfoById(String bankId) {
        List<Condition> conditions = new ArrayList<>();
        if (AssertUtils.isNotEmpty(bankId)) {
            conditions.add(BANK_INFO.BANK_ID.eq(bankId));
        }
        return this.getDslContext()
                .select(BANK_INFO.fields())
                .from(BANK_INFO)
                .where(conditions)
                .fetchOneInto(BankInfoBo.class);
    }

}
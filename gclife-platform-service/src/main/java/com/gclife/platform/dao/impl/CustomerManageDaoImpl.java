package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.base.Users;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.platform.core.jooq.tables.pojos.CustomerPo;
import com.gclife.platform.dao.CustomerManageDao;
import com.gclife.platform.model.bo.*;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.config.PlatformTermEnum;
import com.gclife.platform.model.request.CustomerMessagesRequest;
import org.jooq.Condition;
import org.jooq.SelectJoinStep;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.gclife.platform.core.jooq.tables.CustomerAgent.CUSTOMER_AGENT;
import static com.gclife.platform.core.jooq.tables.CustomerAgentMedal.CUSTOMER_AGENT_MEDAL;
import static com.gclife.platform.core.jooq.tables.Medal.MEDAL;

/**
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
@Repository
public class CustomerManageDaoImpl extends BaseDaoImpl implements CustomerManageDao {

    @Override
    public List<CustomerMessagesBo> getCustomerMessages(Users users, CustomerMessagesRequest customerMessagesRequest) {

        List<CustomerMessagesBo> customerMessagesBoList;
        try {

            SelectJoinStep selectJoinStep =
                    this.getDslContext()
                            .select(CUSTOMER_AGENT.fields())
                            .select(
                                    DSL.field(
                                            DSL.name("APPLICANT", MEDAL.MEDAL_NO.getName()), String.class).as("applicant")
                            )
                            .select(
                                    DSL.field(
                                            DSL.name("INSURED", MEDAL.MEDAL_NO.getName()), String.class).as("insured")
                            )
                            .select(
                                    DSL.field(
                                            DSL.name("BENEFICIARY", MEDAL.MEDAL_NO.getName()), String.class).as("beneficiary")
                            )
                            .select(CUSTOMER_AGENT.GROUP_CODE.as("groupCode"))
                            .select(CUSTOMER_AGENT.CUSTOMER_AGENT_ID.as("customerId"))
                            .select(CUSTOMER_AGENT.CUSTOMER_AGENT_ID.countOver().as("totalLine"))
                            .from(CUSTOMER_AGENT)
                            .leftJoin(
                                    DSL.table(this.getDslContext()
                                            .select(MEDAL.MEDAL_NO)
                                            .select(CUSTOMER_AGENT_MEDAL.CUSTOMER_ID)
                                            .from(CUSTOMER_AGENT_MEDAL)
                                            .leftJoin(MEDAL).on(CUSTOMER_AGENT_MEDAL.MEDAL_ID.eq(MEDAL.MEDAL_ID))
                                            .where(MEDAL.MEDAL_NO.eq(PlatformTermEnum.MedalEnum.APPLICANT.name()))
                                    ).as("APPLICANT"))
                            .on(CUSTOMER_AGENT.CUSTOMER_AGENT_ID.eq(
                                    DSL.field(
                                            DSL.name("APPLICANT", CUSTOMER_AGENT_MEDAL.CUSTOMER_ID.getName()), String.class)))

                            .leftJoin(
                                    DSL.table(this.getDslContext()
                                            .select(MEDAL.MEDAL_NO)
                                            .select(CUSTOMER_AGENT_MEDAL.CUSTOMER_ID)
                                            .from(CUSTOMER_AGENT_MEDAL)
                                            .leftJoin(MEDAL).on(CUSTOMER_AGENT_MEDAL.MEDAL_ID.eq(MEDAL.MEDAL_ID))
                                            .where(MEDAL.MEDAL_NO.eq(PlatformTermEnum.MedalEnum.INSURED.name()))
                                    ).as("INSURED"))
                            .on(CUSTOMER_AGENT.CUSTOMER_AGENT_ID.eq(
                                    DSL.field(
                                            DSL.name("INSURED", CUSTOMER_AGENT_MEDAL.CUSTOMER_ID.getName()), String.class)))

                            .leftJoin(
                                    DSL.table(this.getDslContext()
                                            .select(MEDAL.MEDAL_NO)
                                            .select(CUSTOMER_AGENT_MEDAL.CUSTOMER_ID)
                                            .from(CUSTOMER_AGENT_MEDAL)
                                            .leftJoin(MEDAL).on(CUSTOMER_AGENT_MEDAL.MEDAL_ID.eq(MEDAL.MEDAL_ID))
                                            .where(MEDAL.MEDAL_NO.eq(PlatformTermEnum.MedalEnum.BENEFICIARY.name()))
                                    ).as("BENEFICIARY"))
                            .on(CUSTOMER_AGENT.CUSTOMER_AGENT_ID.eq(
                                    DSL.field(
                                            DSL.name("BENEFICIARY", CUSTOMER_AGENT_MEDAL.CUSTOMER_ID.getName()), String.class)));


            List<Condition> conditions = new ArrayList<Condition>();

            if (AssertUtils.isNotNull(customerMessagesRequest.getMedalNo())) {
                String medalNo = customerMessagesRequest.getMedalNo();

                if (medalNo.equals(PlatformTermEnum.MedalEnum.APPLICANT.name()) || medalNo.equals(PlatformTermEnum.MedalEnum.INSURED.name()) || medalNo.equals(PlatformTermEnum.MedalEnum.BENEFICIARY.name())) {
                    conditions.add(
                            DSL.field(
                                    DSL.name(customerMessagesRequest.getMedalNo(), MEDAL.MEDAL_NO.getName()), String.class).eq(customerMessagesRequest.getMedalNo())
                    );
                }

                if (medalNo.equals(PlatformTermEnum.MedalEnum.QUASI_CUSTOMER.name())) {
                    conditions.add(DSL.field(DSL.name(PlatformTermEnum.MedalEnum.APPLICANT.name(), MEDAL.MEDAL_NO.getName()), String.class).isNull());
                    conditions.add(DSL.field(DSL.name(PlatformTermEnum.MedalEnum.INSURED.name(), MEDAL.MEDAL_NO.getName()), String.class).isNull());
                    conditions.add(DSL.field(DSL.name(PlatformTermEnum.MedalEnum.BENEFICIARY.name(), MEDAL.MEDAL_NO.getName()), String.class).isNull());
                }

                if (medalNo.equals(PlatformTermEnum.MedalEnum.CUSTOMER.name())) {
                    conditions.add(DSL.field(DSL.name(PlatformTermEnum.MedalEnum.APPLICANT.name(), MEDAL.MEDAL_NO.getName()), String.class).isNotNull()
                            .or(DSL.field(DSL.name(PlatformTermEnum.MedalEnum.INSURED.name(), MEDAL.MEDAL_NO.getName()), String.class).isNotNull())
                            .or(DSL.field(DSL.name(PlatformTermEnum.MedalEnum.BENEFICIARY.name(), MEDAL.MEDAL_NO.getName()), String.class).isNotNull()));
                }
            }
            if (AssertUtils.isNotNull(customerMessagesRequest.getName())) {
                conditions.add(CUSTOMER_AGENT.NAME.like("%" + customerMessagesRequest.getName() + "%"));
            }

            conditions.add(CUSTOMER_AGENT.USER_ID.eq(users.getUserId()));

            selectJoinStep.where(conditions);

            selectJoinStep.orderBy(CUSTOMER_AGENT.GROUP_CODE);
            //分页查询
            selectJoinStep.offset(customerMessagesRequest.getOffset()).limit(customerMessagesRequest.getPageSize());

            //打印SQL
            System.err.println(selectJoinStep.toString());
            customerMessagesBoList = selectJoinStep.fetchInto(CustomerMessagesBo.class);

        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_QUERY_CUSTOMER_MESSAGE_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_QUERY_CUSTOMER_MESSAGE_ERROR);
        }
        return customerMessagesBoList;
    }

    @Override
    public CustomerMessageBo getCustomerMessage(String medalNo,String userId, String customerId) {
        CustomerMessageBo customerMessageBo;
        try {
            SelectJoinStep selectJoinStep =
                    this.getDslContext()
                            .select(CUSTOMER_AGENT.CUSTOMER_AGENT_ID.as("customerId"))
                            .select(MEDAL.MEDAL_NO.as("medalNo"))
                            .from(CUSTOMER_AGENT)
                            .innerJoin(CUSTOMER_AGENT_MEDAL).on(CUSTOMER_AGENT.CUSTOMER_AGENT_ID.eq(CUSTOMER_AGENT_MEDAL.CUSTOMER_ID))
                            .innerJoin(MEDAL).on(CUSTOMER_AGENT_MEDAL.MEDAL_ID.eq(MEDAL.MEDAL_ID));


            List<Condition> conditions = new ArrayList<Condition>();

            conditions.add(CUSTOMER_AGENT.CUSTOMER_AGENT_ID.eq(customerId));

            conditions.add(CUSTOMER_AGENT.USER_ID.eq(userId));

            conditions.add(MEDAL.MEDAL_NO.eq(medalNo));

            selectJoinStep.where(conditions);

            customerMessageBo = (CustomerMessageBo) selectJoinStep.fetchOneInto(CustomerMessageBo.class);
            System.out.println(selectJoinStep.toString());

        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_AGENT_ERROR);
        }

        return customerMessageBo;
    }

    @Override
    public List<MedalBo> getMedalList(String customerId) {
        List<MedalBo> medalBoList = null;
        try {
            SelectJoinStep selectJoinStep =
                    this.getDslContext()
                            .select(CUSTOMER_AGENT_MEDAL.fields())
                            .from(CUSTOMER_AGENT_MEDAL);
            List<Condition> conditions = new ArrayList<Condition>();
            conditions.add(CUSTOMER_AGENT_MEDAL.CUSTOMER_ID.eq(customerId));
            selectJoinStep.where(conditions);
            System.out.println(selectJoinStep.toString());
            medalBoList = selectJoinStep.fetchInto(MedalBo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_ERROR);
        }
        return medalBoList;
    }

    @Override
    public MedalBo getMedalIdByOn(String medalNo) {
        MedalBo medalBo = null;
        try {

            SelectJoinStep selectJoinStep =
                    this.getDslContext()
                            .select(MEDAL.fields())
                            .from(MEDAL);
            List<Condition> conditions = new ArrayList<Condition>();

            conditions.add(MEDAL.MEDAL_NO.eq(medalNo));

            selectJoinStep.where(conditions);

            medalBo = (MedalBo) selectJoinStep.fetchOneInto(MedalBo.class);
            System.out.println(selectJoinStep.toString());
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_ERROR);
        }

        return medalBo;
    }

    @Override
    public List<CustomerAgentPo> findByIdNo(String customerId,String idType, String idNo, String userId) {
        List<CustomerAgentPo> customerPoList;
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(CUSTOMER_AGENT.fields())
                    .from(CUSTOMER_AGENT);
            List<Condition> conditionList = new ArrayList<>();

            if (AssertUtils.isNotEmpty(customerId)) {
                conditionList.add(CUSTOMER_AGENT.CUSTOMER_AGENT_ID.ne(customerId));
            }else if (AssertUtils.isNotEmpty(idType)&&AssertUtils.isNotEmpty(idNo)) {
                conditionList.add(CUSTOMER_AGENT.ID_TYPE.eq(idType));
                conditionList.add(CUSTOMER_AGENT.ID_NO.eq(idNo));
                conditionList.add(CUSTOMER_AGENT.USER_ID.eq(userId));
            }
            selectJoinStep.where(conditionList);

            System.out.println(selectJoinStep.toString());
            customerPoList = selectJoinStep.orderBy(CUSTOMER_AGENT.CREATED_DATE.desc()).fetchInto(CustomerAgentPo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_SAVE_CUSTOMER_ERROR);
        }
        return customerPoList;
    }
}
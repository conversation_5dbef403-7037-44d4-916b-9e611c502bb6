package com.gclife.platform.dao;

import com.gclife.common.dao.base.BaseDao;
import com.gclife.platform.model.response.CareerResponse;
import com.gclife.platform.model.bo.CareerBo;
import com.gclife.platform.model.response.CareerResponse;
import com.gclife.platform.model.response.CareerTreeResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-9-27
 * description
 */
public interface CareerExtDao extends BaseDao {
    /**
     * 获取职业信息
     *
     * @param careerId
     * @param providerId
     * @return
     */
    List<CareerResponse> loadCareer(String careerId, String providerId);

    /**
     * 获取职业信息
     *
     * @param careerId 职业ID
     * @return CareerBo
     */
    CareerBo getCareerInfo(String careerId);

    /**
     * 根据职业ids或者职业codes获取职业详情
     *
     * @param careerIds 职业ID
     * @return List<CareerBo>
     */
    List<CareerBo> getCareerInfo(List<String> careerIds);

    /**
     * 职业树
     *
     * @param careerId
     * @return
     */
    List<CareerTreeResponse> loadCareerTree(String careerId);

}
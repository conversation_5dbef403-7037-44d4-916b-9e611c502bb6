package com.gclife.platform.dao;


import com.gclife.common.dao.base.BaseDao;
import com.gclife.platform.core.jooq.tables.pojos.UsersPo;
import com.gclife.platform.model.bo.RolesBo;
import com.gclife.platform.model.bo.UserLoginLogBo;
import com.gclife.platform.model.bo.UsersBo;
import com.gclife.platform.model.bo.UsersOauthBo;
import com.gclife.platform.model.request.UserQueryRequest;
import com.gclife.platform.model.response.workflow.UsersWfResponse;

import java.util.List;
//

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10.
 * \* Time: 下午1:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
public interface UsersLoginLogExtDao extends BaseDao {

    /**
     * 查询用户最新登录信息
     *
     * @param userId 用户ID
     * @return  UserLoginLogBo
     */
    public UserLoginLogBo loadUserAppNewestLoginLog(String userId);

    /**
     * 查询用户连续登录失败次数
     *
     * @param userId 用户ID
     * @return  int
     */
    public int getAppUserFailCount(String userId);

}
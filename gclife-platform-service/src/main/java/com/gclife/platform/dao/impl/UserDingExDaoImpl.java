package com.gclife.platform.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.core.jooq.tables.pojos.UserDingRelationPo;
import com.gclife.platform.dao.UserDingExDao;
import static com.gclife.platform.core.jooq.tables.UserDingRelation.USER_DING_RELATION;
import org.springframework.stereotype.Component;


@Component
public class UserDingExDaoImpl extends BaseDaoImpl implements UserDingExDao {
    @Override
    public UserDingRelationPo loadUserDingRelationPoByUserId(String userId) {
        UserDingRelationPo userDingRelationPo = null;
        try {
            userDingRelationPo = this.getDslContext()
                    .select(USER_DING_RELATION.fields())
                    .from(USER_DING_RELATION)
                    .where(USER_DING_RELATION.USER_ID.eq(userId))
                    .fetchOneInto(UserDingRelationPo.class);
        } catch (Exception e) {
            this.getLogger().error(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR.getValue());
            throw new RequestException(PlatformErrorConfigEnum.USERS_QUERY_USERS_ERROR);
        }
        return userDingRelationPo;
    }


}

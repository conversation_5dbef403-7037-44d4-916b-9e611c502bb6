package com.gclife.platform.dao;


import com.gclife.common.dao.base.BaseDao;
import com.gclife.common.model.base.Users;
import com.gclife.platform.base.model.bo.TopBranchConfigDo;
import com.gclife.platform.core.jooq.tables.pojos.TopBranchConfigPo;
import com.gclife.platform.model.bo.BranchBo;
import com.gclife.platform.model.bo.BranchLevelBo;
import com.gclife.platform.model.response.BranchNameTreeResponse;

import java.util.List;
//

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10.
 * \* Time: 下午1:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * <AUTHOR>
 */
public interface BranchExtDao extends BaseDao {


    /**
     * 查询用户管理的销售渠道列表
     * @param branchId 机构id
     * @param language 语言
     * @return
     */
    public BranchBo loadBranchsById(String branchId,String language);



    /**
     * 查询用户管理的销售渠道列表
     * @param branchIds 机构id集合
     * @param language 语言
     * @return
     */
    public List<BranchBo> loadBranchsByIds(List<String> branchIds,String language);


    /**
     * 查询机构下所有子机构列表
     * @param branchId
     * @return
     */
    public List<BranchLevelBo> loadBranchAllChildsBranchList(String branchId);

    /**
     * 加载销售叶子机构集合
     * @param branchIds 机构集合
     * @return
     */
    public List<BranchLevelBo> loadBranchAllChildsBranchList(List<String> branchIds);



    /**
     * 加载职员管理的销售机构集合
     * @param userId 用户ID
     * @return
     */
    public List<BranchLevelBo> loadUserManagerBranchList(String userId);



    /**
     * 加载职员管理的销售机构集合
     * @param userId 用户ID
     * @return
     */
    public List<BranchBo> loadManagerRootBranchs(String userId);




    /**
     * 查询父机构树集合
     * @param branchId　销售机构
     * @return
     */
    public List<BranchLevelBo> loadParentBranchs(String branchId);


    /**
     * 查询父机构树集合
     * @param branchIds　销售机构集合
     * @return
     */
    public List<BranchLevelBo> loadParentBranchs(List<String> branchIds);

    List<BranchNameTreeResponse> loadParentBranchName(String branchId);

    /**
     *
     * @return
     */
    BranchBo getBranchByUserId(String userId);

    /**
     * 查询机构顶级配置
     * @param branchId
     * @param configType
     * @return
     */
    TopBranchConfigPo loadBranchConfig(String branchId, String configType);

}
package com.gclife.party;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.common.util.DateUtils;
import com.gclife.party.dao.TopicBaseDao;
import com.gclife.party.model.bo.TopicUserStatisticsBo;
import com.gclife.party.model.vo.TopicStatisticsQueryVo;
import com.gclife.party.service.TopicBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-18
 * description:账户信息测试
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class TopicBaseServiceTest extends UsersTest {
    @Autowired
    TopicBaseDao topicBaseDao;

    @Autowired
    TopicBusinessService topicBusinessService;

    */
/**
     * 根据账户ID获取账户信息
     *//*

    @Test
    public void queryOneBranchById() {
        try {
            TopicStatisticsQueryVo topicStatisticsQueryVo = new TopicStatisticsQueryVo();
            topicStatisticsQueryVo.setUserId("ca28c884c69a4744b6aed83ff45a6252");
            topicStatisticsQueryVo.setYearMonth("201901");
            topicStatisticsQueryVo.setYearMonthDay(DateUtils.getCurrentDateToTime());

            List<TopicUserStatisticsBo> topicUserStatisticsBos = topicBaseDao.queryTopicUserStatisticsBo(topicStatisticsQueryVo);
            System.out.println(JSON.toJSONString(topicUserStatisticsBos));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

    @Test
    public void queryOneUserTopicSignStatistics() {
        try {

           ResultObject resultObject  = topicBusinessService.queryOneUserTopicSignStatistics(this.getCurrentLoginUsers(), null, null);
            System.out.println(JSON.toJSONString(resultObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }

}*/

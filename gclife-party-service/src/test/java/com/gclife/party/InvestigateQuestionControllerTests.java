package com.gclife.party;

import com.alibaba.fastjson.JSON;
import com.gclife.party.model.request.CustomerAgentRequest;
import com.gclife.party.model.request.InvestigateCustomerRequest;
import com.gclife.party.model.request.InvestigateQuestionRequest;
import com.gclife.party.model.vo.InvestigateAttachmentVo;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class InvestigateQuestionControllerTests {

    @Test
    public void saveInvestigateQuestionList() {
        InvestigateCustomerRequest investigateCustomerRequest = new InvestigateCustomerRequest();
        CustomerAgentRequest customerAgentRequest = new CustomerAgentRequest();
        customerAgentRequest.setName("白忠英");
        customerAgentRequest.setBirthdayFormat("1994-02-25");
        customerAgentRequest.setOccupationCode("GC-0201002");
        customerAgentRequest.setHomeAreaCode("806100");
        customerAgentRequest.setEmail("<EMAIL>");
        customerAgentRequest.setWechatNo("56165165165");
        customerAgentRequest.setFacebookNo("Bored Panda");

        List<InvestigateAttachmentVo> attachmentList = new ArrayList<>();
        InvestigateAttachmentVo investigateAttachmentVo1 = new InvestigateAttachmentVo();
        investigateAttachmentVo1.setAttachmentId("021ba66b4e4c4074a4ddae5daea9a2c0");
        investigateAttachmentVo1.setAttachmentSeq(1l);
        attachmentList.add(investigateAttachmentVo1);

        InvestigateAttachmentVo investigateAttachmentVo2 = new InvestigateAttachmentVo();
        investigateAttachmentVo2.setAttachmentId("021ba66b4e4c4074a4ddae5daea9a2c0");
        investigateAttachmentVo2.setAttachmentSeq(1l);
        attachmentList.add(investigateAttachmentVo2);

        customerAgentRequest.setAttachmentList(attachmentList);
        investigateCustomerRequest.setCustomerAgent(customerAgentRequest);

        List<InvestigateQuestionRequest> investigateQuestionList = new ArrayList<>();
        for (int i = 1; i < 7; i++) {
            InvestigateQuestionRequest investigateQuestionRequest = new InvestigateQuestionRequest();
            investigateQuestionRequest.setQuestionId("ID_CUSTOMER_QUESTIONNAIRE"+i);
            List<String> stringList = new ArrayList<>();
            stringList.add("ID_CUSTOMER_QUESTIONNAIRE"+i+"_A");
            investigateQuestionRequest.setQuestionOptionIdList(stringList);
            investigateQuestionList.add(investigateQuestionRequest);
        }

        investigateCustomerRequest.setInvestigateQuestionList(investigateQuestionList);

        System.out.println(JSON.toJSONString(investigateCustomerRequest));
    }


}
*/

package com.gclife.party;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.util.DateUtils;
import com.gclife.party.model.request.CustomerMessagesRequest;
import com.gclife.party.model.request.EndorseCustomerRequest;
import com.gclife.party.model.request.UserCustomerRequest;
import com.gclife.party.service.CustomerBaseBusinessService;
import com.gclife.party.service.CustomerManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * create 17-10-18
 * description:账户信息测试
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class CustomerBaseServiceTest extends UsersTest {
    @Autowired
    CustomerBaseBusinessService customerBaseBusinessService;
    @Autowired
    CustomerManageService customerManageService;


    */
/**
     * 根据账户ID获取账户信息
     *//*

    @Test
    public void queryOneBranchById() {
        try {
            System.out.println("=============================================查询机构信息 start================================================");
            for (int i = 0; i < 10; i++) {
                String number = DateUtils.getJobNumberByTime("", "", DateUtils.FORMATE54, false);
                System.out.println(number);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================查询机构信息 end================================================");
    }


    @Test
    public void synchronizeBaseCustomer() {
        try {
            System.out.println("=============================================同步客户信息 start================================================");
            EndorseCustomerRequest endorseCustomerRequest=new EndorseCustomerRequest();
            endorseCustomerRequest.setCustomerId("CUSTOMER_ccefe3be-3245-4708-8f02-47d4fe317b80");
            endorseCustomerRequest.setCustomerNo("201809110509452421");
            endorseCustomerRequest.setEmail("<EMAIL>");
            endorseCustomerRequest.setHomeAddress("华南城五号馆");
            endorseCustomerRequest.setHomePhone("015733111");
            endorseCustomerRequest.setHomeZipCode("438500");
            endorseCustomerRequest.setMobile("***********");
            endorseCustomerRequest.setHomeAreaCode("801000");
            ResultObject resultObject=customerBaseBusinessService.synchronizeBaseCustomer(endorseCustomerRequest,getCurrentLoginUsers());
            System.out.println(resultObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================同步客户信息 end================================================");
    }

    @Test
    public void getCustomerMessages() {
        try {
            System.out.println("=============================================同步客户信息 start================================================");
            CustomerMessagesRequest customerMessagesRequest=new CustomerMessagesRequest();
            customerMessagesRequest.setMedalNo("APPLICANT");
            ResultObject resultObject=customerManageService.getCustomerMessages(getCurrentLoginUsers(),customerMessagesRequest);
            System.out.println(resultObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================同步客户信息 end================================================");
    }

    @Test
    public void saveCustomerMessage() {
        try {
            System.out.println("=============================================同步客户信息 start================================================");
            UserCustomerRequest userCustomerRequest=new UserCustomerRequest();
            userCustomerRequest.setName("jackie");
            userCustomerRequest.setIdType("ID");
            userCustomerRequest.setIdNo("211513231");
            userCustomerRequest.setSex("MALE");
            userCustomerRequest.setBirthday(1322712732000L);
            ResultObject resultObject=customerManageService.saveCustomerMessage(userCustomerRequest,getCurrentLoginUsers());
            System.out.println(resultObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================同步客户信息 end================================================");
    }

    @Test
    public void getAgentCustomerRelation() {
        try {
            System.out.println("=============================================同步客户信息 start================================================");
            CustomerMessagesRequest customerMessagesRequest=new CustomerMessagesRequest();
            customerMessagesRequest.setMedalNo("APPLICANT");
            ResultObject resultObject=customerBaseBusinessService.getAgentCustomerRelation("956f815b102245a5a72bd4aaba1a7fd1");
            System.out.println(resultObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================同步客户信息 end================================================");
    }

    @Test
    public void getBaseCustomer() {
        try {
            System.out.println("=============================================同步客户信息 start================================================");
            ResultObject resultObject=customerBaseBusinessService.getBaseCustomer("956f815b102245a5a72bd4aaba1a7fd1","20180925132428");
            System.out.println(resultObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================同步客户信息 end================================================");
    }

    @Test
    public void queryCustomerList() {
        try {
            System.out.println("=============================================同步客户信息 start================================================");
            CustomerMessagesRequest customerMessagesRequest=new CustomerMessagesRequest();
            customerMessagesRequest.setKeyword("STA NANNAN");
            ResultObject resultObject=customerBaseBusinessService.queryCustomerList(new AppRequestHeads(),customerMessagesRequest);
            System.out.println(resultObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("=============================================同步客户信息 end================================================");
    }
}*/

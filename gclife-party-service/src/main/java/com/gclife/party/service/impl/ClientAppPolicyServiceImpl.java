package com.gclife.party.service.impl;

import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentKeyWordResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.client.api.ClientCustomerBaseApi;
import com.gclife.client.model.request.CustomerAndRelationshipRequest;
import com.gclife.client.model.request.CustomerRequest;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.error.ApplyErrorConfigEnum;
import com.gclife.common.error.PolicyErrorConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.party.core.jooq.tables.daos.*;
import com.gclife.party.core.jooq.tables.pojos.*;
import com.gclife.party.model.bo.PolicyManageListBo;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.UserCustomerRequest;
import com.gclife.party.model.request.client.ClientManageListRequest;
import com.gclife.party.model.request.client.ClientPolicyEntryRequest;
import com.gclife.party.model.request.client.ClientPolicyErrorRequest;
import com.gclife.party.model.request.policy.DutyClassResponse;
import com.gclife.party.model.request.policy.PolicyCoverageRequest;
import com.gclife.party.model.response.client.*;
import com.gclife.party.model.response.customer.MemberResponse;
import com.gclife.party.service.ClientAppPolicyService;
import com.gclife.party.service.CustomerManageService;
import com.gclife.party.service.base.ClientManagerBaseService;
import com.gclife.party.service.base.CustomerBaseService;
import com.gclife.party.transform.LanguageUtils;
import com.gclife.party.validate.transfer.CustomerBaseTransfer;
import com.gclife.platform.api.PlatformEmployeeApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.api.PlatformUsersBaseApi;
import com.gclife.platform.model.request.EmployeeQueryRequest;
import com.gclife.platform.model.response.EmployeResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.product.model.response.ProviderResponse;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.gclife.common.model.config.TerminologyTypeEnum.DATE_TYPE;

/**
 * <AUTHOR>
 * create 2022/8/17 17:06
 * description:
 */
@Service
@Slf4j
public class ClientAppPolicyServiceImpl extends BaseBusinessServiceImpl implements ClientAppPolicyService {
    @Autowired
    private ClientManagerBaseService clientManagerBaseService;
    @Autowired
    private PolicyDao policyDao;
    @Autowired
    private PlatformEmployeeApi platformEmployeeApi;
    @Autowired
    private PlatformUsersBaseApi platformUsersBaseApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PolicyInsuredDao policyInsuredDao;
    @Autowired
    private CustomerBaseService customerBaseService;
    @Autowired
    private DutyClassDao dutyClassDao;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private ProviderDao providerDao;
    @Autowired
    private PolicyCoverageDao policyCoverageDao;
    @Autowired
    private PolicyAttachmentDao policyAttachmentDao;
    @Autowired
    private CustomerBaseTransfer customerBaseTransfer;
    @Autowired
    private ClientCustomerBaseApi clientCustomerBaseApi;
    @Autowired
    private CustomerManageService customerManageService;
    public Map<String, List<SyscodeResponse>> mapSyscodeList;

    /**
     * 客户管理保单录入列表
     *
     * @param clientManageListRequest 保单录入列表
     * @return PolicyManageListResponse
     */
    @Override
    public ResultObject<BasePageResponse<PolicyManageListResponse>> queryClientPolicyList(ClientManageListRequest clientManageListRequest, Users users) {
        ResultObject<BasePageResponse<PolicyManageListResponse>> resultObject = new ResultObject<>();
        if (AssertUtils.isNotEmpty(clientManageListRequest.getKeyword())) {
            List<AgentKeyWordResponse> agentKeyWordResponseList = agentApi.loadAllAgentsByKeyword(clientManageListRequest.getKeyword()).getData();
            if (AssertUtils.isNotEmpty(agentKeyWordResponseList)) {
                List<String> agentIdList = agentKeyWordResponseList.stream().map(AgentKeyWordResponse::getAgentId).distinct().collect(Collectors.toList());
                clientManageListRequest.setServiceAgentIds(agentIdList);
            }
        }
        List<PolicyManageListBo> policyManageListBos = clientManagerBaseService.queryClientPolicyList(clientManageListRequest);

        //国际化查询
        List<String> codeTypes = new ArrayList<>();
        codeTypes.add(DATE_TYPE.name());
        ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(users.getLanguage(), codeTypes);
        mapSyscodeList = mapResultObject.getData();

        if (!AssertUtils.isNotEmpty(policyManageListBos)) {
            return resultObject;
        }
        List<PolicyManageListResponse> policyManageListResponses = (List<PolicyManageListResponse>) this.converterList(policyManageListBos, new TypeToken<List<PolicyManageListResponse>>() {
        }.getType());
        this.transPolicyList(clientManageListRequest, resultObject, policyManageListResponses);
        return resultObject;
    }

    /**
     * 签收/取消保单
     *
     * @param policyId   保单
     * @param cancelFlag 取消标识
     * @param users      用户
     * @return Void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<Void> signPolicy(String policyId, String cancelFlag, Users users) {
        // 查询保单
        PolicyPo policyPo = policyDao.findById(policyId);
        AssertUtils.isNotNull(log, policyPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        if (TerminologyConfigEnum.WHETHER.YES.name().equals(cancelFlag)) {
            policyPo.setInputDate(null);
            policyPo.setInputUserId(null);
            if (PartyTermEnum.POLICY_INPUT_STATUS.ENTERING.name().equals(policyPo.getPolicyStatus()) || !AssertUtils.isNotEmpty(policyPo.getPolicyStatus())) {
                policyPo.setInputStatus(PartyTermEnum.POLICY_INPUT_STATUS.PENDING_ENTRY.name());
            }
        } else {
            policyPo.setInputDate(DateUtils.getCurrentTime());
            policyPo.setInputUserId(users.getUserId());
            policyPo.setInputStatus(PartyTermEnum.POLICY_INPUT_STATUS.ENTERING.name());
        }
        clientManagerBaseService.savePolicy(policyPo, users.getUserId());
        return ResultObject.success();
    }

    /**
     * 录入保单异常处理
     *
     * @param clientPolicyErrorRequest 保单ID
     * @param users                    用户
     * @return Void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<Void> errorPolicy(ClientPolicyErrorRequest clientPolicyErrorRequest, Users users) {
        // 查询保单
        String policyId = clientPolicyErrorRequest.getPolicyId();
        String remark = clientPolicyErrorRequest.getRemark();
        AssertUtils.isNotNull(log, policyId, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(log, remark, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_REMARK_IS_NOT_NULL);
        PolicyPo policyPo = policyDao.findById(policyId);
        AssertUtils.isNotNull(log, policyPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        policyPo.setInputStatus(PartyTermEnum.POLICY_INPUT_STATUS.ENTRY_EXCEPTION.name());
        clientManagerBaseService.savePolicy(policyPo, users.getUserId());

        PolicyOptionRecordPo policyOptionRecordPo = new PolicyOptionRecordPo();
        policyOptionRecordPo.setPolicyId(policyId);
        policyOptionRecordPo.setOptionUserId(users.getUserId());
        policyOptionRecordPo.setOptionTime(DateUtils.getCurrentTime());
        policyOptionRecordPo.setOptionCode(PartyTermEnum.POLICY_OPTION_CODE.APPLY.name());
        policyOptionRecordPo.setOptionResult(PartyTermEnum.POLICY_OPTION_RESULT.INPUT_ERROR.name());
        policyOptionRecordPo.setRemark(remark);
        clientManagerBaseService.savePolicyOptionRecord(policyOptionRecordPo, users.getUserId());
        return ResultObject.success();
    }

    /**
     * 查询保单详情
     *
     * @param policyId        保单ID
     * @param appRequestHeads
     * @return
     */
    @Override
    public ResultObject<ClientManagePolicyDetailResponse> detail(String policyId, AppRequestHeads appRequestHeads) {
        // 查询保单
        String language = appRequestHeads.getLanguage();

        PolicyPo policyPo = policyDao.findById(policyId);
        AssertUtils.isNotNull(log, policyPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        ClientManagePolicyDetailResponse policyResponse = new ClientManagePolicyDetailResponse();
        ClazzUtils.copyPropertiesIgnoreNull(policyPo, policyResponse);
        if (AssertUtils.isNotNull(policyPo.getEffectiveDate())) {
            policyResponse.setEffectiveDays(DateUtils.intervalDay(policyPo.getEffectiveDate(), DateUtils.getCurrentTime()));
        }
        // 查询保险公司
        ProviderPo providerPo = providerDao.findById(policyPo.getProviderId());
        if (AssertUtils.isNotNull(providerPo)) {
            policyResponse.setProviderName(providerPo.getProviderName());
            policyResponse.setLogoUrl(providerPo.getLogoUrl());
        }
        // 查询投保人
        PolicyApplicantPo policyApplicantPo = clientManagerBaseService.queryApplicant(policyId);
        if (AssertUtils.isNotNull(policyApplicantPo)) {
            policyResponse.setApplicantName(policyApplicantPo.getName());
            policyResponse.setApplicantFamilyName(policyApplicantPo.getFamilyName());
            policyResponse.setApplicantGivenName(policyApplicantPo.getGivenName());
            policyResponse.setApplicantSex(policyApplicantPo.getSex());
            policyResponse.setApplicantBirthday(policyApplicantPo.getBirthday());
            policyResponse.setApplicantIdType(policyApplicantPo.getIdType());
            policyResponse.setApplicantIdNo(policyApplicantPo.getIdNo());
            policyResponse.setApplicantMobile(policyApplicantPo.getMobile());
        }
        // 查询被保人
        List<PolicyInsuredPo> policyInsuredPos = policyInsuredDao.fetchByPolicyId(policyId);
//        AssertUtils.isNotEmpty(log, policyInsuredPos, PartyErrorConfigEnum.CLIENT_POLICY_BUSINESS_INSURED_IS_NOT_FOUND);
        if (AssertUtils.isNotEmpty(policyInsuredPos)) {
            PolicyInsuredPo policyInsuredPo = policyInsuredPos.get(0);
            policyResponse.setInsuredName(policyInsuredPo.getName());
            policyResponse.setInsuredFamilyName(policyInsuredPo.getFamilyName());
            policyResponse.setInsuredGivenName(policyInsuredPo.getGivenName());
            policyResponse.setInsuredSex(policyInsuredPo.getSex());
            policyResponse.setInsuredBirthday(policyInsuredPo.getBirthday());
            policyResponse.setInsuredIdType(policyInsuredPo.getIdType());
            policyResponse.setInsuredIdNo(policyInsuredPo.getIdNo());
            policyResponse.setRelationship(policyInsuredPo.getRelationship());
        }

        // 查询保障(缴费)期限国际化数据
        List<SyscodeResponse> periodSysCodes = platformInternationalBaseApi.queryInternational(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PRODUCT_PERIOD_AGE.name(), language).getData();
        List<SyscodeResponse> periodYearSysCodes = platformInternationalBaseApi.queryInternational(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PRODUCT_PERIOD_YEAR.name(), language).getData();
        periodSysCodes.addAll(periodYearSysCodes);
        // 查询险种
        List<PolicyCoveragePo> policyCoveragePos = clientManagerBaseService.listPolicyCoverage(policyId);
        if (AssertUtils.isNotEmpty(policyCoveragePos)) {
            Optional<PolicyCoveragePo> coverageOptional = policyCoveragePos.stream()
                    .filter(policyCoveragePo -> "MAIN".equals(policyCoveragePo.getPrimaryFlag()))
                    .findFirst();
            if (coverageOptional.isPresent()) {
                PolicyCoveragePo policyCoveragePo = coverageOptional.get();
                policyResponse.setProductName(policyCoveragePo.getProductName());
                policyResponse.setPremiumFrequency(policyCoveragePo.getPremiumFrequency());
                policyResponse.setPremiumPeriod(policyCoveragePo.getPremiumPeriod());
                if (AssertUtils.isNotEmpty(policyCoveragePo.getPremiumPeriod())) {
                    if (policyCoveragePo.getPremiumPeriod().contains("AGE_")) {
                        policyResponse.setPremiumPeriodType("PRODUCT_PERIOD_AGE");
                    } else {
                        policyResponse.setPremiumPeriodType("PRODUCT_PERIOD_YEAR");
                    }
                }
                periodSysCodes.stream()
                        .filter(syscode -> syscode.getCodeKey().equals(policyResponse.getPremiumPeriod()))
                        .findFirst().ifPresent(syscode -> policyResponse.setPremiumPeriodName(syscode.getCodeName()));
            }

            // 查询保障类别
            List<DutyClassPo> dutyClassPos = dutyClassDao.findAll();
            List<ClientPolicyCoverageResponse> policyCoverages = new ArrayList<>();
            // 设置险种保额
            policyCoveragePos.forEach(policyCoveragePo -> {
                if (AssertUtils.isNotNull(policyCoveragePo.getAmount()) && AssertUtils.isNotEmpty(policyCoveragePo.getMult())) {
                    policyCoveragePo.setAmount(policyCoveragePo.getAmount().multiply(new BigDecimal(policyCoveragePo.getMult())));
                }
                ClientPolicyCoverageResponse policyCoverageResponse = (ClientPolicyCoverageResponse) this.converterObject(policyCoveragePo, ClientPolicyCoverageResponse.class);
                periodSysCodes.stream()
                        .filter(syscode -> syscode.getCodeKey().equals(policyCoverageResponse.getCoveragePeriod()))
                        .findFirst().ifPresent(syscode -> policyCoverageResponse.setCoveragePeriodName(syscode.getCodeName()));
                policyCoverageResponse.setDutyClassId(policyCoveragePo.getCustomerDutyClass());
                dutyClassPos.stream()
                        .filter(dutyClassPo -> dutyClassPo.getDutyClassId().equals(policyCoverageResponse.getDutyClassId()))
                        .findFirst().ifPresent(dutyClassPo -> policyCoverageResponse.setDutyClassType(dutyClassPo.getDutyClassType()));
                if (AssertUtils.isNotEmpty(policyCoveragePo.getPremiumPeriod())) {
                    if (policyCoveragePo.getCoveragePeriod().contains("AGE_")) {
                        policyCoverageResponse.setCoveragePeriodType("PRODUCT_PERIOD_AGE");
                        policyCoverageResponse.setCoveragePeriodTypeName((language.equals(TerminologyConfigEnum.LANGUAGE.ZH_CN.name()) ? "至" : "To ") + policyCoverageResponse.getCoveragePeriodName());
                    } else {
                        policyCoverageResponse.setCoveragePeriodType("PRODUCT_PERIOD_YEAR");
                        policyCoverageResponse.setCoveragePeriodTypeName(policyCoverageResponse.getCoveragePeriodName());
                    }
                }
                policyCoverages.add(policyCoverageResponse);
            });
            policyResponse.setPolicyCoverages(policyCoverages);

            // 保单总保费
            BigDecimal totalAmount = policyCoveragePos.stream()
                    .filter(policyCoveragePo -> AssertUtils.isNotNull(policyCoveragePo.getAmount()))
                    .map(PolicyCoveragePo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            policyResponse.setTotalAmount(totalAmount);
        }

        // 查询电子保单
        PolicyAttachmentPo attachmentPo = clientManagerBaseService.queryOneAttachment(policyId);
        if (AssertUtils.isNotNull(attachmentPo)) {
            policyResponse.setAttachmentId(attachmentPo.getAttachmentId());
        }
        List<PictureBatchAttachmentPo> pictureBatchAttachmentPos = clientManagerBaseService.queryPictureBatchAttachment(policyPo.getPictureBatchId());
        if (AssertUtils.isNotEmpty(pictureBatchAttachmentPos)) {
            policyResponse.setAttachments((List<AttachmentResponse>) this.converterList(pictureBatchAttachmentPos, new org.modelmapper.TypeToken<List<AttachmentResponse>>() {
            }.getType()));
        }

        return ResultObject.<ClientManagePolicyDetailResponse>success().setData(policyResponse);
    }

    /**
     * 保单流程记录
     *
     * @param policyId 保单ID
     * @return 保单流程记录
     */
    @Override
    public ResultObject<List<PolicyOptionRecordResponse>> getOptionRecordList(String policyId) {
        ResultObject<List<PolicyOptionRecordResponse>> resultObject = new ResultObject<>();

        List<PolicyOptionRecordPo> policyOptionRecordPos = clientManagerBaseService.queryPolicyOptionRecord(policyId);
        if (!AssertUtils.isNotEmpty(policyOptionRecordPos)) {
            return resultObject;
        }
        List<PolicyOptionRecordResponse> policyOptionRecordResponses = (List<PolicyOptionRecordResponse>) this.converterList(policyOptionRecordPos, new org.modelmapper.TypeToken<List<PolicyOptionRecordResponse>>() {
        }.getType());
        List<String> optionUserIds = policyOptionRecordResponses.stream().map(PolicyOptionRecordResponse::getOptionUserId).distinct().collect(Collectors.toList());
        EmployeeQueryRequest employeeQueryRequest = new EmployeeQueryRequest();
        employeeQueryRequest.setListUserId(JackSonUtils.toJson(optionUserIds));
        ResultObject<List<EmployeResponse>> listResultObject = platformEmployeeApi.employeGet(employeeQueryRequest);

        policyOptionRecordResponses.forEach(policyOptionRecordResponse -> {
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                listResultObject.getData().stream().filter(employeResponse -> employeResponse.getEmployeId().equals(policyOptionRecordResponse.getOptionUserId()))
                        .findFirst().ifPresent(employeResponse -> policyOptionRecordResponse.setOptionUserName(employeResponse.getEmployeName()));
            }
            if (!AssertUtils.isNotEmpty(policyOptionRecordResponse.getOptionUserName())) {
                //变更前后
                ResultObject<AgentResponse> agentBefore = agentApi.agentByIdGet(policyOptionRecordResponse.getOptionUserId());
                if (!AssertUtils.isResultObjectDataNull(agentBefore)) {
                    policyOptionRecordResponse.setOptionUserName(agentBefore.getData().getAgentName() + "/" + agentBefore.getData().getAgentCode());
                }
            }
        });
        resultObject.setData(policyOptionRecordResponses);
        return resultObject;
    }

    /**
     * 保单提交
     *
     * @param clientPolicyEntryRequest 保单录入请求参数
     * @param users                    用户
     * @return Void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<Void> savePolicy(ClientPolicyEntryRequest clientPolicyEntryRequest, Users users) {
        // 参数校验
        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getProviderId(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PROVIDER_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getEffectiveDateFormat(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_EFFECTIVE_DATE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getPremium(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PREMIUM_IS_NOT_NULL);
        AssertUtils.isNotDigital(log, clientPolicyEntryRequest.getPremium(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PREMIUM_ERROR);
        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getPolicyCoverages(), ApplyErrorConfigEnum.APPLY_ACCEPT_PRODUCT_IS_NOT_NULL);
        //被保人
//        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getInsuredName(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_NAME_IS_NOT_NULL);
//        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getInsuredFamilyName(), PartyErrorConfigEnum.PARTY_CUSTOMER_FAMILY_NAME_IS_NOT_NULL);
//        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getInsuredGivenName(), PartyErrorConfigEnum.PARTY_CUSTOMER_GIVEN_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getInsuredBirthdayFormat(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_BIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getInsuredSex(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_SEX_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getInsuredIdType(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getInsuredIdNo(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, clientPolicyEntryRequest.getRelationship(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_RELATIONSHIP_IS_NOT_NULL);

        //险种
        clientPolicyEntryRequest.getPolicyCoverages().forEach(coverageRequest -> {
            AssertUtils.isNotEmpty(log, coverageRequest.getPrimaryFlag(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_PRIMARY_FLAG_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, coverageRequest.getDutyClassId(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_DUTY_CLASS_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, coverageRequest.getAmount(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_AMOUNT_IS_NOT_NULL);
            AssertUtils.isNotDigital(log, coverageRequest.getAmount(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_AMOUNT_ERROR);
            AssertUtils.isNotEmpty(log, coverageRequest.getCoveragePeriod(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_COVERAGE_PERIOD_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, coverageRequest.getProductName(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_NAME_IS_NOT_NULL);
        });
        Optional<PolicyCoverageRequest> optional = clientPolicyEntryRequest.getPolicyCoverages().stream()
                .filter(coverageRequest -> PartyTermEnum.PRIMARY_FLAG.MAIN.name().equals(coverageRequest.getPrimaryFlag()))
                .findFirst();
        if (!optional.isPresent()) {
            throw new RequestException(PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_MAIN_IS_NOT_NULL);
        }

        String policyId = clientPolicyEntryRequest.getPolicyId();
        // 查询保单
        PolicyPo policyPo = policyDao.findById(policyId);
        AssertUtils.isNotNull(log, policyPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_POLICY_IS_NOT_FOUND);

        // 查询被保人
        PolicyInsuredPo policyInsuredPo = new PolicyInsuredPo();
        List<PolicyInsuredPo> policyInsuredPos = policyInsuredDao.fetchByPolicyId(policyId);
        if (AssertUtils.isNotEmpty(policyInsuredPos)) {
            policyInsuredPo = policyInsuredPos.get(0);
        }
        // 查询险种
        List<PolicyCoveragePo> coveragePos = customerBaseService.listPolicyCoverage(policyId);
        if (AssertUtils.isNotEmpty(coveragePos)) {
            policyCoverageDao.delete(coveragePos);
        }
        // 查询电子保单
        PolicyAttachmentPo attachmentPo = customerBaseService.queryOneAttachment(policyId);
        if (AssertUtils.isNotNull(attachmentPo)) {
            policyAttachmentDao.delete(attachmentPo);
        }
        ClazzUtils.copyPropertiesIgnoreNull(clientPolicyEntryRequest, policyPo);
        policyPo.setInputUserId(users.getUserId());
        boolean editFlag = PartyTermEnum.POLICY_INPUT_STATUS.ENTERED.name().equals(policyPo.getPolicyStatus());

        policyPo.setInputStatus(PartyTermEnum.POLICY_INPUT_STATUS.ENTERED.name());
        policyPo.setPremium(new BigDecimal(clientPolicyEntryRequest.getPremium()));
        long effectiveDate = DateUtils.stringToTime(clientPolicyEntryRequest.getEffectiveDateFormat(), DateUtils.FORMATE18);
        policyPo.setEffectiveDate(effectiveDate);

        // 查询客户信息
        CustomerAgentPo customerAgentPo = new CustomerAgentPo();
        if (AssertUtils.isNotEmpty(clientPolicyEntryRequest.getInsuredCustomerId())) {
            customerAgentPo = customerBaseService.queryOneCustomerAgent(clientPolicyEntryRequest.getInsuredCustomerId());
            AssertUtils.isNotNull(log, customerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
            if (!AssertUtils.isNotEmpty(customerAgentPo.getFamilyName()) || !AssertUtils.isNotEmpty(customerAgentPo.getGivenName())) {
                customerAgentPo.setName(clientPolicyEntryRequest.getInsuredFamilyName() + " " + clientPolicyEntryRequest.getInsuredGivenName());
                customerAgentPo.setFamilyName(clientPolicyEntryRequest.getInsuredFamilyName());
                customerAgentPo.setGivenName(clientPolicyEntryRequest.getInsuredGivenName());
            }
            // 保存被保人
            ClazzUtils.copyPropertiesIgnoreNull(customerAgentPo, policyInsuredPo);
        } else {
            policyInsuredPo.setName(clientPolicyEntryRequest.getInsuredFamilyName() + " " + clientPolicyEntryRequest.getInsuredGivenName());
            policyInsuredPo.setFamilyName(clientPolicyEntryRequest.getInsuredFamilyName());
            policyInsuredPo.setGivenName(clientPolicyEntryRequest.getInsuredGivenName());
            policyInsuredPo.setSex(clientPolicyEntryRequest.getInsuredSex());
            policyInsuredPo.setBirthday(DateUtils.stringToTime(clientPolicyEntryRequest.getInsuredBirthdayFormat(), DateUtils.FORMATE18));
            policyInsuredPo.setIdType(clientPolicyEntryRequest.getInsuredIdType());
            policyInsuredPo.setIdNo(clientPolicyEntryRequest.getInsuredIdNo());
        }
        policyInsuredPo.setRelationship(clientPolicyEntryRequest.getRelationship());
        policyInsuredPo.setPolicyId(policyPo.getPolicyId());

        UserCustomerRequest userCustomerRequest = new UserCustomerRequest();
        userCustomerRequest.setCustomerId(clientPolicyEntryRequest.getInsuredCustomerId());
        userCustomerRequest.setFamilyName(clientPolicyEntryRequest.getInsuredFamilyName());
        userCustomerRequest.setGivenName(clientPolicyEntryRequest.getInsuredGivenName());
        userCustomerRequest.setSex(clientPolicyEntryRequest.getInsuredSex());
        userCustomerRequest.setBirthdayFormat(clientPolicyEntryRequest.getInsuredBirthdayFormat());
        userCustomerRequest.setIdType(clientPolicyEntryRequest.getInsuredIdType());
        userCustomerRequest.setIdNo(clientPolicyEntryRequest.getInsuredIdNo());
        userCustomerRequest.setRelationship(clientPolicyEntryRequest.getRelationship());
        // 查询投保人
        PolicyApplicantPo policyApplicantPo = customerBaseService.queryApplicant(policyId);
        AssertUtils.isNotNull(this.getLogger(), policyApplicantPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_APPLICANT_IS_NOT_FOUND);
        userCustomerRequest.setOneselfCustomerId(policyApplicantPo.getCustomerId());
        // 校验并保存客户
        customerBaseTransfer.checkAndSaveCustomer(userCustomerRequest, customerAgentPo, users.getUserId());
        // 保存家庭成员关系
        if (!PartyTermEnum.RELATIONSHIP.ONESELF.name().equals(userCustomerRequest.getRelationship())) {
            customerBaseTransfer.saveCustomerRelationship(userCustomerRequest.getCustomerId(), userCustomerRequest.getOneselfCustomerId(),
                    customerAgentPo.getCustomerAgentId(), userCustomerRequest.getRelationship(), users.getUserId());
        }

        // 保存被保人
        policyInsuredPo.setCustomerId(customerAgentPo.getCustomerAgentId());
        customerBaseService.savePolicyInsured(policyInsuredPo, users.getUserId());

        // 保存电子保单
        if (AssertUtils.isNotEmpty(clientPolicyEntryRequest.getAttachmentId())) {
            PolicyAttachmentPo policyAttachmentPo = new PolicyAttachmentPo();
            policyAttachmentPo.setAttachmentId(clientPolicyEntryRequest.getAttachmentId());
            policyAttachmentPo.setPolicyId(policyPo.getPolicyId());
            customerBaseService.savePolicyAttachment(policyAttachmentPo, users.getUserId());
        }
        // 保存险种
        List<PolicyCoveragePo> policyCoveragePos = new ArrayList<>();
        PolicyInsuredPo finalPolicyInsuredPo = policyInsuredPo;
        clientPolicyEntryRequest.getPolicyCoverages().forEach(policyCoverageRequest -> {
            PolicyCoveragePo policyCoveragePo = (PolicyCoveragePo) this.converterObject(policyCoverageRequest, PolicyCoveragePo.class);
            Long endDate = customerBaseTransfer.calculateEndDate(effectiveDate, policyCoveragePo.getCoveragePeriod(), finalPolicyInsuredPo.getBirthday());
            policyCoveragePo.setProductName(policyCoverageRequest.getProductName());
            if (PartyTermEnum.PRIMARY_FLAG.MAIN.name().equals(policyCoveragePo.getPrimaryFlag())) {
                policyPo.setMaturityDate(endDate);
                if (AssertUtils.isNotNull(endDate) && endDate <= DateUtils.getCurrentTime()) {
                    policyPo.setPolicyStatus(PartyTermEnum.CLIENT_POLICY_STATUS.INVALID.name());
                }
            }
            if (AssertUtils.isNotEmpty(clientPolicyEntryRequest.getPremiumPeriod())) {
                policyCoveragePo.setPremiumPeriod(clientPolicyEntryRequest.getPremiumPeriod());
            }
            policyCoveragePo.setMult("1");
            policyCoveragePo.setPremiumFrequency(clientPolicyEntryRequest.getPremiumFrequency());
            policyCoveragePo.setEffectiveDate(effectiveDate);
            policyCoveragePo.setCoveragePeriodStartDate(effectiveDate);
            policyCoveragePo.setCoveragePeriodEndDate(endDate);
            policyCoveragePo.setMaturityDate(endDate);
            policyCoveragePo.setPolicyId(policyPo.getPolicyId());
            policyCoveragePo.setInsuredId(finalPolicyInsuredPo.getInsuredId());
            policyCoveragePo.setCustomerDutyClass(policyCoverageRequest.getDutyClassId());
            policyCoveragePo.setAmount(new BigDecimal(policyCoverageRequest.getAmount()));
            policyCoveragePos.add(policyCoveragePo);
        });
        customerBaseService.addPolicyCoverage(policyCoveragePos, users.getUserId());
        customerBaseService.savePolicy(policyPo, users.getUserId());

        PolicyOptionRecordPo policyOptionRecordPo = new PolicyOptionRecordPo();
        policyOptionRecordPo.setPolicyId(policyId);
        policyOptionRecordPo.setOptionUserId(users.getUserId());
        policyOptionRecordPo.setOptionTime(DateUtils.getCurrentTime());
        if (editFlag) {
            policyOptionRecordPo.setOptionCode(PartyTermEnum.POLICY_OPTION_CODE.EDIT.name());
            policyOptionRecordPo.setOptionResult(PartyTermEnum.POLICY_OPTION_RESULT.SAVE_SUCCESS.name());
        } else {
            policyOptionRecordPo.setOptionCode(PartyTermEnum.POLICY_OPTION_CODE.SUBMIT.name());
            policyOptionRecordPo.setOptionResult(PartyTermEnum.POLICY_OPTION_RESULT.INPUT_COMPLETE.name());
        }
        clientManagerBaseService.savePolicyOptionRecord(policyOptionRecordPo, users.getUserId());

        // 查询本人
        CustomerAgentPo oneselfCustomerAgentPo = customerBaseService.queryOneCustomerAgent(userCustomerRequest.getOneselfCustomerId());
        // 保存client客户及关系数据
        CustomerAndRelationshipRequest customerAndRelationshipRequest = new CustomerAndRelationshipRequest();
        CustomerRequest oneselfCustomerRequest = new CustomerRequest();
        ClazzUtils.copyPropertiesIgnoreNull(oneselfCustomerAgentPo, oneselfCustomerRequest);
        oneselfCustomerRequest.setCustomerId(oneselfCustomerAgentPo.getCustomerAgentId());
        oneselfCustomerRequest.setHeadUrl(oneselfCustomerAgentPo.getAvatar());
        customerAndRelationshipRequest.setOneselfCustomer(oneselfCustomerRequest);
        CustomerRequest customerRequest = new CustomerRequest();
        ClazzUtils.copyPropertiesIgnoreNull(customerAgentPo, customerRequest);
        customerRequest.setCustomerId(customerAgentPo.getCustomerAgentId());
        customerRequest.setHeadUrl(customerAgentPo.getAvatar());
        customerAndRelationshipRequest.setCustomer(customerRequest);
        if (!PartyTermEnum.RELATIONSHIP.ONESELF.name().equals(userCustomerRequest.getRelationship())) {
            customerAndRelationshipRequest.setRelationship(userCustomerRequest.getRelationship());
        }
        clientCustomerBaseApi.saveCustomerAndRelationship(customerAndRelationshipRequest);

        return ResultObject.success();
    }

    /**
     * 查询保险公司
     *
     * @return ProviderResponses
     */
    @Override
    public ResultObject<List<ProviderResponse>> listProvider() {
        List<ProviderPo> providerPos = providerDao.findAll();
        List<ProviderResponse> providerResponses = (List<ProviderResponse>) this.converterList(
                providerPos, new org.modelmapper.TypeToken<List<ProviderResponse>>() {
                }.getType()
        );
        return ResultObject.<List<ProviderResponse>>success().setData(providerResponses);
    }

    /**
     * 查询保障类别库
     *
     * @return DutyClassResponses
     */
    @Override
    public ResultObject<List<DutyClassResponse>> listDutyClass() {
        List<DutyClassPo> dutyClassPos = dutyClassDao.findAll();
        List<DutyClassResponse> dutyClassResponses = (List<DutyClassResponse>) this.converterList(
                dutyClassPos, new TypeToken<List<DutyClassResponse>>() {
                }.getType()
        );
        return ResultObject.<List<DutyClassResponse>>success().setData(dutyClassResponses);
    }

    /**
     * 该保单下的家庭成员列表
     *
     * @param policyId
     * @param keyword
     * @return
     */
    @Override
    public ResultObject<List<ClientCustomerListResponse>> listMember(String policyId, String keyword) {
        ResultObject<List<ClientCustomerListResponse>> resultObject = new ResultObject<>();

        AssertUtils.isNotNull(log, policyId, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
        PolicyPo policyPo = policyDao.findById(policyId);
        AssertUtils.isNotNull(log, policyPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        PolicyApplicantPo policyApplicantPo = clientManagerBaseService.queryApplicant(policyId);
        AssertUtils.isNotNull(log, policyApplicantPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        ResultObject<List<MemberResponse>> listResultObject = customerManageService.listMember(policyApplicantPo.getCustomerId(), keyword);
        if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
            List<ClientCustomerListResponse> clientCustomerListResponses = (List<ClientCustomerListResponse>) this.converterList(
                    listResultObject.getData(), new TypeToken<List<ClientCustomerListResponse>>() {
                    }.getType()
            );
            resultObject.setData(clientCustomerListResponses);
        }

        return resultObject;
    }

    private void transPolicyList(ClientManageListRequest clientPolicyListRequest, ResultObject<BasePageResponse<PolicyManageListResponse>> resultObject, List<PolicyManageListResponse> policyManageListResponses) {
        List<String> inputUserIds = policyManageListResponses.stream().map(PolicyManageListResponse::getInputUserId).distinct().collect(Collectors.toList());
        ResultObject<List<UserResponse>> applyUsersPoByIds = platformUsersBaseApi.queryUsersPoByIds(inputUserIds);

        List<String> agentIds = policyManageListResponses.stream().map(PolicyManageListResponse::getServiceAgentId).distinct().collect(Collectors.toList());
        AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
        applyAgentRequest.setListAgentId(agentIds);
        ResultObject<List<AgentResponse>> listResultObject = agentApi.agentsGet(applyAgentRequest);

        policyManageListResponses.forEach(policyManageListResponse -> {
            policyManageListResponse.setWaitTime(DateUtils.getCurrentTime() - policyManageListResponse.getSubmitDate());
            policyManageListResponse.setWaitTimeFormat(alwaysFormat(policyManageListResponse.getSubmitDate(), DateUtils.getCurrentTime()));
            if (!AssertUtils.isResultObjectListDataNull(applyUsersPoByIds)) {
                applyUsersPoByIds.getData().stream().filter(userResponse -> userResponse.getUserId().equals(policyManageListResponse.getInputUserId()))
                        .findFirst().ifPresent(employeResponse -> policyManageListResponse.setInputUserName(employeResponse.getName()));
            }
            //保障顾问
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                listResultObject.getData().stream().filter(agentResponse -> agentResponse.getAgentId().equals(policyManageListResponse.getServiceAgentId()))
                        .findFirst().ifPresent(agentResponse -> {
                    policyManageListResponse.setServiceAgentCode(agentResponse.getAgentCode());
                    policyManageListResponse.setServiceAgentName(agentResponse.getAgentName());
                    policyManageListResponse.setServiceAgentNameCode(agentResponse.getAgentName() + "/" + agentResponse.getAgentCode());
                });
            }
        });

        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(policyManageListResponses) ? policyManageListResponses.get(0).getTotalLine() : null;
        BasePageResponse<PolicyManageListResponse> basePageResponse = BasePageResponse.getData(clientPolicyListRequest.getCurrentPage(), clientPolicyListRequest.getPageSize(), totalLine, policyManageListResponses);
        resultObject.setData(basePageResponse);
    }

    private String alwaysFormat(Long startTime, Long endTime) {
        endTime = AssertUtils.isNotNull(endTime) ? endTime : DateUtils.getCurrentTime();
        if (AssertUtils.isNotNull(startTime) && AssertUtils.isNotNull(endTime) && endTime.longValue() > startTime.longValue()) {
            String day = LanguageUtils.getCodeName(mapSyscodeList.get(DATE_TYPE.name()), "DAY");
            String hour = LanguageUtils.getCodeName(mapSyscodeList.get(DATE_TYPE.name()), "HOUR");
            long underwriteAlways = endTime - startTime;
            long dayl = 1 * 24 * 60 * 60 * 1000l;
            day = underwriteAlways > dayl ? underwriteAlways / dayl + day : "";
            hour = ((underwriteAlways - (underwriteAlways / dayl) * dayl) / (60 * 60 * 1000l)) + hour;
            return day + hour;
        }
        return "";

    }
}

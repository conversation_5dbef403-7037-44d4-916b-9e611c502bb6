package com.gclife.party.service.impl;

import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.party.core.jooq.tables.daos.ProtectionCustomerDao;
import com.gclife.party.core.jooq.tables.pojos.ProtectionCustomerPo;
import com.gclife.party.model.request.ProtectionCustomerRequest;
import com.gclife.party.model.response.ProtectionCustomerResponse;
import com.gclife.party.service.ProtectionCustomerService;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import static com.gclife.party.model.config.PartyErrorConfigEnum.*;

/**
 * <AUTHOR>
 * create 2021-04-29
 * description: 特别保护表格
 */
@Service
public class ProtectionCustomerServiceImpl extends BaseBusinessServiceImpl implements ProtectionCustomerService {

    @Autowired
    private ProtectionCustomerDao protectionCustomerDao;

    @Autowired
    private AttachmentApi attachmentApi;

    @Override
    public void exportProtectionCustomer(HttpServletResponse httpServletResponse) {
        //由输入流得到工作簿
        ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentApi.templateGet("PROTECTION_CUSTOMER_EXPORT_TEMPLATE");
        AssertUtils.isResultObjectDataNull(this.getLogger(), attachmentRespFcResultObject);
        AttachmentResponse attachmentRespFc = attachmentRespFcResultObject.getData();
        try {
            URL url = new URL(attachmentRespFc.getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            boolean booleanWhile = true;
            XSSFSheet sheet = workbook.getSheetAt(0);

            List<ProtectionCustomerResponse> protectionCustomerResponses = this.queryProtectionCustomer();

            if (AssertUtils.isNotEmpty(protectionCustomerResponses)) {
                int number = 1;
                for (int i = 0; i < protectionCustomerResponses.size(); i++) {
                    ProtectionCustomerResponse protectionCustomerResponse = protectionCustomerResponses.get(i);
                    Row writeRow = sheet.getRow(number);
                    if (!AssertUtils.isNotNull(writeRow)) {
                        Row sheetRow = sheet.getRow(number - 1);
                        writeRow = sheet.createRow(number);
                        copyRow(workbook, sheet, sheetRow, writeRow, false);
                    }
                    //writeRow.getCell(0).setCellValue(i + 1);
                    Long createdDate = protectionCustomerResponse.getCreatedDate();
                    writeRow.getCell(0).setCellValue(DateUtils.timeStrToString(createdDate, DateUtils.FORMATE19));
                    String customerName = protectionCustomerResponse.getCustomerName();
                    writeRow.getCell(1).setCellValue(customerName);
                    String mobile = protectionCustomerResponse.getMobile();
                    writeRow.getCell(2).setCellValue(mobile);
                    String chiefBranch = protectionCustomerResponse.getChiefBranch();
                    writeRow.getCell(3).setCellValue(chiefBranch);
                    number++;
                }


            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(attachmentRespFc.getTemplateName() + ".xlsx", "UTF-8"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(PARTY_EXPORT_PROTECTION_CUSTOMER_ERROR);
        }
    }

    @Transactional
    @Override
    public ResultObject saveProtectionCustomer(ProtectionCustomerRequest protectionCustomerRequest) {
        ResultObject resultObject = new ResultObject();
        if (!AssertUtils.isNotEmpty(protectionCustomerRequest.getCustomerName())) {
            throw new RequestException(PROTECTION_CUSTOMER_NAME_IS_NOT_NULL);
        }

        if (!AssertUtils.isNotEmpty(protectionCustomerRequest.getMobile())) {
            throw new RequestException(PROTECTION_CUSTOMER_MOBILE_IS_NOT_NULL);
        }

        if (!AssertUtils.isNotEmpty(protectionCustomerRequest.getChiefBranch())) {
            throw new RequestException(PROTECTION_CUSTOMER_ChIEF_BRANCH_IS_NOT_NULL);
        }

        ProtectionCustomerPo protectionCustomerPo = (ProtectionCustomerPo) this.converterObject(protectionCustomerRequest, ProtectionCustomerPo.class);
        protectionCustomerPo.setProtectionCustomerId(UUIDUtils.getUUIDShort());

        protectionCustomerPo.setCreatedDate(new Date().getTime());

        protectionCustomerDao.insert(protectionCustomerPo);

        return resultObject;
    }


    public List<ProtectionCustomerResponse> queryProtectionCustomer() {
        List<ProtectionCustomerResponse> protectionCustomerResponses = new ArrayList<>();
        List<ProtectionCustomerPo> protectionCustomerPoList = protectionCustomerDao.findAll();
        protectionCustomerPoList.forEach(protectionCustomerPo -> {
            ProtectionCustomerResponse protectionCustomerResponse = (ProtectionCustomerResponse) this.converterObject(protectionCustomerPo, ProtectionCustomerResponse.class);
            protectionCustomerResponses.add(protectionCustomerResponse);
        });
        return protectionCustomerResponses;
    }

    public static void copyRow(Workbook wb, Sheet sheet, Row fromRow, Row toRow, boolean copyValueFlag) {
        toRow.setHeight(fromRow.getHeight());
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress cellRangeAddress = sheet.getMergedRegion(i);
            if (cellRangeAddress.getFirstRow() == fromRow.getRowNum()) {
                CellRangeAddress newCellRangeAddress = new CellRangeAddress(toRow.getRowNum(), (toRow.getRowNum() + (cellRangeAddress.getLastRow() - cellRangeAddress.getFirstRow())), cellRangeAddress.getFirstColumn(), cellRangeAddress.getLastColumn());
                sheet.addMergedRegion(newCellRangeAddress);
            }
        }
        for (Iterator cellIt = fromRow.cellIterator(); cellIt.hasNext(); ) {
            Cell tmpCell = (Cell) cellIt.next();
            Cell newCell = toRow.createCell(tmpCell.getColumnIndex());
            copyCell(wb, tmpCell, newCell, copyValueFlag);
        }
    }

    public static void copyCell(Workbook wb, Cell srcCell, Cell distCell, boolean copyValueFlag) {
        CellStyle newstyle = wb.createCellStyle();
        newstyle.cloneStyleFrom(srcCell.getCellStyle());
        distCell.setCellStyle(newstyle);
        if (srcCell.getCellComment() != null) {
            distCell.setCellComment(srcCell.getCellComment());
        }
        int srcCellType = srcCell.getCellType();
        distCell.setCellType(srcCellType);
        if (copyValueFlag) {
            if (srcCellType == HSSFCell.CELL_TYPE_NUMERIC) {
                if (HSSFDateUtil.isCellDateFormatted(srcCell)) {
                    distCell.setCellValue(srcCell.getDateCellValue());
                } else {
                    distCell.setCellValue(srcCell.getNumericCellValue());
                }
            } else if (srcCellType == HSSFCell.CELL_TYPE_STRING) {
                distCell.setCellValue(srcCell.getRichStringCellValue());
            } else if (srcCellType == HSSFCell.CELL_TYPE_BLANK) {
            } else if (srcCellType == HSSFCell.CELL_TYPE_BOOLEAN) {
                distCell.setCellValue(srcCell.getBooleanCellValue());
            } else if (srcCellType == HSSFCell.CELL_TYPE_ERROR) {
                distCell.setCellErrorValue(srcCell.getErrorCellValue());
            } else if (srcCellType == HSSFCell.CELL_TYPE_FORMULA) {
                distCell.setCellFormula(srcCell.getCellFormula());
            } else {
            }
        }
    }
}

package com.gclife.party.service;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.party.model.request.client.ClientChangeServiceRequest;
import com.gclife.party.model.request.client.ClientManageListRequest;
import com.gclife.party.model.response.client.ClientChangeRecordResponse;
import com.gclife.party.model.response.client.ClientDicResponse;
import com.gclife.party.model.response.client.ClientManageDetailResponse;
import com.gclife.party.model.response.client.ClientManageListResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 2022/8/10 9:43
 * description:
 */
public interface ClientAppManageService {

    /**
     * 客户管理列表
     *
     * @param clientManageListRequest 列表请求
     * @return ClientManageListResponses
     */
    ResultObject<BasePageResponse<ClientManageListResponse>> queryClientManageList(ClientManageListRequest clientManageListRequest);

    /**
     * 客户信息详情
     *
     * @param customerAgentId 客户ID
     * @return ClientManageDetailResponse
     */
    ResultObject<ClientManageDetailResponse> queryClientInfoDetail(String customerAgentId);

    /**
     * 变更保障顾问
     *
     * @param clientChangeServiceRequest 变更保障顾问
     * @param users                      用户
     * @return Void
     */
    ResultObject<Void> postChangeService(ClientChangeServiceRequest clientChangeServiceRequest, Users users);

    /**
     * 异动列表
     *
     * @param customerAgentId 客户ID
     * @param appRequestHeads 请求头
     * @return 异动列表
     */
    ResultObject<List<ClientChangeRecordResponse>> getChangeRecordList(String customerAgentId, AppRequestHeads appRequestHeads);

    /**
     * 字典数据
     *
     * @param appRequestHeads 请求头
     * @return ClientDicResponse
     */
    ResultObject<ClientDicResponse> getDic(AppRequestHeads appRequestHeads);
}

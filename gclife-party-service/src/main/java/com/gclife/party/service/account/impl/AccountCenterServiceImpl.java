package com.gclife.party.service.account.impl;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.party.model.bo.FundAccountOverviewBo;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.response.account.FundAccountOverviewResponse;
import com.gclife.party.model.response.account.TermResponse;
import com.gclife.party.service.account.AccountCenterService;
import com.gclife.party.service.base.UserAccountBaseService;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2019-07-12 08:45
 */
@Service
public class AccountCenterServiceImpl extends BaseBusinessServiceImpl implements AccountCenterService {

    @Autowired
    private UserAccountBaseService userAccountBaseService;

    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    @Override
    public ResultObject<FundAccountOverviewResponse> fundAccountOverview(Users currentLoginUsers) {
        ResultObject resultObject = new ResultObject();
        FundAccountOverviewBo fundAccountOverviewBo = userAccountBaseService.fundAccountOverview();
        BigDecimal availableAmountSum = fundAccountOverviewBo.getResidueAmountSum().subtract(fundAccountOverviewBo.getDisableAmountSum()).setScale(2, BigDecimal.ROUND_HALF_UP);
        fundAccountOverviewBo.setAvailableAmountSum(availableAmountSum);
        FundAccountOverviewResponse fundAccountOverviewResponse = (FundAccountOverviewResponse) this.converterObject(fundAccountOverviewBo, FundAccountOverviewResponse.class);
        resultObject.setData(fundAccountOverviewResponse);
        return resultObject;
    }


    @Override
    public ResultObject<TermResponse> term(Users currentLoginUsers) {
        ResultObject<TermResponse> resultObject = new ResultObject<>();
        TermResponse termResponse = new TermResponse();
        List<SyscodeResponse> accountStatusTerm = platformInternationalBaseApi.queryInternational(PartyTermEnum.ACCOUNT_STATUS.ACCOUNT_STATUS.name(), null).getData();
        //去掉未激活的
        List<String> accountStatusList = new ArrayList<>();
        accountStatusList.add(PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name());
        accountStatusList.add(PartyTermEnum.ACCOUNT_STATUS.FROZEN.name());
        if(AssertUtils.isNotEmpty(accountStatusTerm)){
            accountStatusTerm = accountStatusTerm.stream().filter(a-> accountStatusList.contains(a.getCodeKey())).collect(Collectors.toList());
        }
        termResponse.setAccountStatusTerm(accountStatusTerm);
        resultObject.setData(termResponse);
        return resultObject;
    }
}

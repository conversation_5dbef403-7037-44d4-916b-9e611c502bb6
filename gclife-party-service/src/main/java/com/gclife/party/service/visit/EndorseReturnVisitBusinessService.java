package com.gclife.party.service.visit;

import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseService;
import com.gclife.party.model.request.visit.ReturnVisitPageRequest;

/**
 * @Auther: chenjinrong
 * @Date: 19-10-23 17:41
 * @Description:
 */
public interface EndorseReturnVisitBusinessService extends BaseService {

    ResultObject getEndorseReturnVisitList(ReturnVisitPageRequest pageRequest);
}

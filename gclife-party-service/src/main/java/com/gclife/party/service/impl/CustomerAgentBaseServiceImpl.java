package com.gclife.party.service.impl;

import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.party.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.party.dao.CustomerAgentBaseDao;
import com.gclife.party.model.response.CustomerAgentsResponse;
import com.gclife.party.service.CustomerAgentBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/6/1
 */
@Service
public class CustomerAgentBaseServiceImpl extends BaseBusinessServiceImpl implements CustomerAgentBaseService {

    @Autowired
    private CustomerAgentBaseDao customerAgentBaseDao;

    @Override
    public ResultObject<CustomerAgentsResponse> getBaseCustomerAgent(String customerAgentId) {
        ResultObject<CustomerAgentsResponse> resultObject = new ResultObject<>();
        CustomerAgentPo customerAgentPo = customerAgentBaseDao.getCustomerAgentByPK(customerAgentId);
        CustomerAgentsResponse customerAgentsResponse = null;
        if (AssertUtils.isNotNull(customerAgentPo)) {
            customerAgentsResponse = (CustomerAgentsResponse) converterObject(customerAgentPo, CustomerAgentsResponse.class);
        }

        resultObject.setData(customerAgentsResponse);
        return resultObject;
    }
}

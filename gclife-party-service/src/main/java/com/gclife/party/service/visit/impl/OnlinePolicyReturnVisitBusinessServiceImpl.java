package com.gclife.party.service.visit.impl;

import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.*;
import com.gclife.party.core.jooq.tables.daos.ReturnVisitAttachmentDao;
import com.gclife.party.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.party.core.jooq.tables.pojos.ReturnVisitAttachmentPo;
import com.gclife.party.core.jooq.tables.pojos.ReturnVisitChangePo;
import com.gclife.party.core.jooq.tables.pojos.ReturnVisitPo;
import com.gclife.party.dao.ReturnVisitAttachmentBaseDao;
import com.gclife.party.dao.ReturnVisitExtDao;
import com.gclife.party.model.bo.ReturnVisitBo;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.visit.PolicyReturnVisitAddRequest;
import com.gclife.party.model.request.visit.PolicyReturnVisitAttachmentRequest;
import com.gclife.party.model.request.visit.PolicyReturnVisitSubmitRequest;
import com.gclife.party.model.request.visit.ReturnVisitPageRequest;
import com.gclife.party.model.response.visit.*;
import com.gclife.party.service.MessageBusinessService;
import com.gclife.party.service.base.ReturnVisitBaseService;
import com.gclife.party.service.visit.OnlinePolicyReturnVisitBusinessService;
import com.gclife.platform.api.PlatformUsersApi;
import com.gclife.platform.model.response.UserInfoResponse;
import com.gclife.policy.model.response.PolicyResponse;
import com.gclife.policy.model.vo.PolicyAgentVo;
import com.gclife.policy.model.vo.PolicyCoverageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.codehaus.janino.IClass;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyTermEnum.RETURN_VISIT_TYPE.*;
import static com.gclife.party.model.config.PartyTermEnum.SOURCE_TYPE.BROWER;
import static com.gclife.party.model.config.PartyTermEnum.SOURCE_TYPE.CLIENT;

@Slf4j
@Service
public class OnlinePolicyReturnVisitBusinessServiceImpl extends BaseBusinessServiceImpl implements OnlinePolicyReturnVisitBusinessService {

    @Autowired
    private ReturnVisitBaseService returnVisitBaseService;

    @Autowired
    private MessageBusinessService messageBusinessService;

    @Autowired
    private ReturnVisitAttachmentDao returnVisitAttachmentDao;

    @Autowired
    private ReturnVisitAttachmentBaseDao returnVisitAttachmentBaseDao;

    @Autowired
    private ReturnVisitExtDao returnVisitExtDao;

    @Autowired
    private PlatformUsersApi platformUsersApi;

    @Autowired
    private AttachmentApi attachmentApi;

    @Override
    public ResultObject addOnlinePolicyReturnVisit(Users users, PolicyReturnVisitAddRequest returnVisitSubmitRequest) {
        ResultObject resultObject = ResultObject.success();
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getBusinessType(), PartyErrorConfigEnum.PARTY_PARAMETER_BUSINESS_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getReturnVisitType(), PartyErrorConfigEnum.PARTY_PARAMETER_RETURN_VISIT_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getFamilyName(), PartyErrorConfigEnum.PARTY_CUSTOMER_FAMILY_NAME_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getGivenName(), PartyErrorConfigEnum.PARTY_CUSTOMER_GIVEN_NAME_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getOnlineMobile(), PartyErrorConfigEnum.PARTY_CUSTOMER_MOBILE_IS_NOT_NULL);
        if (!ONLINE_NON_STANDARD_HEALTH_NOTIFICATION.name().equals(returnVisitSubmitRequest.getReturnVisitType())) {
            AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getApplyAmount(), PartyErrorConfigEnum.PARTY_USER_ACCOUNT_AMOUNT_IS_NOT_NULL);
        }
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getSourceType(), PartyErrorConfigEnum.PARTY_SOURCE_TYPE_IS_NOT_NULL);
        if (CLIENT.name().equals(returnVisitSubmitRequest.getSourceType())) {
            AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getConsultantName(), PartyErrorConfigEnum.PARTY_CONSULTANT_NAME_IS_NOT_NULL);
        }
        log.info("网销申请其他保额的returnVisitSubmitRequest参数："+ JackSonUtils.toJson(returnVisitSubmitRequest));

        if (!AssertUtils.isNotNull(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.fromCode(returnVisitSubmitRequest.getBusinessType()))) {
            throwsException(PartyErrorConfigEnum.PARTY_RETURN_BUSINESS_TYPE_NOT_EXIST);
        }
        if (!AssertUtils.isNotNull(PartyTermEnum.RETURN_VISIT_TYPE.fromCode(returnVisitSubmitRequest.getReturnVisitType()))) {
            throwsException(PartyErrorConfigEnum.PARTY_RETURN_RETURN_VISIT_TYPE_NOT_EXIST);
        }

        ReturnVisitPo returnVisitPo = new ReturnVisitPo();
        returnVisitPo.setFamilyName(returnVisitSubmitRequest.getFamilyName());
        returnVisitPo.setGivenName(returnVisitSubmitRequest.getGivenName());
        returnVisitPo.setApplyName(returnVisitSubmitRequest.getFamilyName() + " " +returnVisitSubmitRequest.getGivenName());
        returnVisitPo.setProductId("PRO880000000000020A");
        returnVisitPo.setReturnVisitType(returnVisitSubmitRequest.getReturnVisitType());
        returnVisitPo.setReturnVisitStatus(PartyTermEnum.RETURN_VISIT_STATUS.NO_RETURN_VISIT.name());
        returnVisitPo.setBusinessType(returnVisitSubmitRequest.getBusinessType());
        returnVisitPo.setOnlineMobile(returnVisitSubmitRequest.getOnlineMobile());
        returnVisitPo.setApplyAmount(returnVisitSubmitRequest.getApplyAmount());
        returnVisitPo.setSourceType(returnVisitSubmitRequest.getSourceType());
        returnVisitPo.setConsultantName(returnVisitSubmitRequest.getConsultantName());
        returnVisitPo.setApplyDate(DateUtils.getCurrentTime());
        returnVisitPo.setReturnVisitService(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.fromCode(returnVisitSubmitRequest.getBusinessType()).getServiceName());
        returnVisitPo.setMustReturnFlag(returnVisitSubmitRequest.getMustReturnFlag());
        returnVisitBaseService.saveReturnVisitPo(users.getUserId(), returnVisitPo);

        //异步发送消息
        new Thread(() -> this.sendOnlineVisitMessage(returnVisitPo)).start();

        String name = returnVisitSubmitRequest.getFamilyName() + " " + returnVisitSubmitRequest.getGivenName();
        resultObject.setData(name);
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postOnlinePolicyReturnVisitSubmit(Users users, PolicyReturnVisitSubmitRequest policyReceiptSubmitRequest) {
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitId(), PartyErrorConfigEnum.PARTY_PARAMETER_RETURN_VISIT_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitType(), PartyErrorConfigEnum.PARTY_PARAMETER_RETURN_VISIT_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitDateFormat(), PartyErrorConfigEnum.PARTY_PARAMETER_RETURN_VISIT_DATE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitChannel(), PartyErrorConfigEnum.PARTY_SAVE_POLICY_RETURN_VISIT_CHANNEL_IS_NOT_NULL_ERROR);
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitResult(), PartyErrorConfigEnum.PARTY_SAVE_POLICY_RETURN_VISIT_RESULT_IS_NOT_NULL_ERROR);
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitRemark(), PartyErrorConfigEnum.PARTY_SAVE_POLICY_RETURN_VISIT_REMARK_IS_NOT_NULL_ERROR);

        ReturnVisitPo returnVisitPo = returnVisitBaseService.getByReturnVisitId(policyReceiptSubmitRequest.getReturnVisitId());
        if (!AssertUtils.isNotNull(returnVisitPo)) {
            throwsException(PartyErrorConfigEnum.PARTY_RETURN_VISIT_IS_NOT_EXIST);
        }

        long returnVisitDate = DateUtils.stringToTime(policyReceiptSubmitRequest.getReturnVisitDateFormat(), DateUtils.FORMATE3);
        if (returnVisitDate > DateUtils.timeToTimeLow(DateUtils.getCurrentTime())) {
            throw new RequestException(PartyErrorConfigEnum.PARTY_RETURN_VISIT_DATE_CANNOT_BE_GREATER_THAN_THE_CURRENT_DATE);
        }
        ReflectionUtils.copyProperties(returnVisitPo, policyReceiptSubmitRequest);
        returnVisitPo.setReturnVisitStatus(PartyTermEnum.RETURN_VISIT_STATUS.YES_RETURN_VISIT.name());
        returnVisitPo.setReturnVisitDate(returnVisitDate);
        returnVisitPo.setReturnVisitUserId(users.getUserId());
        returnVisitBaseService.saveReturnVisitPo(users.getUserId(), returnVisitPo);

        String returnVisitId = returnVisitPo.getReturnVisitId();
        List<ReturnVisitAttachmentPo> policyReturnVisitAttachmentPoList = new ArrayList<>();
        List<PolicyReturnVisitAttachmentRequest> policyReturnAttachmentVisitList = policyReceiptSubmitRequest.getPolicyReturnAttachmentVisitList();
        policyReturnAttachmentVisitList.forEach(policyReturnVisitAttachmentRequest -> {
            ReturnVisitAttachmentPo policyReturnVisitAttachmentPo = new ReturnVisitAttachmentPo();
            policyReturnVisitAttachmentPo.setReturnVisitAttachmentId(UUIDUtils.getUUIDShort());
            policyReturnVisitAttachmentPo.setAttachmentId(policyReturnVisitAttachmentRequest.getAttachmentId());
            policyReturnVisitAttachmentPo.setCreatedUserId(users.getUserId());
            policyReturnVisitAttachmentPo.setCreatedDate(System.currentTimeMillis());
            policyReturnVisitAttachmentPo.setReturnVisitId(returnVisitId);
            policyReturnVisitAttachmentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyReturnVisitAttachmentPoList.add(policyReturnVisitAttachmentPo);
        });
        returnVisitAttachmentDao.insert(policyReturnVisitAttachmentPoList);
        return ResultObject.success();
    }

    @Override
    public ResultObject getOnlinePolicyReturnVisitDetail(String returnVisitId, String returnVisitChangeId) {
        ResultObject<PolicyReturnVisitDetailResponse> resultObject = ResultObject.success();
        ReturnVisitPo returnVisitPo = returnVisitBaseService.getByReturnVisitId(returnVisitId);
        AssertUtils.isNotNull(this.getLogger(), returnVisitPo, PartyErrorConfigEnum.PARTY_RETURN_VISIT_ID_NOT_NULL);
        PolicyReturnVisitDetailResponse returnVisitDetailResponse = new PolicyReturnVisitDetailResponse();

        //回访详情
        OnlinePolicyReturnVisitResponse returnVisitResponse = new OnlinePolicyReturnVisitResponse();
        ReturnVisitResponse returnVisitResponse1 = new ReturnVisitResponse();
        returnVisitResponse.setReturnVisitId(returnVisitPo.getReturnVisitId());
        ReflectionUtils.copyProperties(returnVisitResponse, returnVisitPo);
        ReflectionUtils.copyProperties(returnVisitResponse1,returnVisitPo);

        returnVisitDetailResponse.setBasicDetailResponse(returnVisitResponse);
        returnVisitDetailResponse.setPolicyReturnVisit(returnVisitResponse1);

        //回访修改信息
        if (AssertUtils.isNotNull(returnVisitChangeId)) {
            ReturnVisitChangePo returnVisitChangePo = returnVisitBaseService.getReturnVisitChangeById(returnVisitChangeId);
            if (AssertUtils.isNotNull(returnVisitChangePo)) {
                ReturnVisitChangeRespone changeRespone = new ReturnVisitChangeRespone();
                changeRespone.setChangeRemark(returnVisitChangePo.getChangeReamrk());
                returnVisitDetailResponse.setChangeRespone(changeRespone);
                returnVisitResponse1.setReturnVisitChangeId(returnVisitChangeId);
            }
        }

        //附件信息
        List<ReturnVisitAttachmentPo> returnVisitAttachmentPos = returnVisitAttachmentBaseDao.getReturnVisitAttachmentByReturnVisitId(returnVisitId);
        if (AssertUtils.isNotEmpty(returnVisitAttachmentPos)) {
            List<String> attachmentList = returnVisitAttachmentPos.stream().map(ReturnVisitAttachmentPo::getAttachmentId).collect(Collectors.toList());
            ResultObject<List<AttachmentResponse>> attachmentResultObject = attachmentApi.attachmentList(attachmentList);
            AssertUtils.isResultObjectError(attachmentResultObject);
            List<AttachmentResponse> attachmentResponseList = attachmentResultObject.getData();

            List<ReturnVisitAttachmentResponse> attachements = new ArrayList<>();
            returnVisitAttachmentPos.stream().forEach(attachement -> {
                ReturnVisitAttachmentResponse attachmentResponse = new ReturnVisitAttachmentResponse();
                attachmentResponse.setAttachmentId(attachement.getAttachmentId());
                attachmentResponseList.stream().filter(temp -> temp.getMediaId().equals(attachement.getAttachmentId()))
                        .findFirst().ifPresent(a -> {
                            attachmentResponse.setAttachmentName(a.getFileName() + "." + a.getFileSuffix());
                            attachmentResponse.setUrl(a.getUrl());
                        });
                attachements.add(attachmentResponse);
            });
            returnVisitDetailResponse.setPolicyReturnAttachmentVisitList(attachements);
        }

        resultObject.setData(returnVisitDetailResponse);
        return resultObject;
    }

    @Override
    public ResultObject getOnlinePolicyReturnVisitList(ReturnVisitPageRequest pageRequest) {
        ResultObject<BasePageResponse<OnlinePolicyReturnVisitResponse>> resultObject = ResultObject.success();
        List<OnlinePolicyReturnVisitResponse> policyReturnVisitResponseList = new ArrayList<>();
        List<ReturnVisitBo> returnVisitBoList = returnVisitExtDao.queryOnlineReturnVisitList(pageRequest);
        if (AssertUtils.isNotEmpty(returnVisitBoList)) {
            returnVisitBoList.stream().forEach(returnVisitBo -> {
                OnlinePolicyReturnVisitResponse policyReturnVisitResponse = new OnlinePolicyReturnVisitResponse();
                ClazzUtils.copyPropertiesIgnoreNull(returnVisitBo, policyReturnVisitResponse);
                policyReturnVisitResponseList.add(policyReturnVisitResponse);
            });
        }
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(returnVisitBoList) ? returnVisitBoList.get(0).getTotalLine() : null;

        BasePageResponse basePageResponse = BasePageResponse.getData(pageRequest.getCurrentPage(), pageRequest.getPageSize(), totalLine, policyReturnVisitResponseList);
        resultObject.setData(basePageResponse);
        System.out.println(JackSonUtils.toJson(returnVisitBoList));
        return resultObject;
    }

    private void sendOnlineVisitMessage(ReturnVisitPo returnVisitPo) {
        try {
            Map messageParamMap = new HashMap<>();
            String returnVisitType = returnVisitPo.getReturnVisitType();
            messageParamMap.put("applicantName",returnVisitPo.getApplyName());
            messageParamMap.put("mobile", returnVisitPo.getOnlineMobile());
            messageParamMap.put("applyAmount",returnVisitPo.getApplyAmount());
            //传入国际化的条件key和type，到消息微服务那边根据消息微服务发送给内部人员的语言进行国际化取值
            Map<String,String> hashMap1 = new HashMap();
            hashMap1.put("key",returnVisitPo.getProductId());
            hashMap1.put("type","PRODUCT_ID");
            Map<String,String> hashMap = new HashMap();
            hashMap.put("key",returnVisitPo.getSourceType());
            hashMap.put("type","SOURCE_TYPE");
            messageParamMap.put("sourceType",hashMap);
            messageParamMap.put("productName",hashMap1);
            messageParamMap.put("applyDate",DateUtils.timeStrToString(returnVisitPo.getApplyDate(), DateUtils.FORMATE6));
            messageParamMap.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE6));
            log.info("客户回访消息参数:{}", JackSonUtils.toJson(messageParamMap));
            if (CLIENT.name().equals(returnVisitPo.getSourceType())) {
                if (ONLINE_OTHER_AMOUNT.name().equals(returnVisitType)) {
                    log.info("开始发送客户app网销申请其他保额回访消息");
                    messageParamMap.put("consultantName",returnVisitPo.getConsultantName());
                    log.info("客户app网销申请消息参数:{}", JackSonUtils.toJson(messageParamMap));
                    List<String> userIds = platformUsersApi.getBusinessUsersNew(PartyTermEnum.MSG_BUSINESS_TYPE.CLIENT_ONLINE_RETURN_VISIT_MESSAGE_REMINDER.name()).getData();
                    log.info("开始发送客户app网销申请其他保额回访消息userIds:"+ userIds);
                    messageBusinessService.pushBusinessMessage(PartyTermEnum.MSG_BUSINESS_TYPE.CLIENT_ONLINE_RETURN_VISIT_MESSAGE_REMINDER.name(), userIds, messageParamMap);
                }
                if (ONLINE_WRONG_BMI.name().equals(returnVisitType)) {
                    log.info("开始发送客户app网销申请BMI不符回访消息");
                    log.info("客户app网销申请消息参数:{}", JackSonUtils.toJson(messageParamMap));
                    List<String> userIds = platformUsersApi.getBusinessUsersNew(PartyTermEnum.MSG_BUSINESS_TYPE.CLIENT_ONLINE_WRONG_BMI_REMINDER.name()).getData();
                    log.info("开始发送客户app网销申请BMI不符回访消息userIds:"+ userIds);
                    messageBusinessService.pushBusinessMessage(PartyTermEnum.MSG_BUSINESS_TYPE.CLIENT_ONLINE_WRONG_BMI_REMINDER.name(), userIds, messageParamMap);
                }
                if (ONLINE_NON_STANDARD_HEALTH_NOTIFICATION.name().equals(returnVisitType)) {
                    log.info("开始发送客户app网销非标健康告知回访消息");
                    log.info("客户app网销申请消息参数:{}", JackSonUtils.toJson(messageParamMap));
                    List<String> userIds = platformUsersApi.getBusinessUsersNew(PartyTermEnum.MSG_BUSINESS_TYPE.CLIENT_ONLINE_NON_STANDARD_HEALTH_NOTIFICATION.name()).getData();
                    log.info("开始发送客户app网销非标健康告知回访消息userIds:"+ userIds);
                    messageBusinessService.pushBusinessMessage(PartyTermEnum.MSG_BUSINESS_TYPE.CLIENT_ONLINE_NON_STANDARD_HEALTH_NOTIFICATION.name(), userIds, messageParamMap);
                }

            }else {
                if (ONLINE_OTHER_AMOUNT.name().equals(returnVisitType)) {
                    log.info("开始发送官网网销申请其他保额回访消息");
                    List<String> userIds = platformUsersApi.getBusinessUsersNew(PartyTermEnum.MSG_BUSINESS_TYPE.BROWER_ONLINE_RETURN_VISIT_MESSAGE_REMINDER.name()).getData();
                    log.info("开始发送官网网销申请其他保额回访消息userIds:"+ userIds);
                    messageBusinessService.pushBusinessMessage(PartyTermEnum.MSG_BUSINESS_TYPE.BROWER_ONLINE_RETURN_VISIT_MESSAGE_REMINDER.name(), userIds, messageParamMap);
                }
                if (ONLINE_WRONG_BMI.name().equals(returnVisitType)) {
                    log.info("开始发送官网网销申请BMI不符回访消息");
                    List<String> userIds = platformUsersApi.getBusinessUsersNew(PartyTermEnum.MSG_BUSINESS_TYPE.BROWER_ONLINE_WRONG_BMI_REMINDER.name()).getData();
                    log.info("开始发送官网网销申请BMI不符回访消息userIds:"+ userIds);
                    messageBusinessService.pushBusinessMessage(PartyTermEnum.MSG_BUSINESS_TYPE.BROWER_ONLINE_WRONG_BMI_REMINDER.name(), userIds, messageParamMap);
                }
                if (ONLINE_NON_STANDARD_HEALTH_NOTIFICATION.name().equals(returnVisitType)) {
                    log.info("开始发送官网网销非标健康告知回访消息");
                    log.info("官网网销申请消息参数:{}", JackSonUtils.toJson(messageParamMap));
                    List<String> userIds = platformUsersApi.getBusinessUsersNew(PartyTermEnum.MSG_BUSINESS_TYPE.BROWER_ONLINE_NON_STANDARD_HEALTH_NOTIFICATION.name()).getData();
                    log.info("开始发送官网网销非标健康告知回访消息userIds:"+ userIds);
                    messageBusinessService.pushBusinessMessage(PartyTermEnum.MSG_BUSINESS_TYPE.BROWER_ONLINE_NON_STANDARD_HEALTH_NOTIFICATION.name(), userIds, messageParamMap);
                }
            }

        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            log.info("发送网销客户回访消息失败");
        }
    }


}
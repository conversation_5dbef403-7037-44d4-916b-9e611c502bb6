package com.gclife.party.service.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.party.core.jooq.tables.daos.CustomerAgentDao;
import com.gclife.party.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.party.core.jooq.tables.pojos.CustomerHistoryPo;
import com.gclife.party.core.jooq.tables.pojos.CustomerPo;
import com.gclife.party.dao.CustomerManageBaseDao;
import com.gclife.party.model.bo.CustomerAgentBo;
import com.gclife.party.model.bo.CustomerRelationshipBo;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.request.CustomerMessagesRequest;
import com.gclife.party.model.request.EndorseCustomerRequest;
import com.gclife.party.model.response.*;
import com.gclife.party.model.vo.CustomerQueryVo;
import com.gclife.party.service.CustomerBaseBusinessService;
import com.gclife.party.service.base.CustomerBaseService;
import com.gclife.party.service.base.CustomerHistoryBaseService;
import com.gclife.party.validate.parameter.CustomerParameterValidate;
import com.gclife.party.validate.transfer.CustomerBaseTransfer;
import com.gclife.platform.api.PlatformAreaApi;
import com.gclife.platform.model.response.AreaNameResponse;
import com.gclife.platform.model.response.AreaTreeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyApplicantResponse;
import com.gclife.policy.model.response.PolicyDetailResponse;
import com.gclife.policy.model.response.PolicyInsuredResponse;
import com.gclife.policy.model.response.PolicyOnlineResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyErrorConfigEnum.PARTY_CUSTOMER_ID_IS_NOT_NULL;
import static com.gclife.party.model.config.PartyErrorConfigEnum.PARTY_CUSTOMER_VERSION_NO_IS_NOT_NULL;


/**
 * <AUTHOR>
 * create 18-9-7
 * description:
 */
@Service
public class CustomerBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements CustomerBaseBusinessService {

    @Autowired
    private CustomerBaseService customerBaseService;
    @Autowired
    private CustomerAgentDao customerAgentDao;

    @Autowired
    private CustomerHistoryBaseService customerHistoryBaseService;

    @Autowired
    private CustomerParameterValidate customerParameterValidate;

    @Autowired
    private CustomerBaseTransfer customerBaseTransfer;
    @Autowired
    private PlatformAreaApi platformAreaApi;

    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private CustomerManageBaseDao customerManageBaseDao;

    /**
     * 根据客户ID获取客户信息
     *
     * @return
     */
    @Override
    public ResultObject<CustomerMessageResponse> getBaseCustomer(String customerId, String versionNo) {
        ResultObject<CustomerMessageResponse> resultObject = new ResultObject<>();
        try {
            CustomerMessageResponse customerMessageResponse = null;
            AssertUtils.isNotEmpty(getLogger(), customerId, PARTY_CUSTOMER_ID_IS_NOT_NULL);
            CustomerPo customerPo = customerBaseService.queryOneCustomer(customerId, versionNo);
            if (AssertUtils.isNotNull(customerPo)) {
                customerMessageResponse = (CustomerMessageResponse) this.converterObject(customerPo, CustomerMessageResponse.class);
            } else {
                CustomerHistoryPo customerHistoryPo = customerHistoryBaseService.queryOneCustomerHistory(customerId, versionNo);
                if (AssertUtils.isNotNull(customerHistoryPo)) {
                    customerMessageResponse = (CustomerMessageResponse) this.converterObject(customerHistoryPo, CustomerMessageResponse.class);
                }
            }
            resultObject.setData(customerMessageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PartyErrorConfigEnum.PARTY_QUERY_CUSTOMER_MESSAGE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<CustomerHistoryResponse> getBaseCustomerHistory(String customerId, String versionNo) {
        ResultObject<CustomerHistoryResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), customerId, PARTY_CUSTOMER_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), versionNo, PARTY_CUSTOMER_VERSION_NO_IS_NOT_NULL);
            CustomerHistoryPo customerHistoryPo = customerHistoryBaseService.queryOneCustomerHistory(customerId, versionNo);
            CustomerHistoryResponse customerHistoryResponse = (CustomerHistoryResponse) this.converterObject(customerHistoryPo, CustomerHistoryResponse.class);
            resultObject.setData(customerHistoryResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PartyErrorConfigEnum.PARTY_QUERY_CUSTOMER_HISTORY_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }


    @Override
    public ResultObject<BasePageResponse<CustomerMessageResponse>> queryCustomerList(AppRequestHeads appRequestHandler, CustomerMessagesRequest customerMessagesRequest) {
        ResultObject<BasePageResponse<CustomerMessageResponse>> resultObject = new ResultObject<>();
        try {
            List<String> customerIds = new ArrayList<>();
            //网销新增根据保单号查询客户
            if (AssertUtils.isNotEmpty(customerMessagesRequest.getKeyword())) {
                ResultObject<PolicyOnlineResponse> policyOnlineResponseResultObject = policyApi.getPolicyExistByPolicyNoEndorse(customerMessagesRequest.getKeyword());
                if (!AssertUtils.isResultObjectDataNull(policyOnlineResponseResultObject) && AssertUtils.isNotEmpty(policyOnlineResponseResultObject.getData().getPolicyId())) {
                    //根据保单id查询投保人被保人的客户id
                    ResultObject<PolicyDetailResponse> policyDetailResponseResultObject = policyApi.queryPolicyDetailGet(policyOnlineResponseResultObject.getData().getPolicyId());
                    if (!AssertUtils.isResultObjectDataNull(policyDetailResponseResultObject)) {
                        PolicyDetailResponse policyDetailResponse = policyDetailResponseResultObject.getData();
                        PolicyApplicantResponse policyApplicant = policyDetailResponse.getPolicyApplicant();
                        List<PolicyInsuredResponse> listPolicyInsured = policyDetailResponse.getListPolicyInsured();
                        if (AssertUtils.isNotNull(policyApplicant)) {
                            String customerIdApplicant = policyApplicant.getCustomerId();
                            customerIds.add(customerIdApplicant);
                        }

                        if(AssertUtils.isNotNull(listPolicyInsured)) {
                            listPolicyInsured.forEach(policyInsuredResponse -> {
                                if (AssertUtils.isNotNull(policyInsuredResponse)) {
                                    String customerId = policyInsuredResponse.getCustomerId();
                                    customerIds.add(customerId);
                                }
                            });
                        }
                        customerMessagesRequest.setCustomerIds(customerIds);
                        customerMessagesRequest.setKeyword(null);
                    }
                }
            }

            List<CustomerPo> customerPoList = customerBaseService.queryCustomerByKeyword((CustomerQueryVo) this.converterObject(customerMessagesRequest, CustomerQueryVo.class));
            if (!AssertUtils.isNotEmpty(customerPoList)) {
                return resultObject;
            }
            List<String> areaIds = customerPoList.stream().map(CustomerPo::getHomeAreaCode).distinct().collect(Collectors.toList());
            ResultObject<List<AreaNameResponse>> listAreaNameResultObject = platformAreaApi.areaNameGetBatch(areaIds);
            ResultObject<Map<String, List<AreaTreeResponse>>> areaMapResultObject = platformAreaApi.areaTreeGetBatch(areaIds);

            List<CustomerMessageResponse> customerMessageResponseList = new ArrayList<>();

            customerPoList.forEach(customerPo -> {
                CustomerMessageResponse customerMessageResponse = new CustomerMessageResponse();
                ClazzUtils.copyPropertiesIgnoreNull(customerPo, customerMessageResponse);

                String homeAreaCode = customerPo.getHomeAreaCode();
                if (AssertUtils.isNotEmpty(homeAreaCode)) {
                    if (!AssertUtils.isResultObjectListDataNull(listAreaNameResultObject)) {
                        listAreaNameResultObject.getData().stream().filter(areaNameResponse -> areaNameResponse.getAreaId().equals(homeAreaCode)).findFirst().ifPresent(areaNameResponse -> {
                            customerMessageResponse.setHomeAreaCodeName(areaNameResponse.getAreaName());
                        });
                    }
                    if (!AssertUtils.isResultObjectDataNull(areaMapResultObject) && AssertUtils.isNotEmpty(areaMapResultObject.getData().get(homeAreaCode))) {
                        List<AreaTreeResponse> areaTreeResponses = areaMapResultObject.getData().get(homeAreaCode);
                        customerMessageResponse.setListHomeAreaName(areaTreeResponses);
                    }
                }
                customerMessageResponseList.add(customerMessageResponse);
            });
            //获取总页数
            int totalLine = AssertUtils.isNotEmpty(customerPoList) ? customerPoList.get(0).getTotalLine() : 0;
            BasePageResponse<CustomerMessageResponse> basePageResponse = BasePageResponse.getData(customerMessagesRequest.getCurrentPage(), customerMessagesRequest.getPageSize(), totalLine, customerMessageResponseList);
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PartyErrorConfigEnum.PARTY_QUERY_CUSTOMER_MESSAGE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject synchronizeBaseCustomer(EndorseCustomerRequest endorseCustomerRequest, Users users) {
        ResultObject<CustomerSynchResponse> resultObject = new ResultObject<>();
        try {
            customerParameterValidate.validCustomerData(endorseCustomerRequest, users);
            CustomerPo customerPo = customerBaseService.queryOneCustomer(endorseCustomerRequest.getCustomerId(), null);
            AssertUtils.isNotNull(getLogger(), customerPo, PartyErrorConfigEnum.PARTY_UPDATE_CUSTOMER_IS_NULL);
            //备份客户历史数据
            CustomerHistoryPo customerHistoryPo = (CustomerHistoryPo) converterObject(customerPo, CustomerHistoryPo.class);
            customerHistoryBaseService.saveCustomerHistoryPo(customerHistoryPo);

            // 旧版本号
            String oldVersion = customerPo.getVersionNo();
            //修改当前最新历史信息，版本更新
            customerBaseTransfer.transformCustomerBase(customerPo, endorseCustomerRequest);
            // 新版本号
            String newVersion = DateUtils.getJobNumberByTime("", "", DateUtils.FORMATE53, false);
            customerPo.setVersionNo(newVersion);
            customerBaseService.saveCustomerPo(customerPo);

            resultObject.setData(new CustomerSynchResponse(customerPo.getCustomerId(), oldVersion, newVersion));
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            setTransactionalResultObjectException(this.getLogger(), resultObject, e, PartyErrorConfigEnum.PARTY_CUSTOMER_SYNCHRONIZE_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<CustomerAgentsResponse>> getAgentCustomerRelation(String customerId) {
        ResultObject<List<CustomerAgentsResponse>> resultObject = new ResultObject<>();
        try {
            List<CustomerAgentPo> customerAgentPos = customerBaseService.queryCustomerAgentByCustomerId(customerId);
            List<CustomerAgentsResponse> customerAgentResponseList = (List<CustomerAgentsResponse>) this.converterList(customerAgentPos, new TypeToken<List<CustomerAgentsResponse>>() {
            }.getType());
            resultObject.setData(customerAgentResponseList);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PartyErrorConfigEnum.PARTY_QUERY_CUSTOMER_MESSAGE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    /**
     * 回滚客户信息
     *
     * @param customerId   客户ID
     * @param oldVersionNo 旧版本号
     * @return
     */
    @Override
    @Transactional
    public ResultObject rollbackCustomer(String customerId, String oldVersionNo) {
        ResultObject resultObject = new ResultObject();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), customerId, PartyErrorConfigEnum.PARTY_CUSTOMER_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), oldVersionNo, PARTY_CUSTOMER_VERSION_NO_IS_NOT_NULL);

            // 回滚客户数据
            customerHistoryBaseService.rollbackCustomerHistory(customerId, oldVersionNo);
        } catch (Exception e) {
            e.printStackTrace();
            setTransactionalResultObjectException(this.getLogger(), resultObject, e, PartyErrorConfigEnum.PARTY_ROLLBACK_CUSTOMER_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<CustomerMessageResponse>> getBaseCustomerList(String... customerIds) {
        ResultObject<List<CustomerMessageResponse>> resultObject = new ResultObject<>();
        List<CustomerPo> customerPoList = customerBaseService.getBaseCustomerList(customerIds);
        List<CustomerMessageResponse> customerMessageResponseList = (List<CustomerMessageResponse>) this.converterList(customerPoList, new TypeToken<List<CustomerMessageResponse>>() {
        }.getType());
        resultObject.setData(customerMessageResponseList);
        return resultObject;
    }

    @Override
    public ResultObject<List<CustomerAgentResponse>> getClientCustomerAgentList(String type) {
        ResultObject<List<CustomerAgentResponse>> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(type)) {
            return resultObject;
        }
        List<CustomerAgentPo> customerAgentPos = customerBaseService.getClientCustomerAgentList(type);
        List<CustomerAgentResponse> customerMessageResponseList = (List<CustomerAgentResponse>) this.converterList(customerAgentPos, new TypeToken<List<CustomerAgentResponse>>() {
        }.getType());
        resultObject.setData(customerMessageResponseList);
        return resultObject;
    }

    public static void main(String[] args) {
        String A = "ស្រ៊ាន ស្រុីម";
        String B = "ស៊្រាន ស្រុីម";
        System.out.println(A.equals(B));

        byte[] bytesA = A.getBytes();
        int asciiSumA = 0;
        for (byte b : bytesA) {
            System.out.println("A" + b);
            asciiSumA += b;
        }
        System.out.println("ASCII A sum: " + asciiSumA);


        byte[] bytesB = B.getBytes();
        int asciiSumB = 0;
        for (byte b : bytesB) {
            System.out.println("B" + b);
            asciiSumB += b;
        }
        System.out.println("ASCII B sum: " + asciiSumB);

    }

    /**
     * 查询疑似客户列表
     *
     * @param users
     * @param customerAgentId
     * @return
     */
    @Override
    public ResultObject<List<CustomerMessageResponse>> querySuspectedCustomer(Users users, String customerAgentId) {
        ResultObject<List<CustomerMessageResponse>> resultObject = new ResultObject<>();
        try {
            List<CustomerPo> customerPoList = customerBaseService.querySuspectedCustomer(customerAgentId);
            if (!AssertUtils.isNotEmpty(customerPoList)) {
                return resultObject;
            }
            List<String> areaIds = customerPoList.stream().map(CustomerPo::getHomeAreaCode).distinct().collect(Collectors.toList());
            ResultObject<List<AreaNameResponse>> listAreaNameResultObject = platformAreaApi.areaNameGetBatch(areaIds);
            ResultObject<Map<String, List<AreaTreeResponse>>> areaMapResultObject = platformAreaApi.areaTreeGetBatch(areaIds);

            List<CustomerMessageResponse> customerMessageResponseList = new ArrayList<>();

            customerPoList.forEach(customerPo -> {
                CustomerMessageResponse customerMessageResponse = new CustomerMessageResponse();
                ClazzUtils.copyPropertiesIgnoreNull(customerPo, customerMessageResponse);

                List<CustomerAgentPo> customerAgentPos = customerBaseService.queryCustomerAgentByCustomerId(customerPo.getCustomerId());
                if (AssertUtils.isNotEmpty(customerAgentPos)) {
                    customerMessageResponse.setCustomerAgentIds(customerAgentPos.stream().map(CustomerAgentPo::getCustomerAgentId).distinct().collect(Collectors.toList()));
                }

                String homeAreaCode = customerPo.getHomeAreaCode();
                if (AssertUtils.isNotEmpty(homeAreaCode)) {
                    if (!AssertUtils.isResultObjectListDataNull(listAreaNameResultObject)) {
                        listAreaNameResultObject.getData().stream().filter(areaNameResponse -> areaNameResponse.getAreaId().equals(homeAreaCode)).findFirst().ifPresent(areaNameResponse -> {
                            customerMessageResponse.setHomeAreaCodeName(areaNameResponse.getAreaName());
                        });
                    }
                    if (!AssertUtils.isResultObjectDataNull(areaMapResultObject) && AssertUtils.isNotEmpty(areaMapResultObject.getData().get(homeAreaCode))) {
                        List<AreaTreeResponse> areaTreeResponses = areaMapResultObject.getData().get(homeAreaCode);
                        customerMessageResponse.setListHomeAreaName(areaTreeResponses);
                    }
                }
                customerMessageResponseList.add(customerMessageResponse);
            });
            resultObject.setData(customerMessageResponseList);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PartyErrorConfigEnum.PARTY_QUERY_CUSTOMER_MESSAGE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<CustomerMessageResponse> queryRealCustomerByCustomerAgentId(Users users, String customerAgentId) {
        ResultObject<CustomerMessageResponse> resultObject = new ResultObject<>();
        CustomerAgentPo customerAgentPo = customerAgentDao.findById(customerAgentId);
        if (!AssertUtils.isNotNull(customerAgentPo)) {
            return resultObject;
        }
        CustomerPo customerPo = customerBaseService.queryOneCustomerByFourElements(customerAgentPo.getName(), customerAgentPo.getIdNo(), customerAgentPo.getBirthday(), customerAgentPo.getSex());
        if (!AssertUtils.isNotNull(customerPo)) {
            customerPo = customerBaseService.queryOneCustomerByCustomerNo(customerAgentPo.getCustomerNo());
        }

        if (!AssertUtils.isNotNull(customerPo)) {
            return resultObject;
        }
        CustomerMessageResponse customerMessageResponse = new CustomerMessageResponse();
        ClazzUtils.copyPropertiesIgnoreNull(customerPo, customerMessageResponse);
        resultObject.setData(customerMessageResponse);
        return resultObject;
    }

    @Override
    public ResultObject<List<CustomerAgentsResponse>> getCustomerRelationByCustomerAgentId(String customerAgentId) {
        ResultObject<List<CustomerAgentsResponse>> resultObject = new ResultObject<>();
        List<CustomerAgentPo> customerAgentPos = customerBaseService.getCustomerRelationByCustomerAgentId(customerAgentId);
        List<CustomerAgentsResponse> customerAgentResponseList = (List<CustomerAgentsResponse>) this.converterList(customerAgentPos, new TypeToken<List<CustomerAgentsResponse>>() {
        }.getType());
        resultObject.setData(customerAgentResponseList);
        return resultObject;
    }

    /**
     * 获取代理人客户关联的家人的所有代理人客户
     * @param customerAgentId 代理人客户ID
     * @param relationship 关系
     * @return
     */
    @Override
    public ResultObject<List<CustomerAgentsResponse>> getCustomerByRelationship(String customerAgentId, String relationship) {
        ResultObject<List<CustomerAgentsResponse>> resultObject = ResultObject.success();
        // 查询家庭成员关系
        List<CustomerRelationshipBo> customerRelationshipBos = customerManageBaseDao.listMember(customerAgentId, null);
        if (AssertUtils.isNotEmpty(customerRelationshipBos)) {
            List<String> customerIds = customerRelationshipBos.stream()
                    .filter(customerRelationshipBo -> customerRelationshipBo.getRelationship().equals(relationship))
                    .map(CustomerRelationshipBo::getRelationCustomerId).distinct().collect(Collectors.toList());
            // 查询关联的客户
            List<CustomerAgentBo> customerAgentBos = customerBaseService.getCustomerRelationByCustomerAgentId(customerIds);
            List<CustomerAgentsResponse> customerAgentsResponses = (List<CustomerAgentsResponse>) this.converterList(
                    customerAgentBos, new TypeToken<List<CustomerAgentsResponse>>() {}.getType()
            );
            resultObject.setData(customerAgentsResponses);
        }
        return resultObject;
    }

}

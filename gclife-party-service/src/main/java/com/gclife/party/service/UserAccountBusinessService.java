package com.gclife.party.service;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.party.model.request.*;
import com.gclife.party.model.response.CashWithdrawalResponse;
import com.gclife.party.model.response.UserAccountDetailResponse;
import com.gclife.party.model.response.UserAccountResponse;
import com.gclife.party.model.response.UserAccountTransactionRecordResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 19-6-13
 * description:
 */
public interface UserAccountBusinessService extends BaseBusinessService {
    /**
     * 批量保存账户收入
     *
     * @param userAccountIncomeRequests 收入参数
     * @return ResultObject
     */
    ResultObject postUserAccountIncomeList(List<UserAccountIncomeRequest> userAccountIncomeRequests);

    /**
     * 保存账户收入
     *
     * @param userAccountIncomeRequest 收入参数
     * @return ResultObject
     */
    ResultObject postUserAccountIncome(UserAccountIncomeRequest userAccountIncomeRequest);

    /**
     * 保存账户支出
     *
     * @param userAccountOutlayRequest 支出参数
     * @return ResultObject
     */
    ResultObject<UserAccountTransactionRecordResponse> postUserAccountOutlay(UserAccountOutlayRequest userAccountOutlayRequest);

    /**
     * 批量保存账户支出
     *
     * @param userAccountOutlayRequests 支出参数
     * @return ResultObject
     */
    ResultObject<List<UserAccountTransactionRecordResponse>> postUserAccountOutlayBatch(List<UserAccountOutlayRequest> userAccountOutlayRequests);


    /**
     * 账户初始化
     *
     * @param userId
     * @return
     */
    ResultObject initializationAccountByUserId(String userId);

    /**
     * 账户初始化
     *
     * @param userId
     * @return
     */
    ResultObject activityAccountByUserId(String userId);

    /**
     * 支出状态调整
     *
     * @param userAccountNotifyRequest 业务ID
     * @return ResultObject
     */
    ResultObject updateAccountStatus(UserAccountNotifyRequest userAccountNotifyRequest);

    /**
     * 支出状态调整异常回滚
     *
     * @param userAccountRollbackRequest 业务ID
     * @return ResultObject
     */
    ResultObject updateAccountStatusRollBack(UserAccountRollbackRequest userAccountRollbackRequest);

    /**
     * 不可用收入处理
     *
     * @param userAccountDisableRequests 不可用收入
     * @return ResultObject
     */
    ResultObject dealAccountDisable(List<UserAccountDisableRequest> userAccountDisableRequests);

    /**
     * 根据账户类型获取用户账户信息
     *
     * @param userAccountTypeCode 用户账户类型
     * @param userId              用户
     * @return ResultObject
     */
    ResultObject<UserAccountResponse> userAccountGet(String userAccountTypeCode, String userId);

    /**
     * 根据用户ID、账户类型、流水类型获取用户账户信息
     *
     * @param userId              用户
     * @param userAccountTypeCode 账户类型编码
     * @param streamCode          流水类型
     * @param currentPage         当前页面
     * @param pageSize            页面大小
     * @return UserAccountDetailResponse
     */
    ResultObject<List<UserAccountDetailResponse>> userAccountDetailGet(String userId, String userAccountTypeCode, String streamCode, Integer currentPage, Integer pageSize);

    /**
     * 批量支出状态调整异常回滚
     *
     * @param userAccountRollbackRequests 请求参数
     * @return ResultObject
     */
    ResultObject updateAccountStatusRollBackBatch(List<UserAccountRollbackRequest> userAccountRollbackRequests);

    /**
     * 批量收入撤回
     *
     * @param userAccountRollbackRequests 请求参数
     * @return ResultObject
     */
    ResultObject updateAccountIncomeRollBackBatch(List<UserAccountRollbackRequest> userAccountRollbackRequests);


    /**
     * 新增提现记录
     *
     * @param cashWithdrawalRequest 提现参数
     * @return CashWithdrawalResponse
     */
    ResultObject<CashWithdrawalResponse> postCashWithdrawal(CashWithdrawalRequest cashWithdrawalRequest);

    /**
     * 提现成功回调
     *
     * @param userAccountNotifyRequest 回调参数
     * @return ResultObject
     */
    ResultObject postCashWithdrawalNotify(UserAccountNotifyRequest userAccountNotifyRequest);

    /**
     * 提现异常回滚
     *
     * @param userAccountRollbackRequest 回滚参数
     * @return ResultObject
     */
    ResultObject updateAccountWithdrawRollBack(UserAccountRollbackRequest userAccountRollbackRequest);

    /**
     * 发起批量提现
     *
     * @param users users
     * @return ResultObject
     */
    ResultObject postCashWithdrawalBatch(Users users);
}

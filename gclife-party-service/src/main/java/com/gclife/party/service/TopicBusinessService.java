package com.gclife.party.service;

import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.party.model.request.topic.TopicSignRequest;
import com.gclife.party.model.response.topic.TopicSignRecordResponse;
import com.gclife.party.model.response.topic.TopicStatisticsSignResponse;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 17:24 2018/10/10
 * @ Description: 调查title
 * @ Modified By:
 * @ Version: $version
 */
public interface TopicBusinessService {


    /**
     * app查询用户签到统计信息
     *
     * @param currentLoginUsers 当前用户
     * @param lat
     *@param lng @return ResultObject<TopicStatisticsSignResponse>
     */
    ResultObject<TopicStatisticsSignResponse> queryOneUserTopicSignStatistics(Users currentLoginUsers, String lat, String lng);

    /**
     * 用户签到
     *
     * @param users            用户
     * @param topicSignRequest 签到参数
     * @return ResultObject
     */
    ResultObject userTopicSign(Users users, TopicSignRequest topicSignRequest);

    /**
     * 用户签到记录
     *
     * @param users 用户
     * @param basePageRequest
     * @return TopicSignRecordResponses
     */
    ResultObject<BasePageResponse<TopicSignRecordResponse>> getUserTopicSignRecord(Users users, BasePageRequest basePageRequest);
}

package com.gclife.party.service.account;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.party.model.request.account.AccountOperateListRequest;
import com.gclife.party.model.request.account.AccountOperateRequest;
import com.gclife.party.model.request.account.AccountOperateReviewRequest;
import com.gclife.party.model.response.account.AccountOperateListResponse;
import com.gclife.party.model.response.account.AccountOperateReviewListResponse;

/**
 * <AUTHOR>
 * create 19-7-12
 * description:
 */
public interface AccountFreezeService extends BaseBusinessService {

    /**
     * 账户冻结列表
     *
     * @param accountOperateListRequest 列表参数
     * @param users                     用户
     * @return AccountOperateListResponse
     */
    ResultObject<BasePageResponse<AccountOperateListResponse>> getAccountFreezeList(AccountOperateListRequest accountOperateListRequest, Users users);

    /**
     * 申请账户冻结
     *
     * @param accountOperateRequest 申请参数
     * @param users                 用户
     * @return ResultObject
     */
    ResultObject postAccountFreeze(AccountOperateRequest accountOperateRequest, Users users);

    /**
     * 冻结审批列表
     *
     * @param accountOperateListRequest 列表参数
     * @param users                     用户
     * @return AccountOperateReviewListResponse
     */
    ResultObject<BasePageResponse<AccountOperateReviewListResponse>> getAccountFreezeReviewList(AccountOperateListRequest accountOperateListRequest, Users users);

    /**
     * 操作账户冻结审批
     *
     * @param accountOperateReviewRequest 审批参数
     * @param users                       用户
     * @return ResultObject
     */
    ResultObject postAccountFreezeReview(AccountOperateReviewRequest accountOperateReviewRequest, Users users);
}

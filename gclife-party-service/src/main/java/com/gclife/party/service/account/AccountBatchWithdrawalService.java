package com.gclife.party.service.account;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.party.model.request.account.AccountRequest;
import com.gclife.party.model.request.visit.BatchWithdrawalRequest;
import com.gclife.party.model.request.visit.WithdrawalResult;
import com.gclife.party.model.response.account.AccountResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 2021/4/6 下午6:06
 * description:
 */

public interface AccountBatchWithdrawalService  extends BaseBusinessService {


    /**
     * 查询提现账户列表
     *
     * @param accountRequest
     * @param currentLoginUsers
     * @return ResultObject
     */
    ResultObject<BasePageResponse<AccountResponse>> postCashWithdrawalBatchList(AccountRequest accountRequest, Users currentLoginUsers);


//    /**
//     * 提现所选账户
//     *
//     * @param
//     * @param
//     * @return ResultObject
//     */
//    ResultObject<BasePageResponse<AccountResponse>> postCashWithdrawalFullWithdraw(List<String> accountIds ,Users currentLoginUsers);


    /**
     * 提现账户
     *
     * @param
     * @param
     * @return ResultObject
     */
    ResultObject<WithdrawalResult> postCashWithdrawalExtract(BatchWithdrawalRequest batchWithdrawalRequest, Users currentLoginUsers);

}

package com.gclife.party.service.impl;

import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.api.AgentExtApi;
import com.gclife.agent.model.response.*;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.party.model.bo.TopicAttachmentBo;
import com.gclife.party.model.bo.TopicBo;
import com.gclife.party.model.bo.TopicPageBo;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.PositionSignInRequest;
import com.gclife.party.model.response.topic.PositionSignInDetailAgentResponse;
import com.gclife.party.model.response.topic.PositionSignInDetailResponse;
import com.gclife.party.model.response.topic.PositionSignInResponse;
import com.gclife.party.model.response.topic.TopicAttachmentResponse;
import com.gclife.party.model.vo.TopicQueryVo;
import com.gclife.party.service.PositionSignInService;
import com.gclife.party.service.base.TopicAttachmentBaseService;
import com.gclife.party.service.base.TopicBaseService;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.google.common.reflect.TypeToken;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyErrorConfigEnum.*;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 14:29 2019/1/16
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Service
public class PositionSignInServiceImpl extends BaseBusinessServiceImpl implements PositionSignInService {
    @Autowired
    private TopicBaseService topicBaseService;
    @Autowired
    private TopicAttachmentBaseService topicAttachmentBaseService;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;
    @Autowired
    private AgentExtApi agentExtApi;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private AttachmentApi attachmentApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    @Override
    public ResultObject<BasePageResponse<PositionSignInResponse>> queryPositionSignIn(PositionSignInRequest positionSignInRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<PositionSignInResponse>> resultObject = new ResultObject<>();
        positionSignInRequest.setTopicTypeCode(PartyTermEnum.TOPIC_TYPE_CODE.SIGNIN.name());
        List<AgentKeyWordResponse> agentKeyWordRespFcList = null;
        if (AssertUtils.isNotEmpty(positionSignInRequest.getKeyword())) {
            ResultObject<List<AgentKeyWordResponse>> agentsVagueGet = agentApi.agentsVagueGet(positionSignInRequest.getKeyword());
            positionSignInRequest.setKeyword(null);
            AssertUtils.isResultObjectDataNull(this.getLogger(), agentsVagueGet);
            agentKeyWordRespFcList = agentsVagueGet.getData();
            if (!AssertUtils.isNotEmpty(agentKeyWordRespFcList)) {
                return resultObject;
            }
        }
        TopicQueryVo topicVo = (TopicQueryVo) this.converterObject(positionSignInRequest, TopicQueryVo.class);
        if (AssertUtils.isNotEmpty(agentKeyWordRespFcList)) {
            List<String> userIdList = agentKeyWordRespFcList.stream().map(agentKeyWordRespFc -> agentKeyWordRespFc.getUserId()).collect(Collectors.toList());
            topicVo.setUserIdList(userIdList);
        }
        //处理机构
        if(AssertUtils.isNotNull(positionSignInRequest.getBranchId())){
            ResultObject<List<BranchResponse>> listResultObject = platformBranchBaseApi.queryBranchTreeBranchListById(positionSignInRequest.getBranchId());
            AssertUtils.isResultObjectError(this.getLogger(),listResultObject);
            if(AssertUtils.isNotNull(listResultObject.getData()) && listResultObject.getData().size()>1){
                topicVo.setBranchId(null);
                topicVo.setBranchIds(listResultObject.getData().stream().map(BranchResponse::getBranchId).collect(Collectors.toList()));
            }
        }
        List<TopicPageBo> topicPageBoList = topicBaseService.queryPageTopicPo(topicVo);
        if (!AssertUtils.isNotEmpty(agentKeyWordRespFcList) && AssertUtils.isNotEmpty(topicPageBoList)) {
            List<String> userIdList = topicPageBoList.stream().map(topicPageBo -> topicPageBo.getUserId()).collect(Collectors.toList());
            ResultObject<List<AgentResponse>> agents = agentExtApi.agentsGetByUserId(userIdList);
            AssertUtils.isResultObjectError(this.getLogger(), agents);
            agentKeyWordRespFcList = (List<AgentKeyWordResponse>) this.converterList(agents.getData(), new TypeToken<List<AgentKeyWordResponse>>() {
            }.getType());
        }
        int totalLine = 0;
        List<PositionSignInResponse> positionSignInList = null;
        if (AssertUtils.isNotEmpty(topicPageBoList)) {
            List<String> types = new ArrayList<>();
            types.add(TerminologyTypeEnum.BRANCH_NAME.name());
            Map<String, List<SyscodeResponse>> internationalList = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), types).getData();

            List<String> branchIdList = topicPageBoList.stream().map(topicPageBo -> topicPageBo.getBranchId()).collect(Collectors.toList());
            ResultObject<List<BranchResponse>> listResultObject = platformBranchBaseApi.queryBranchTreeLeafListById(branchIdList);
            AssertUtils.isResultObjectError(this.getLogger(), listResultObject);
            List<BranchResponse> branchBaseRespFcList = listResultObject.getData();
            for (TopicPageBo topicPageBo : topicPageBoList) {
                if (AssertUtils.isNotEmpty(agentKeyWordRespFcList)) {
                    agentKeyWordRespFcList.forEach(agentKeyWordRespFc -> {
                        if (topicPageBo.getUserId().equals(agentKeyWordRespFc.getUserId())) {
                            topicPageBo.setAgentName(agentKeyWordRespFc.getAgentName());
                            topicPageBo.setAgentCode(agentKeyWordRespFc.getAgentCode());
                            return;
                        }
                    });
                }
                if (AssertUtils.isNotEmpty(branchBaseRespFcList)) {
                    branchBaseRespFcList.forEach(branchBaseRespFc -> {
                        if (branchBaseRespFc.getBranchId().equals(topicPageBo.getBranchId())) {
                            topicPageBo.setBranchName(branchBaseRespFc.getBranchShortname());
                            if (AssertUtils.isNotNull(internationalList)) {
                                // 机构信息国际化术语
                                List<SyscodeResponse> syscodeResponses = internationalList.get(TerminologyTypeEnum.BRANCH_NAME.name());

                                //机构信息国际化
                                if (AssertUtils.isNotEmpty(syscodeResponses)) {
                                    topicPageBo.setBranchName(this.queryOneTerminologyListName(syscodeResponses, topicPageBo.getBranchId()));
                                }
                            }
                            return;
                        }
                    });
                }
            }
            positionSignInList = (List<PositionSignInResponse>) this.converterList(topicPageBoList, new TypeToken<List<PositionSignInResponse>>() {
            }.getType());
            totalLine = topicPageBoList.get(0).getTotalLine();
        }
        BasePageResponse<PositionSignInResponse> basePageResponse = BasePageResponse.getData(positionSignInRequest.getCurrentPage(), positionSignInRequest.getPageSize(), totalLine, positionSignInList);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    @Override
    public ResultObject<List<PositionSignInResponse>> queryPositionSignInAll(PositionSignInRequest positionSignInRequest, Users currentLoginUsers) {
        ResultObject<List<PositionSignInResponse>> resultObject = new ResultObject<>();
        positionSignInRequest.setTopicTypeCode(PartyTermEnum.TOPIC_TYPE_CODE.SIGNIN.name());
        List<AgentKeyWordResponse> agentKeyWordRespFcList = null;
        if (AssertUtils.isNotEmpty(positionSignInRequest.getKeyword())) {
            ResultObject<List<AgentKeyWordResponse>> agentsVagueGet = agentApi.agentsVagueGet(positionSignInRequest.getKeyword());
            positionSignInRequest.setKeyword(null);
            AssertUtils.isResultObjectDataNull(this.getLogger(), agentsVagueGet);
            agentKeyWordRespFcList = agentsVagueGet.getData();
            if (!AssertUtils.isNotEmpty(agentKeyWordRespFcList)) {
                return resultObject;
            }
        }
        TopicQueryVo topicVo = (TopicQueryVo) this.converterObject(positionSignInRequest, TopicQueryVo.class);
        if (AssertUtils.isNotEmpty(agentKeyWordRespFcList)) {
            List<String> userIdList = agentKeyWordRespFcList.stream().map(agentKeyWordRespFc -> agentKeyWordRespFc.getUserId()).collect(Collectors.toList());
            topicVo.setUserIdList(userIdList);
        }
        //处理机构
        if (AssertUtils.isNotNull(positionSignInRequest.getBranchId())) {
            ResultObject<List<BranchResponse>> listResultObject = platformBranchBaseApi.queryBranchTreeBranchListById(positionSignInRequest.getBranchId());
            AssertUtils.isResultObjectError(this.getLogger(), listResultObject);
            if (AssertUtils.isNotNull(listResultObject.getData()) && listResultObject.getData().size() > 1) {
                topicVo.setBranchId(null);
                topicVo.setBranchIds(listResultObject.getData().stream().map(BranchResponse::getBranchId).collect(Collectors.toList()));
            }
        }
        List<TopicPageBo> topicPageBoList = topicBaseService.queryPageTopicPoAll(topicVo);
        if (!AssertUtils.isNotEmpty(agentKeyWordRespFcList) && AssertUtils.isNotEmpty(topicPageBoList)) {
            List<String> userIdList = topicPageBoList.stream().map(topicPageBo -> topicPageBo.getUserId()).collect(Collectors.toList());
            ResultObject<List<AgentResponse>> agents = agentExtApi.agentsGetByUserId(userIdList);
            AssertUtils.isResultObjectError(this.getLogger(), agents);
            agentKeyWordRespFcList = (List<AgentKeyWordResponse>) this.converterList(agents.getData(), new TypeToken<List<AgentKeyWordResponse>>() {
            }.getType());
        }
        int totalLine = 0;
        List<PositionSignInResponse> positionSignInList = null;
        if (AssertUtils.isNotEmpty(topicPageBoList)) {
            List<String> types = new ArrayList<>();
            types.add(TerminologyTypeEnum.BRANCH_NAME.name());
            Map<String, List<SyscodeResponse>> internationalList = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), types).getData();

            List<String> branchIdList = topicPageBoList.stream().map(topicPageBo -> topicPageBo.getBranchId()).collect(Collectors.toList());
            ResultObject<List<BranchResponse>> listResultObject = platformBranchBaseApi.queryBranchTreeLeafListById(branchIdList);
            AssertUtils.isResultObjectError(this.getLogger(), listResultObject);
            List<BranchResponse> branchBaseRespFcList = listResultObject.getData();
            for (TopicPageBo topicPageBo : topicPageBoList) {
                if (AssertUtils.isNotEmpty(agentKeyWordRespFcList)) {
                    agentKeyWordRespFcList.forEach(agentKeyWordRespFc -> {
                        if (topicPageBo.getUserId().equals(agentKeyWordRespFc.getUserId())) {
                            topicPageBo.setAgentName(agentKeyWordRespFc.getAgentName());
                            topicPageBo.setAgentCode(agentKeyWordRespFc.getAgentCode());
                            return;
                        }
                    });
                }
                if (AssertUtils.isNotEmpty(branchBaseRespFcList)) {
                    branchBaseRespFcList.forEach(branchBaseRespFc -> {
                        if (branchBaseRespFc.getBranchId().equals(topicPageBo.getBranchId())) {
                            topicPageBo.setBranchName(branchBaseRespFc.getBranchShortname());
                            if (AssertUtils.isNotNull(internationalList)) {
                                // 机构信息国际化术语
                                List<SyscodeResponse> syscodeResponses = internationalList.get(TerminologyTypeEnum.BRANCH_NAME.name());

                                //机构信息国际化
                                if (AssertUtils.isNotEmpty(syscodeResponses)) {
                                    topicPageBo.setBranchName(this.queryOneTerminologyListName(syscodeResponses, branchBaseRespFc.getBranchId()));
                                }
                            }
                            return;
                        }
                    });
                }
            }
            positionSignInList = (List<PositionSignInResponse>) this.converterList(topicPageBoList, new TypeToken<List<PositionSignInResponse>>() {
            }.getType());
        }
        resultObject.setData(positionSignInList);
        return resultObject;
    }

    @Override
    public void exportPositionSignIn(HttpServletResponse response, PositionSignInRequest positionSignInRequest, Users currentLoginUsers) {
        try {
            //请求数据校验
            //AssertUtils.isNotEmpty(this.getLogger(), positionSignInRequest.getBranchId(), PARTY_EXPORT_POSITION_SIGN_BRANCH_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), positionSignInRequest.getPublishStartDate(), PARTY_EXPORT_POSITION_SIGN_PUBLISH_DATE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), positionSignInRequest.getPublishEndDate(), PARTY_EXPORT_POSITION_SIGN_PUBLISH_DATE_IS_NOT_NULL);

            //查询签到数据
            List<PositionSignInResponse> positionSignInResponses = this.queryPositionSignInAll(positionSignInRequest, currentLoginUsers).getData();
            //导出数据
            this.exportCommonPosition(response, positionSignInResponses, currentLoginUsers);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                throw new RequestException(error.getiEnum());
            } else {
                throw new RequestException(PARTY_EXPORT_POSITION_SIGN_ERROR);
            }
        }

    }

    /**
     * 导出位置签到列表
     */
    private void exportCommonPosition(HttpServletResponse response, List<PositionSignInResponse> positionSignInResponses, Users currentLoginUsers) throws Exception {
            //由输入流得到工作簿
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentApi.templateGet(PartyTermEnum.IMPORT_EXPORT_TEMPLATE.CHECK_IN_REPORT.name());
            URL url = new URL(attachmentRespFcResultObject.getData().getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            //得到工作表
            XSSFSheet sheet = workbook.getSheetAt(0);
            try {
                int num = 0;
                int total = AssertUtils.isNotNull(positionSignInResponses) && positionSignInResponses.size() > 0 ? positionSignInResponses.size() + 2 : sheet.getLastRowNum();
                System.out.println("====================================exports size================================================");
                System.out.println("total:" + total);
                System.out.println("====================================exports size================================================");

                for (int i = 2; i < total; i++) {
                    if (!(positionSignInResponses.size() > num && AssertUtils.isNotNull(positionSignInResponses.get(num)))) {
                        break;
                    }
                    PositionSignInResponse positionSignInResponse = positionSignInResponses.get(num);
                    num++;
                    Row writeRow = sheet.getRow(i);
                    if (!AssertUtils.isNotNull(writeRow)) {
                        Row sheetRow = sheet.getRow(i - 1);
                        writeRow = sheet.createRow(i);
                        copyRow(workbook, sheet, sheetRow, writeRow, false);
                    }
                    int c = 0;
                    writeRow.getCell(c++).setCellValue(i - 1);
                    String agentName = positionSignInResponse.getAgentName();
                    writeRow.getCell(c++).setCellValue(agentName);
                    String agentCode = positionSignInResponse.getAgentCode();
                    writeRow.getCell(c++).setCellValue(agentCode);
                    String branchName = positionSignInResponse.getBranchName();
                    writeRow.getCell(c++).setCellValue(branchName);
                    Long publishTime = positionSignInResponse.getPublishTime();
                    String publishTimeFormat = DateUtils.timeStrToString(publishTime, DateUtils.FORMATE5);
                    writeRow.getCell(c++).setCellValue(publishTimeFormat);
                    String address = positionSignInResponse.getAddress();
                    writeRow.getCell(c++).setCellValue(address);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("Check-In Report 位置签到.xls", "UTF-8"));
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();
    }

    public String queryOneTerminologyListName(List<SyscodeResponse> syscodeRespFcList, String codeKey) {
        if (AssertUtils.isNotEmpty(syscodeRespFcList) && AssertUtils.isNotEmpty(codeKey)) {
            for (SyscodeResponse syscodeRespFc : syscodeRespFcList) {
                if (codeKey.equals(syscodeRespFc.getCodeKey())) {
                    return syscodeRespFc.getCodeName();
                }
            }
        }
        return null;
    }

    public static void copyRow(Workbook wb, Sheet sheet, Row fromRow, Row toRow, boolean copyValueFlag) {
        toRow.setHeight(fromRow.getHeight());
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress cellRangeAddress = sheet.getMergedRegion(i);
            if (cellRangeAddress.getFirstRow() == fromRow.getRowNum()) {
                CellRangeAddress newCellRangeAddress = new CellRangeAddress(toRow.getRowNum(), (toRow.getRowNum() + (cellRangeAddress.getLastRow() - cellRangeAddress.getFirstRow())), cellRangeAddress.getFirstColumn(), cellRangeAddress.getLastColumn());
                sheet.addMergedRegion(newCellRangeAddress);
            }
        }
        for (Iterator cellIt = fromRow.cellIterator(); cellIt.hasNext(); ) {
            Cell tmpCell = (Cell) cellIt.next();
            Cell newCell = toRow.createCell(tmpCell.getColumnIndex());
            copyCell(wb, tmpCell, newCell, copyValueFlag);
        }
    }

    public static void copyCell(Workbook wb, Cell srcCell, Cell distCell, boolean copyValueFlag) {
        CellStyle newstyle = wb.createCellStyle();
        newstyle.cloneStyleFrom(srcCell.getCellStyle());
        distCell.setCellStyle(newstyle);
        if (srcCell.getCellComment() != null) {
            distCell.setCellComment(srcCell.getCellComment());
        }
        int srcCellType = srcCell.getCellType();
        distCell.setCellType(srcCellType);
        if (copyValueFlag) {
            if (srcCellType == HSSFCell.CELL_TYPE_NUMERIC) {
                if (HSSFDateUtil.isCellDateFormatted(srcCell)) {
                    distCell.setCellValue(srcCell.getDateCellValue());
                } else {
                    distCell.setCellValue(srcCell.getNumericCellValue());
                }
            } else if (srcCellType == HSSFCell.CELL_TYPE_STRING) {
                distCell.setCellValue(srcCell.getRichStringCellValue());
            } else if (srcCellType == HSSFCell.CELL_TYPE_BLANK) {
            } else if (srcCellType == HSSFCell.CELL_TYPE_BOOLEAN) {
                distCell.setCellValue(srcCell.getBooleanCellValue());
            } else if (srcCellType == HSSFCell.CELL_TYPE_ERROR) {
                distCell.setCellErrorValue(srcCell.getErrorCellValue());
            } else if (srcCellType == HSSFCell.CELL_TYPE_FORMULA) {
                distCell.setCellFormula(srcCell.getCellFormula());
            } else {
            }
        }
    }

    @Override
    public ResultObject<PositionSignInDetailResponse> queryPositionSignInDetail(String topicId, Users currentLoginUsers) {
        ResultObject<PositionSignInDetailResponse> resultObject = new ResultObject<>();
        //获取帖子数据
        TopicBo topicBo = topicBaseService.queryTopicPo(topicId);
        AssertUtils.isNotNull(this.getLogger(), topicBo, PARTY_TOPIC_SIGNIN_IS_NOT_NULL);
        PositionSignInDetailResponse signInDetailResponse = (PositionSignInDetailResponse) this.converterObject(topicBo, PositionSignInDetailResponse.class);

        //业务员级别信息国际化术语
        List<String> types = new ArrayList<>();
        types.add("AGENT_LEVEL");
        Map<String, List<SyscodeResponse>> internationalList = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), types).getData();

        if(AssertUtils.isNotNull(signInDetailResponse.getPublishTime())){
            Long publishTime = signInDetailResponse.getPublishTime();
            String week = DateUtils.timeToWeek(publishTime, currentLoginUsers.getLanguage());
            String publishTimeFormat = DateUtils.timeStrToString(publishTime, DateUtils.FORMATE6);
            if(AssertUtils.isNotEmpty(week)&&AssertUtils.isNotEmpty(publishTimeFormat)){
                publishTimeFormat = publishTimeFormat.replace(" ", " "+week+" ");
                signInDetailResponse.setPublishTimeFormat(publishTimeFormat);
            }
        }

        //获取帖子附件
        List<TopicAttachmentBo> attachmentBoList = topicAttachmentBaseService.queryAttachmentByTopicId(topicId);
        if (AssertUtils.isNotEmpty(attachmentBoList)) {
            List<TopicAttachmentResponse> attachmentList = (List<TopicAttachmentResponse>) this.converterList(attachmentBoList, new TypeToken<List<TopicAttachmentResponse>>() {
            }.getType());
            signInDetailResponse.setAttachmentList(attachmentList);
        }
        //获取代理人信息
        ResultObject<AgentBaseResponse> agentBaseResponseResultObject = agentBaseAgentApi.queryOneAgentById(topicBo.getUserId());
        AssertUtils.isResultObjectError(this.getLogger(), agentBaseResponseResultObject);
        AgentBaseResponse agent = agentBaseResponseResultObject.getData();
        PositionSignInDetailAgentResponse agentResponse = (PositionSignInDetailAgentResponse) this.converterObject(agent, PositionSignInDetailAgentResponse.class);
        AgentDetailBaseResponse agentDetail = agent.getAgentDetail();
        //设置手机
        if (AssertUtils.isNotNull(agentDetail)) {
            agentResponse.setAgentMobile(agentDetail.getMobile());
        }
        //设置职级
        AgentLevelBaseResponse agentLevel = agent.getAgentLevel();
        if (AssertUtils.isNotNull(agentLevel)) {
            agentResponse.setAgentLevelName(agentLevel.getAgentLevelName());
            if (AssertUtils.isNotNull(internationalList)) {
                // 业务员级别信息国际化术语
                List<SyscodeResponse> syscodeResponses = internationalList.get("AGENT_LEVEL");

                //业务员级别信息国际化术语
                if (AssertUtils.isNotEmpty(syscodeResponses)) {
                    agentResponse.setAgentLevelName(this.queryOneTerminologyListName(syscodeResponses, agentLevel.getAgentLevelCode()));
                }
            }
        }
        BranchResponse branch = platformBranchBaseApi.queryOneBranchById(agent.getBranchId()).getData();
        if (AssertUtils.isNotNull(branch)) {
            agentResponse.setBranchName(branch.getBranchName());
        }
        signInDetailResponse.setAgent(agentResponse);
        resultObject.setData(signInDetailResponse);
        return resultObject;
    }
}

package com.gclife.party.service;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.party.model.request.InvestigateCustomerQueryRequest;
import com.gclife.party.model.response.InvestigateCustomerListResponse;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 17:17 2018/10/15
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
public interface InvestigateCustomerService {
    /**
     * 查询客户调查表
     *
     * @param investigateCustomerRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<BasePageResponse<InvestigateCustomerListResponse>> queryInvestigateCustomerList(InvestigateCustomerQueryRequest investigateCustomerRequest, Users currentLoginUsers);

    /**
     * app 查询调查客户信息
     * @param investigateCustomerRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<BasePageResponse<InvestigateCustomerListResponse>> queryAppInvestigateCustomerList(InvestigateCustomerQueryRequest investigateCustomerRequest, Users currentLoginUsers);
}

package com.gclife.party.service.impl;

import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.party.core.jooq.tables.pojos.PromotionalCodesPo;
import com.gclife.party.model.response.PromotionalCodesResponse;
import com.gclife.party.service.PromotionalCodesService;
import com.gclife.party.service.base.PromotionalCodesBaseService;
import com.gclife.product.api.ProductApi;
import com.gclife.product.model.response.ActivityDetailResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.gclife.party.model.config.PartyErrorConfigEnum.PROMOTIONAL_CODES_CODE_IS_NOT_NULL;
import static com.gclife.party.model.config.PartyErrorConfigEnum.PROMOTIONAL_CODES_CODE_IS_NOT_RIGHT;

@Service
public class PromotionalCodesServiceImpl extends BaseBusinessServiceImpl implements PromotionalCodesService {

    @Autowired
    private PromotionalCodesBaseService promotionalCodesBaseService;
    @Autowired
    private ProductApi productApi;

    @Override
    public ResultObject<PromotionalCodesResponse> queryPromotionalCodes(String code, String flag, String channelType) {
        ResultObject<PromotionalCodesResponse> resultObject = new ResultObject<>();
        PromotionalCodesResponse promotionalCodesResponse = new PromotionalCodesResponse();
        AssertUtils.isNotEmpty(this.getLogger(), code, PROMOTIONAL_CODES_CODE_IS_NOT_NULL);
        PromotionalCodesPo promotionalCodesPo = promotionalCodesBaseService.queryPromotionalCodes(code);
        if (!"eMoney".equals(channelType)) {
            AssertUtils.isNotNull(this.getLogger(), promotionalCodesPo, PROMOTIONAL_CODES_CODE_IS_NOT_RIGHT);
        }
        if (AssertUtils.isNotNull(promotionalCodesPo)) {
            promotionalCodesResponse.setPromotionalCodesCod(promotionalCodesPo.getPromotionalCodesCode());
            promotionalCodesResponse.setActivityId(promotionalCodesPo.getActivityId());
            // flag有值作为产品服务间api调用，不用再查产品了，省资源时间
            if (AssertUtils.isNotEmpty(promotionalCodesPo.getActivityId()) && !AssertUtils.isNotEmpty(flag)) {
                ResultObject<ActivityDetailResponse> responseResultObject = productApi.queryActivityDetail(promotionalCodesPo.getActivityId());
                if (!AssertUtils.isResultObjectDataNull(responseResultObject)) {
                    String activityStatus = responseResultObject.getData().getActivityStatus();
                    if (AssertUtils.isNotEmpty(activityStatus) && "IN_PROGRESS".equals(activityStatus)) {
                        promotionalCodesResponse.setActivityIsShowFlag("YES");
                    }
                }
            }
        }
        resultObject.setData(promotionalCodesResponse);
        return resultObject;
    }

    @Override
    public String verifyPromotionalCodes(String url, String code) {
        getLogger().info("url:" + url);
        getLogger().info("code:" + code);
        String params = "?productId=PRO880000000000020A&" + "t=" + DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(code) && code.contains("OL00003")) {
            String appendString = "&channelType=eMoney";
            //默认跳Product Features
            String point = "&point=features";
            if (code.contains("/")) {
                String[] split = code.split("/");
                for (String s : split) {
                    // 跳转Insurance Plan
                    if ("plan".equals(s)) {
                        point = "&point=plan";
                    } else if ("kh".equals(s)) {
                        // 柬文
                        url = url + "/kh";
                    }
                }
            }
            params = params + appendString + point;
        } else {
            PromotionalCodesPo promotionalCodesPo = promotionalCodesBaseService.queryPromotionalCodes(code);
            if (AssertUtils.isNotNull(promotionalCodesPo)) {
                //跳转成功页面
                params = params + "&code=" + code;
            }
        }
        String path = "https://" + url + "/fastprotect" + params;
        getLogger().info("重定向之后的页面" + path);
        return path;
    }
}

package com.gclife.party.service;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.party.model.request.policy.ClientPolicyQueryRequest;
import com.gclife.party.model.request.policy.DutyClassResponse;
import com.gclife.party.model.request.policy.PictureBatchRequest;
import com.gclife.party.model.request.policy.PolicyRequest;
import com.gclife.party.model.response.policy.*;

import java.util.List;


/**
 * 客户保单
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 22-09-01
 */
public interface CustomerPolicyService {

    /**
     * 保单保存
     * @param policyRequest
     * @param users 用户
     * @return
     */
    ResultObject savePolicy(PolicyRequest policyRequest, Users users);

    /**
     * 保单列表
     * @param customerId 客户ID
     * @return
     */
    ResultObject<List<CustomerPolicyListResponse>> listPolicy(String customerId);

    /**
     * 录入保单详情
     * @param policyId 保单ID
     * @return
     */
    ResultObject<CustomerPolicyResponse> queryPolicy(String policyId);

    /**
     * 录入保单删除
     * @param policyId 保单ID
     * @return
     */
    ResultObject deletePolicy(String policyId);

    /**
     * 图片保单保存
     * @param pictureBatchRequest
     * @param users 用户
     * @return
     */
    ResultObject savePicturePolicy(PictureBatchRequest pictureBatchRequest, Users users);

    /**
     * 图片保单列表
     * @param customerId 客户Id
     * @return
     */
    ResultObject<List<PictureListResponse>> listPicturePolicy(String customerId);

    /**
     * 图片保单信息
     * @param pictureBatchId 图片批次ID
     * @param oneselfCustomerId 本人客户ID
     * @return
     */
    ResultObject<PictureDetailResponse> picturePolicyDetail(String pictureBatchId, String oneselfCustomerId);

    /**
     * 图片保单取消申请
     * @param pictureBatchId 图片批次ID
     * @param users 用户
     * @return
     */
    ResultObject picturePolicyCancel(String pictureBatchId, Users users);

    /**
     * 图片保单删除
     * @param pictureBatchId 图片批次ID
     * @return
     */
    ResultObject deletePicturePolicy(String pictureBatchId);

    /**
     * 查询保险公司
     * @return
     */
    ResultObject listProvider();

    /**
     * 字典数据
     * @param appRequestHeads 请求头
     * @return
     */
    ResultObject<PolicyDicResponse> getPolicyDic(AppRequestHeads appRequestHeads);

    /**
     * 查询保障类别库
     * @return
     */
    ResultObject<List<DutyClassResponse>> listDutyClass();

    /**
     * client保单列表
     * @param queryRequest
     * @return
     */
    ResultObject<List<ClientPolicyListResponse>> listClientPolicy(ClientPolicyQueryRequest queryRequest);
}
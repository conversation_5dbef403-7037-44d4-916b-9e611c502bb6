package com.gclife.party.service.visit.impl;

import com.gclife.agent.api.AgentApi;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.endorse.api.EndorseAcceptApi;
import com.gclife.endorse.model.response.AcceptApplyResponse;
import com.gclife.party.core.jooq.tables.daos.ReturnVisitAttachmentDao;
import com.gclife.party.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.party.dao.ReturnVisitExtDao;
import com.gclife.party.model.bo.ReturnVisitBo;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.visit.ReturnVisitPageRequest;
import com.gclife.party.model.response.visit.EndorseReturnVisitListResponse;
import com.gclife.party.model.response.visit.PolicyReturnVisitResponse;
import com.gclife.party.service.base.ReturnVisitBaseService;
import com.gclife.party.service.visit.EndorseReturnVisitBusinessService;
import com.gclife.platform.api.PlatformAreaApi;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: chenjinrong
 * @Date: 19-10-23 17:42
 * @Description:
 */
@Service
public class EndorseReturnVisitBusinessServiceImpl extends BaseServiceImpl implements EndorseReturnVisitBusinessService {

    @Autowired
    PolicyApi policyApi;

    @Autowired
    AgentApi agentApi;

    @Autowired
    PlatformBranchApi platformBranchApi;

    @Autowired
    PlatformAreaApi platformAreaApi;
    @Autowired
    ReturnVisitBaseService returnVisitBaseService;

    @Autowired
    PlatformBaseInternationServiceApi platformBaseInternationServiceApi;

    @Autowired
    ReturnVisitExtDao returnVisitExtDao;

    @Autowired
    ReturnVisitAttachmentDao returnVisitAttachmentDao;

    @Autowired
    EndorseAcceptApi endorseAcceptApi;

    @Override
    public ResultObject getEndorseReturnVisitList(ReturnVisitPageRequest pageRequest) {
        ResultObject<BasePageResponse<EndorseReturnVisitListResponse>> resultObject = ResultObject.success();
        List<ReturnVisitBo> returnVisitBoList = returnVisitExtDao.queryReturnVisitList(pageRequest);

        List<EndorseReturnVisitListResponse> policyReturnVisitResponseList = new ArrayList<>();
        returnVisitBoList.stream().forEach(returnVisitBo -> {
            EndorseReturnVisitListResponse returnVisitListResponse = new EndorseReturnVisitListResponse();
            returnVisitListResponse.setReturnVisitId(returnVisitBo.getReturnVisitId());

            ResultObject<AcceptApplyResponse> acceptApplyResponseResultObject = endorseAcceptApi.queryApplyInfo(returnVisitBo.getBusinessId());
            String policyId = "";
            if(!AssertUtils.isResultObjectDataNull(acceptApplyResponseResultObject)){
                policyId = acceptApplyResponseResultObject.getData().getApplyId();
            }
            ResultObject<PolicyResponse> policyResponseResultObject =  policyApi.queryOnePolicy(policyId);

            if(AssertUtils.isResultObjectDataNull(policyResponseResultObject) && PartyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name().equalsIgnoreCase(policyResponseResultObject.getData().getPolicyType())){
                CustomerAgentPo customerAgentPo = returnVisitBo.getCustomerAgentPo();
                if(AssertUtils.isNotNull(customerAgentPo)){
                    returnVisitListResponse.setCustomerIdNo(customerAgentPo.getIdNo());
                    returnVisitListResponse.setCustomerName(customerAgentPo.getName());
                    returnVisitListResponse.setCustomerMobile(customerAgentPo.getMobile());
                    returnVisitListResponse.setCustomerIdType(customerAgentPo.getIdType());
                }

            }else {
                CustomerAgentPo customerAgentPo = returnVisitBo.getCustomerAgentPo();
                if(AssertUtils.isNotNull(customerAgentPo)){
                    returnVisitListResponse.setCustomerIdNo(customerAgentPo.getDelegateIdNo());
                    returnVisitListResponse.setCustomerName(customerAgentPo.getDelegateName());
                    returnVisitListResponse.setCustomerMobile(customerAgentPo.getDelegateMobile());
                    returnVisitListResponse.setCustomerIdType(customerAgentPo.getDelegateIdType());
                }
            }

            returnVisitListResponse.setReturnVisitStatus(returnVisitBo.getReturnVisitStatus());
            returnVisitListResponse.setReturnVisitStatusName(PartyTermEnum.RETURN_VISIT_STATUS.fromCode(returnVisitBo.getReturnVisitStatus()).desc());
            returnVisitListResponse.setReturnVisitType(returnVisitBo.getReturnVisitType());
            returnVisitListResponse.setBusinessType(returnVisitBo.getBusinessType());
            returnVisitListResponse.setBusinessTypeName(PartyTermEnum.ENDORSE_BUSINESS_TYPE.fromCode(returnVisitBo.getReturnVisitType()).desc());
            returnVisitListResponse.setReturnVisitTypeName(PartyTermEnum.RETURN_VISIT_TYPE.fromCode(returnVisitBo.getReturnVisitType()).desc());
            policyReturnVisitResponseList.add(returnVisitListResponse);
        });
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(returnVisitBoList) ? returnVisitBoList.get(0).getTotalLine() : null;

        BasePageResponse basePageResponse = BasePageResponse.getData(pageRequest.getCurrentPage(), pageRequest.getPageSize(), totalLine, policyReturnVisitResponseList);
        resultObject.setData(basePageResponse);


        return resultObject;
    }
}

package com.gclife.party.service.account.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.party.core.jooq.tables.pojos.UserAccountOperateReviewPo;
import com.gclife.party.model.bo.AccountOperateListBo;
import com.gclife.party.model.bo.UserAccountBo;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.account.AccountOperateListRequest;
import com.gclife.party.model.request.account.AccountOperateRequest;
import com.gclife.party.model.request.account.AccountOperateReviewRequest;
import com.gclife.party.model.response.account.AccountOperateListResponse;
import com.gclife.party.model.response.account.AccountOperateReviewListResponse;
import com.gclife.party.model.vo.AccountOperateListVo;
import com.gclife.party.service.account.AccountActivityService;
import com.gclife.party.service.base.UserAccountBaseService;
import com.gclife.party.validate.transfer.UserAccountTransfer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static com.gclife.party.model.config.PartyErrorConfigEnum.*;

/**
 * <AUTHOR>
 * create 19-7-12
 * description:
 */
@Service
public class AccountActivityServiceImpl extends BaseBusinessServiceImpl implements AccountActivityService {
    @Autowired
    private UserAccountBaseService userAccountBaseService;
    @Autowired
    private UserAccountTransfer userAccountTransfer;

    /**
     * 账户解冻列表
     *
     * @param accountOperateListRequest 列表参数
     * @param users                     用户
     * @return AccountOperateListResponse
     */
    @Override
    public ResultObject<BasePageResponse<AccountOperateListResponse>> getAccountActivityList(AccountOperateListRequest accountOperateListRequest, Users users) {
        ResultObject<BasePageResponse<AccountOperateListResponse>> resultObject = new ResultObject<>();

        AccountOperateListVo accountOperateListVo = (AccountOperateListVo) this.converterObject(accountOperateListRequest, AccountOperateListVo.class);
        accountOperateListVo.setAccountStatus(PartyTermEnum.ACCOUNT_STATUS.FROZEN.name());
        List<AccountOperateListBo> accountOperateListBos = userAccountBaseService.getAccountOperateList(accountOperateListVo);

        List<AccountOperateListResponse> accountOperateListResponses = userAccountTransfer.transAccountOperateList(accountOperateListBos, users);
        Integer totalLine = AssertUtils.isNotNull(accountOperateListBos) ? accountOperateListBos.get(0).getTotalLine() : null;
        BasePageResponse basePageResponse = BasePageResponse.getData(accountOperateListRequest.getCurrentPage(), accountOperateListRequest.getPageSize(), totalLine, accountOperateListResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    /**
     * 申请账户解冻
     *
     * @param accountOperateRequest 申请参数
     * @param users                 用户
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postAccountActivity(AccountOperateRequest accountOperateRequest, Users users) {
        ResultObject resultObject = new ResultObject();
        this.getLogger().info("accountOperateRequest:{}", JSON.toJSONString(accountOperateRequest));

        String userAccountId = accountOperateRequest.getUserAccountId();
        AssertUtils.isNotEmpty(this.getLogger(), userAccountId, PARTY_USER_ACCOUNT_ID_IS_NOT_NULL);

        UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBoById(userAccountId);
        AssertUtils.isNotNull(this.getLogger(), userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);
        if (PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name().equals(userAccountBo.getAccountStatus())) {
            throw new RequestException(PARTY_THE_ACCOUNT_IS_UNFROZEN_AND_CANNOT_BE_OPERATED);
        }

        //录入备注信息，点击提交按钮，该账户信息提交到解冻审批岗；点击取消关闭弹窗。
        //校验信息：若该账户已发起解冻申请，流程在解冻审批未确认之前时，再次发起解冻申请时须提示“该账户已发起解冻申请，不允许再次提交”。
        UserAccountOperateReviewPo userAccountOperateReview = userAccountBaseService.getUserAccountOperateReview(userAccountId, PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name());
        if (AssertUtils.isNotNull(userAccountOperateReview) && userAccountOperateReview.getReviewStatus().equals(PartyTermEnum.ACCOUNT_REVIEW_STATUS.INITIAL.name())) {
            throw new RequestException(PARTY_THE_ACCOUNT_HAS_INITIATED_A_UNFREEZE_REQUEST_ERROR);
        }

        UserAccountOperateReviewPo userAccountOperateReviewPo = new UserAccountOperateReviewPo();
        userAccountOperateReviewPo.setUserAccountId(userAccountId);
        userAccountOperateReviewPo.setOperateType(PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name());
        userAccountOperateReviewPo.setApplyRemark(accountOperateRequest.getRemark());
        userAccountOperateReviewPo.setApplyDate(DateUtils.getCurrentTime());
        userAccountOperateReviewPo.setApplyUserId(users.getUserId());
        userAccountOperateReviewPo.setReviewStatus(PartyTermEnum.ACCOUNT_REVIEW_STATUS.INITIAL.name());
        userAccountBaseService.saveUserAccountOperateReview(userAccountOperateReviewPo, users.getUserId());

        //发送申请冻结消息
        userAccountTransfer.sendAccountReviewMessage(PartyTermEnum.MSG_BUSINESS_TYPE.ACCOUNT_UNFREEZE_AUDIT_MESSAGE_REMINDER.name(), userAccountBo.getUserId(), users);
        return resultObject;
    }

    /**
     * 账户解冻列表
     *
     * @param accountOperateListRequest 列表参数
     * @param users                     用户
     * @return AccountOperateReviewListResponse
     */
    @Override
    public ResultObject<BasePageResponse<AccountOperateReviewListResponse>> getAccountActivityReviewList(AccountOperateListRequest accountOperateListRequest, Users users) {
        ResultObject<BasePageResponse<AccountOperateReviewListResponse>> resultObject = new ResultObject<>();

        AccountOperateListVo accountOperateListVo = (AccountOperateListVo) this.converterObject(accountOperateListRequest, AccountOperateListVo.class);
        accountOperateListVo.setAccountStatus(PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name());
        List<AccountOperateListBo> accountOperateListBos = userAccountBaseService.getAccountOperateReviewList(accountOperateListVo);

        List<AccountOperateReviewListResponse> accountOperateListResponses = userAccountTransfer.transAccountOperateReviewList(accountOperateListBos, users);
        Integer totalLine = AssertUtils.isNotNull(accountOperateListBos) ? accountOperateListBos.get(0).getTotalLine() : null;
        BasePageResponse basePageResponse = BasePageResponse.getData(accountOperateListRequest.getCurrentPage(), accountOperateListRequest.getPageSize(), totalLine, accountOperateListResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    /**
     * 操作账户解冻审批
     *
     * @param accountOperateReviewRequest 申请参数
     * @param users                       用户
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postAccountActivityReview(AccountOperateReviewRequest accountOperateReviewRequest, Users users) {
        ResultObject resultObject = new ResultObject();
        this.getLogger().info("accountOperateReviewRequest:{}", JSON.toJSONString(accountOperateReviewRequest));

        List<String> userAccountIds = accountOperateReviewRequest.getUserAccountIds();
        AssertUtils.isNotEmpty(this.getLogger(), userAccountIds, PARTY_USER_ACCOUNT_ID_IS_NOT_NULL);
        String reviewResult = accountOperateReviewRequest.getReviewResult();
        AssertUtils.isNotEmpty(this.getLogger(), reviewResult, PARTY_REVIEW_RESULT_IS_NOT_NULL);
        if (!Arrays.toString(TerminologyConfigEnum.WHETHER.values()).contains(reviewResult)) {
            throw new RequestException(PARTY_REVIEW_RESULT_FORMAT_ERROR);
        }
        userAccountIds.forEach(userAccountId -> {
            UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBoById(userAccountId);
            AssertUtils.isNotNull(this.getLogger(), userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);
            if (PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name().equals(userAccountBo.getAccountStatus())) {
                throw new RequestException(PARTY_THE_ACCOUNT_IS_UNFROZEN_AND_CANNOT_BE_OPERATED);
            }

            UserAccountOperateReviewPo userAccountOperateReviewPo = userAccountBaseService.getUserAccountOperateReview(userAccountId, PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name());
            if (!AssertUtils.isNotNull(userAccountOperateReviewPo) || !userAccountOperateReviewPo.getReviewStatus().equals(PartyTermEnum.ACCOUNT_REVIEW_STATUS.INITIAL.name())) {
                throw new RequestException(PARTY_ACCOUNT_APPLICATION_UNFREEZE_IS_NOT_FOUND_OBJECT);
            }

            userAccountOperateReviewPo.setReviewRemark(accountOperateReviewRequest.getRemark());
            userAccountOperateReviewPo.setReviewDate(DateUtils.getCurrentTime());
            userAccountOperateReviewPo.setReviewUserId(users.getUserId());
            if (TerminologyConfigEnum.WHETHER.NO.name().equals(reviewResult)) {
                userAccountOperateReviewPo.setReviewStatus(PartyTermEnum.ACCOUNT_REVIEW_STATUS.AUDIT_NO_PASS.name());
            }
            if (TerminologyConfigEnum.WHETHER.YES.name().equals(reviewResult)) {
                userAccountOperateReviewPo.setReviewStatus(PartyTermEnum.ACCOUNT_REVIEW_STATUS.AUDIT_PASS.name());
                //审核通过更改账户状态
                userAccountBo.setAccountStatus(PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name());
                userAccountBo.setUnfreezeDate(DateUtils.getCurrentTime());
                userAccountBo.setFreezeReasonCode(null);
                userAccountBo.setFreezeDate(null);
                userAccountBaseService.saveUserAccount(userAccountBo, users.getUserId());
            }
            userAccountBaseService.saveUserAccountOperateReview(userAccountOperateReviewPo, users.getUserId());

        });
        return resultObject;
    }
}

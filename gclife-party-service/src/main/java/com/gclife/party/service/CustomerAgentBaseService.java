package com.gclife.party.service;

import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseService;
import com.gclife.party.model.response.CustomerAgentsResponse;

/**
 * <AUTHOR>
 * @date 2022/6/1
 */
public interface CustomerAgentBaseService extends BaseService {

    /**
     * 获取代理人客户信息
     *
     * @param customerAgentId
     * @return
     */
    ResultObject<CustomerAgentsResponse> getBaseCustomerAgent(String customerAgentId);
}

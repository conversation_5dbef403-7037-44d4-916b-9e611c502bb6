package com.gclife.party.service;

import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.ProductEnquiryRequest;

import javax.servlet.http.HttpServletResponse;

/**
 * @ Author     : LiChongFu
 * @ Date       : Created in 20:09 2021/04/29
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */

public interface ProductEnquiryService {

    /**
     * 导出产品询盘表格
     *
     * @param httpServletResponse
     */
    void exportProductEnquiry(HttpServletResponse httpServletResponse);

    /**
     * 保存特别保护用户消息
     *
     * @param productEnquiryRequest
     * @return
     */
    ResultObject saveProductEnquiry(ProductEnquiryRequest productEnquiryRequest);
}

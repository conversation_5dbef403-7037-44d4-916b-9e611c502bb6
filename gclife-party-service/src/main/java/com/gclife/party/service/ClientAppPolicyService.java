package com.gclife.party.service;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.party.model.request.client.ClientManageListRequest;
import com.gclife.party.model.request.client.ClientPolicyEntryRequest;
import com.gclife.party.model.request.client.ClientPolicyErrorRequest;
import com.gclife.party.model.request.policy.DutyClassResponse;
import com.gclife.party.model.response.client.ClientCustomerListResponse;
import com.gclife.party.model.response.client.ClientManagePolicyDetailResponse;
import com.gclife.party.model.response.client.PolicyManageListResponse;
import com.gclife.party.model.response.client.PolicyOptionRecordResponse;
import com.gclife.product.model.response.ProviderResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 2022/8/17 17:05
 * description:
 */
public interface ClientAppPolicyService {

    /**
     * 客户管理保单录入列表
     *
     * @param clientPolicyListRequest 保单录入列表
     * @return PolicyManageListResponse
     */
    ResultObject<BasePageResponse<PolicyManageListResponse>> queryClientPolicyList(ClientManageListRequest clientPolicyListRequest,Users users);

    /**
     * 签收/取消保单
     *
     * @param policyId   保单
     * @param cancelFlag 取消标识
     * @param users      用户
     * @return Void
     */
    ResultObject<Void> signPolicy(String policyId, String cancelFlag, Users users);

    /**
     * 录入保单异常处理
     *
     * @param clientPolicyErrorRequest
     * @param users                    用户
     * @return Void
     */
    ResultObject<Void> errorPolicy(ClientPolicyErrorRequest clientPolicyErrorRequest, Users users);

    /**
     * 查询保单详情
     *
     * @param policyId 保单ID
     * @param appRequestHeads
     * @return
     */
    ResultObject<ClientManagePolicyDetailResponse> detail(String policyId, AppRequestHeads appRequestHeads);

    /**
     * 保单流程记录
     *
     * @param policyId 保单ID
     * @return 保单流程记录
     */
    ResultObject<List<PolicyOptionRecordResponse>> getOptionRecordList(String policyId);

    /**
     * 保单提交
     *
     * @param clientPolicyEntryRequest 保单录入请求参数
     * @param users                    用户
     * @return Void
     */
    ResultObject<Void> savePolicy(ClientPolicyEntryRequest clientPolicyEntryRequest, Users users);

    /**
     * 查询保险公司
     *
     * @return ProviderResponses
     */
    ResultObject<List<ProviderResponse>> listProvider();

    /**
     * 查询保障类别库
     *
     * @return DutyClassResponses
     */
    ResultObject<List<DutyClassResponse>> listDutyClass();

    /**
     * 该保单下的家庭成员列表
     *
     * @param policyId
     * @param keyword
     * @return
     */
    ResultObject<List<ClientCustomerListResponse>> listMember(String policyId, String keyword);
}

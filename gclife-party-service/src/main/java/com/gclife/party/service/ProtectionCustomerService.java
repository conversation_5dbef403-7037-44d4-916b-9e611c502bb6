package com.gclife.party.service;

import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.ProtectionCustomerRequest;

import javax.servlet.http.HttpServletResponse;

/**
 * @ Author     : LiChongFu
 * @ Date       : Created in 20:09 2021/04/29
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
public interface ProtectionCustomerService {

    /**
     * 导出特别保护表格
     *
     * @param httpServletResponse
     */
    void exportProtectionCustomer(HttpServletResponse httpServletResponse);

    /**
     * 保存特别保护用户消息
     *
     * @param protectionCustomerRequest
     * @return
     */
    ResultObject saveProtectionCustomer(ProtectionCustomerRequest protectionCustomerRequest);


}

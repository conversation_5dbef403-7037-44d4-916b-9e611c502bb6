package com.gclife.party.service.account.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentKeyWordResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.party.core.jooq.tables.pojos.CashWithdrawalRecordPo;
import com.gclife.party.model.bo.AccountCashWithdrawalBatchBo;
import com.gclife.party.model.bo.UserAccountBo;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.UserAccountOutlayRequest;
import com.gclife.party.model.request.account.AccountRequest;
import com.gclife.party.model.request.visit.BatchWithdrawalRequest;
import com.gclife.party.model.request.visit.WithdrawalResult;
import com.gclife.party.model.response.account.AccountResponse;
import com.gclife.party.model.vo.AccountVo;
import com.gclife.party.service.account.AccountBatchWithdrawalService;
import com.gclife.party.service.base.UserAccountBaseService;
import com.gclife.party.validate.transfer.UserAccountTransfer;
import com.gclife.payment.api.ReceiptBaseApi;
import com.gclife.payment.model.request.ReceiptItemRequest;
import com.gclife.payment.model.request.ReceiptRequest;
import com.gclife.payment.model.response.ReceiptResponse;
import com.gclife.platform.api.PlatformAccountApi;
import com.gclife.platform.api.PlatformConfigApi;
import com.gclife.platform.model.response.NotifyConfigResponse;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyErrorConfigEnum.*;


/**
 * <AUTHOR>
 * create 2021/4/6 下午6:07
 * description:
 */
@Service
@Slf4j
public class AccountBatchWithdrawalImpl extends BaseBusinessServiceImpl implements AccountBatchWithdrawalService {

    @Autowired
    private UserAccountTransfer userAccountTransfer;
    @Autowired
    private UserAccountBaseService userAccountBaseService;

    @Autowired
    private AgentApi agentApi;

    @Autowired
    private ReceiptBaseApi receiptBaseApi;

    @Autowired
    private PlatformConfigApi platformConfigApi;

    @Autowired
    private PlatformAccountApi platformAccountApi;


    @Override
    @Transactional
    public ResultObject <BasePageResponse<AccountResponse>> postCashWithdrawalBatchList(AccountRequest accountRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<AccountResponse>> resultObject = new ResultObject<>();
        AccountVo accountVo = (AccountVo) this.converterObject(accountRequest, AccountVo.class);
        if (AssertUtils.isNotEmpty(accountRequest.getKeyword())) {
            List<AgentKeyWordResponse> agentKeyWordResponseList = agentApi.loadAllAgentsByKeyword(accountRequest.getKeyword()).getData();
            if (!AssertUtils.isNotEmpty(agentKeyWordResponseList)) {
                resultObject.setData(new BasePageResponse<>());
                return resultObject;
            }
            List<String> agentIdList = agentKeyWordResponseList.stream().map(AgentKeyWordResponse::getAgentId).distinct().collect(Collectors.toList());
            accountVo.setAgentIdList(agentIdList);
        }
        //只查询激活的账户
        List<String> accountStatusList = new ArrayList<>();
        accountStatusList.add(PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name());
        //过滤当前选中的状态
        if (AssertUtils.isNotEmpty(accountVo.getAccountStatus())) {
            accountStatusList = accountStatusList.stream().filter(accountStatus -> accountStatus.equals(accountVo.getAccountStatus())).collect(Collectors.toList());
        }
        accountVo.setAccountStatusLisat(accountStatusList);
        //1.筛选所有可提现金额大于0的用户
        List<UserAccountBo> userAccountBoList = userAccountBaseService.queryAccountCashWithdrawalList(accountVo);

        if (!AssertUtils.isNotEmpty(userAccountBoList)) {
            resultObject.setData(new BasePageResponse<>());
            return resultObject;
        }

//        List<String> userIds = userAccountBoList.stream().map(AccountCashWithdrawalBatchBo::getUserId).distinct().collect(Collectors.toList());

        List<String> userIds = userAccountBoList.stream().map(UserAccountBo::getUserId).distinct().collect(Collectors.toList());


        //2.查询用户所配置的银行帐号
        ResultObject<List<com.gclife.platform.model.response.AccountResponse>> listResultObject = platformAccountApi.accountPost(userIds);
//        if (AssertUtils.isResultObjectListDataNull(listResultObject)) {
//            return resultObject;
//        }

        //3.是否配置了iCBC或ABA的银行帐号
        AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
        applyAgentRequest.setListAgentId(userIds);
        List<AgentResponse> agentResponses = agentApi.agentsGet(applyAgentRequest).getData();
        if(AssertUtils.isNotEmpty(listResultObject.getData())){
            userAccountBoList.forEach(postCashWithdrawalBatchPc ->{
                List<com.gclife.platform.model.response.AccountResponse> accountResponses = listResultObject.getData().stream().filter(accountResponse ->
                        accountResponse.getUserId().equals(postCashWithdrawalBatchPc.getUserId())
                ).collect(Collectors.toList());

                if (AssertUtils.isNotEmpty(accountResponses)) {
                    Optional<com.gclife.platform.model.response.AccountResponse> matchICBC = accountResponses.stream().filter(accountResponse ->
                            "MAIN".equals(accountResponse.getPrimaryFlag())
                    ).findAny();
                    if (matchICBC.isPresent()) {
                        com.gclife.platform.model.response.AccountResponse accountResponse = matchICBC.get();
                        postCashWithdrawalBatchPc.setAccountId(accountResponse.getAccountId());
                        postCashWithdrawalBatchPc.setBankCode(accountResponse.getBankCode());
                        postCashWithdrawalBatchPc.setAccountNo(accountResponse.getAccountNo());
                        postCashWithdrawalBatchPc.setAccountOwner(accountResponse.getAccountOwner());
                        postCashWithdrawalBatchPc.setAccountTypeCode(accountResponse.getAccountTypeCode());
                    }
                }
            });
        }

        List<AccountResponse> accountResponseList = (List<AccountResponse>) this.converterList(userAccountBoList, new TypeToken<List<AccountResponse>>() {
        }.getType());

        if (AssertUtils.isNotEmpty(agentResponses)) {
            accountResponseList.forEach(accountResponse -> agentResponses.stream().filter(agentResponse -> accountResponse.getUserId().equals(agentResponse.getUserId()))
                    .findFirst().ifPresent(agentResponse -> accountResponse.setAgentStatus(agentResponse.getAgentStatus())));
        }
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(userAccountBoList) ? userAccountBoList.get(0).getTotalLine() : null;

        BasePageResponse basePageResponse = BasePageResponse.getData(accountRequest.getCurrentPage(), accountRequest.getPageSize(), totalLine, accountResponseList);

        resultObject.setData(basePageResponse);
        return resultObject;
    }


    @Override
    @Transactional
    public ResultObject<WithdrawalResult> postCashWithdrawalExtract(BatchWithdrawalRequest batchWithdrawalRequest, Users currentLoginUsers) {
        this.getLogger().info("开始批量提现:{}", JSON.toJSONString(batchWithdrawalRequest.getUserAccountids()));

        ResultObject<WithdrawalResult> resultObject = new ResultObject<>();
        WithdrawalResult withdrawalResult =new WithdrawalResult();
        List<String> receiptIds = new ArrayList<>();

            //1.查询选中的代理人用户
            List<AccountCashWithdrawalBatchBo> cashWithdrawalBatch = userAccountBaseService.postCashWithdrawalBatchIds(batchWithdrawalRequest.getUserAccountids());
            if (!AssertUtils.isNotEmpty(cashWithdrawalBatch)) {
                log.info("本次批量提现无可提现数据");
                throw new RequestException(NO_WITHDRAWABLE_DATA);
            }

            List<String> userIds = cashWithdrawalBatch.stream().map(AccountCashWithdrawalBatchBo::getUserId).distinct().collect(Collectors.toList());

            //2.查询用户所配置的银行帐号
            ResultObject<List<com.gclife.platform.model.response.AccountResponse>> listResultObject = platformAccountApi.accountPost(userIds);
            if (AssertUtils.isResultObjectListDataNull(listResultObject)) {
                log.info("本次提现账户没有银行账号");
                throw new RequestException(THERE_IS_NO_BANK_ACCOUNT);
            }

            //3.是否配置了iCBC或ABA的银行帐号
            AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
            applyAgentRequest.setListAgentId(userIds);
            List<AgentResponse> agentResponses = agentApi.agentsGet(applyAgentRequest).getData();

            cashWithdrawalBatch.forEach(accountCashWithdrawalBatchBo -> {
                List<com.gclife.platform.model.response.AccountResponse> accountResponses = listResultObject.getData().stream().filter(accountResponse ->
                        accountResponse.getUserId().equals(accountCashWithdrawalBatchBo.getUserId())
                                && "AUDIT_PASS".equals(accountResponse.getAuditStatus())
                ).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(accountResponses)) {
                    //4.两个银行都配置了则提现至ICBC 和 ABA 其中的默认银行
                    List<String> ICBC_ABA = Arrays.asList("ICBC","ABA BANK");
                    Optional<com.gclife.platform.model.response.AccountResponse> matchICBC = accountResponses.stream().filter(accountResponse ->
                            ICBC_ABA.contains(accountResponse.getBankCode()) && "MAIN".equals(accountResponse.getPrimaryFlag())
                    ).findAny();
                    if (matchICBC.isPresent()) {
                        com.gclife.platform.model.response.AccountResponse accountResponse = matchICBC.get();
                        accountCashWithdrawalBatchBo.setAccountId(accountResponse.getAccountId());
                        accountCashWithdrawalBatchBo.setBankCode(accountResponse.getBankCode());
                        accountCashWithdrawalBatchBo.setAccountNo(accountResponse.getAccountNo());
                        accountCashWithdrawalBatchBo.setAccountOwner(accountResponse.getAccountOwner());
                        accountCashWithdrawalBatchBo.setAccountTypeCode(accountResponse.getAccountTypeCode());
                    }
                }
                agentResponses.stream().filter(agentResponse -> accountCashWithdrawalBatchBo.getUserId().equals(agentResponse.getUserId()))
                        .findFirst().ifPresent(agentResponse -> accountCashWithdrawalBatchBo.setBranchId(agentResponse.getBranchId()));
            });

            //4.移除掉没有账户ID的数据
            cashWithdrawalBatch.removeIf(accountCashWithdrawalBatchBo -> !AssertUtils.isNotEmpty(accountCashWithdrawalBatchBo.getAccountId()));
            if (!AssertUtils.isNotEmpty(cashWithdrawalBatch)) {
                log.info("因所选代理人没有账户ID本次批量提现无可提现AccountId");
                throw new RequestException(NO_DESIGNATED_BANK_ACCOUNT);
            }
        try {
            //5.发起批量提现
            List<CashWithdrawalRecordPo> cashWithdrawalRecordPos = this.saveCashWithdrawalBatch(cashWithdrawalBatch, currentLoginUsers.getUserId(),batchWithdrawalRequest.getAmountOfMoney());

            //6.设置批量提现的业务ID
            cashWithdrawalBatch.forEach(accountCashWithdrawalBatchBo -> {
                cashWithdrawalRecordPos.stream().filter(cashWithdrawalRecordPo -> cashWithdrawalRecordPo.getUserId().equals(accountCashWithdrawalBatchBo.getUserId())
                ).findFirst().ifPresent(cashWithdrawalRecordPo -> accountCashWithdrawalBatchBo.setBizId(cashWithdrawalRecordPo.getCwrId()));
            });

            final BigDecimal[] withdrawalmount = {new BigDecimal(0)};
            final int[] withdrawalQuantity = {0};

            //7.批量保存账户支出
            List<UserAccountOutlayRequest> userAccountOutlayRequests = new ArrayList<>();
            cashWithdrawalBatch.forEach(accountCashWithdrawalBatchBo -> {
                UserAccountOutlayRequest userAccountOutlayRequest = new UserAccountOutlayRequest();
                BigDecimal amount = batchWithdrawalRequest.getAmountOfMoney();
                userAccountOutlayRequest.setUserAccountTypeCode(PartyTermEnum.USER_ACCOUNT_TYPE.CASH_ACCOUNT.name());
                if (!AssertUtils.isNotNull(amount)||amount.compareTo(BigDecimal.ZERO)==0){
                    userAccountOutlayRequest.setAmount(accountCashWithdrawalBatchBo.getCashWithdrawalAmount());
                    withdrawalmount[0] = withdrawalmount[0].add(accountCashWithdrawalBatchBo.getCashWithdrawalAmount());
                    withdrawalQuantity[0] = withdrawalQuantity[0] +1;
                }else {
                    userAccountOutlayRequest.setAmount(amount);
                }
                accountCashWithdrawalBatchBo.setCashWithdrawalAmount(userAccountOutlayRequest.getAmount());
                if (AssertUtils.isNotEmpty(accountCashWithdrawalBatchBo.getAccountId())) {
                    userAccountOutlayRequest.setBankAccountId(accountCashWithdrawalBatchBo.getAccountId());
                }
                userAccountOutlayRequest.setBusinessTypeCode(PartyTermEnum.BUSINESS_TYPE_CODE.WITHDRAW.name());
                userAccountOutlayRequest.setUserId(accountCashWithdrawalBatchBo.getUserId());
                userAccountOutlayRequest.setWithdrawalMethod("TRANSFER");
                userAccountOutlayRequest.setBizId(accountCashWithdrawalBatchBo.getBizId());
                userAccountOutlayRequests.add(userAccountOutlayRequest);
            });
            userAccountOutlayRequests.forEach(userAccountOutlayRequest -> userAccountTransfer.getUserAccountTransactionRecordResponse(userAccountOutlayRequest));

            withdrawalResult.setWithdrawalQuantity(withdrawalQuantity[0]);
            withdrawalResult.setWithdrawalmount(withdrawalmount[0]);

            //8.支付通知接口需要配置化,回调接口路径
            ResultObject<NotifyConfigResponse> notifyConfig = platformConfigApi.getNotifyConfig(PartyTermEnum.PAYMENT_BUSINESS_TYPE.AGENT_WITHDRAW.name());
            AssertUtils.isResultObjectError(log, notifyConfig);

            //9.发起付费申请
            cashWithdrawalBatch.forEach(accountCashWithdrawalBatchBo -> {
                ReceiptRequest receiptRequest = this.transApplyExtract(accountCashWithdrawalBatchBo, notifyConfig.getData());
                ResultObject<ReceiptResponse> receiptResultObject = receiptBaseApi.applyReceipt(receiptRequest);
                AssertUtils.isResultObjectError(log, receiptResultObject);
                receiptIds.add(receiptResultObject.getData().getReceiptId());
            });
        } catch (Exception e) {
            e.printStackTrace();
            if (AssertUtils.isNotEmpty(receiptIds)) {
                ResultObject deletetPaymentReceipt = receiptBaseApi.deletetPaymentReceipt(receiptIds);
                log.info("异常撤销付费记录,关闭订单{}", JSONObject.toJSONString(deletetPaymentReceipt));
            }
            throwsException(log, APP_WITHDRAW_ERROR);
        }
        resultObject.setData(withdrawalResult);
        return resultObject;
    }


    //批量提现
    private List<CashWithdrawalRecordPo> saveCashWithdrawalBatch(List<AccountCashWithdrawalBatchBo> cashWithdrawalBatch, String userId,BigDecimal amountOfMoney) {
        List<CashWithdrawalRecordPo> cashWithdrawalRecordPos = new ArrayList<>();
        cashWithdrawalBatch.forEach(cashWithdrawalBatchBo -> {
            AssertUtils.isNotEmpty(log, cashWithdrawalBatchBo.getUserId(), PARTY_USER_ID_NOT_NULL);
            AssertUtils.isNotNull(log, cashWithdrawalBatchBo.getCashWithdrawalAmount(), PARTY_USER_ACCOUNT_AMOUNT_IS_NOT_NULL);
            if (cashWithdrawalBatchBo.getCashWithdrawalAmount().compareTo(BigDecimal.ZERO) < 0) {
                throw new RequestException(PARTY_THE_AMOUNT_CANNOT_BE_NEGATIVE);
            }
            CashWithdrawalRecordPo cashWithdrawalRecordPo = new CashWithdrawalRecordPo();
            cashWithdrawalRecordPo.setUserId(cashWithdrawalBatchBo.getUserId());
            cashWithdrawalRecordPo.setCashWithdrawalDate(DateUtils.getCurrentTime());
            cashWithdrawalRecordPo.setStatus(PartyTermEnum.WITHDRAW_STATUS.FIANCE_PROCESS.name());
            if (!AssertUtils.isNotNull(amountOfMoney)||amountOfMoney.compareTo(BigDecimal.ZERO)==0){
                cashWithdrawalRecordPo.setAmount(cashWithdrawalBatchBo.getCashWithdrawalAmount());
            }else {
                cashWithdrawalRecordPo.setAmount(amountOfMoney);
            }
            cashWithdrawalRecordPos.add(cashWithdrawalRecordPo);
        });
        userAccountBaseService.saveCashWithdrawalBatch(cashWithdrawalRecordPos, userId);
        return cashWithdrawalRecordPos;
    }

    public ReceiptRequest transApplyExtract(AccountCashWithdrawalBatchBo accountCashWithdrawalBatchBo, NotifyConfigResponse NotifyConfigResponse) {
        ReceiptRequest receiptRequest = new ReceiptRequest();
        receiptRequest.setBranchId(accountCashWithdrawalBatchBo.getBranchId());
        receiptRequest.setBusinessId(accountCashWithdrawalBatchBo.getBizId());
        receiptRequest.setBusinessNo(accountCashWithdrawalBatchBo.getBizId());
        receiptRequest.setBusinessType(PartyTermEnum.PAYMENT_BUSINESS_TYPE.AGENT_WITHDRAW.name());
        receiptRequest.setCurrency(TerminologyConfigEnum.CURRENCY.USD.name());
        receiptRequest.setDeviceChannelId("gclife_agent_app");
        receiptRequest.setDuePayAmount(accountCashWithdrawalBatchBo.getCashWithdrawalAmount());
        receiptRequest.setUserId(accountCashWithdrawalBatchBo.getUserId());
        receiptRequest.setWithdrawalMethod("TRANSFER");
        receiptRequest.setPaymentHandler(NotifyConfigResponse.getNotifyUrl());
        //付款详情
        List<ReceiptItemRequest> receiptItemReqFcs = new ArrayList<>();
        ReceiptItemRequest receiptItemReqFc = new ReceiptItemRequest();
        receiptItemReqFc.setDuePayAmount(accountCashWithdrawalBatchBo.getCashWithdrawalAmount());
        receiptItemReqFc.setPaymentMethodCode(PartyTermEnum.PAYMENT_METHODS.BANK_DEDUCT.name());
        receiptItemReqFc.setPaymentTypeCode(PartyTermEnum.PAYMENT_TYPE.ACTUAL.name());

        //账号信息
        Map<String, String> map = new HashMap<>();
        map.put("bankCode", accountCashWithdrawalBatchBo.getBankCode());
        map.put("bankAccountNo", accountCashWithdrawalBatchBo.getAccountNo());
        map.put("bankAccountName", accountCashWithdrawalBatchBo.getAccountOwner());
        map.put("bankAccountType", accountCashWithdrawalBatchBo.getAccountTypeCode());
        receiptItemReqFc.setPaymentParam(JSON.toJSONString(map));
        receiptItemReqFcs.add(receiptItemReqFc);
        receiptRequest.setItems(receiptItemReqFcs);
        log.info("申请提现所需参数:{}", JSON.toJSONString(receiptRequest));
        return receiptRequest;
    }

}

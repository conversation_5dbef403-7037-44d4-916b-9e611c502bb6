package com.gclife.party.service.visit;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.party.model.request.visit.PolicyReturnVisitAddRequest;
import com.gclife.party.model.request.visit.PolicyReturnVisitChangeRequest;
import com.gclife.party.model.request.visit.PolicyReturnVisitSubmitRequest;
import com.gclife.party.model.request.visit.ReturnVisitPageRequest;
import com.gclife.party.model.response.visit.ReturnVisitAddResponse;

/**
 * @Auther: lichongfu
 * @Date: 22-10-26 14:41
 * @Description: 网销保单回访
 */
public interface OnlinePolicyReturnVisitBusinessService extends BaseBusinessService {
    /**
     * 新增网销保单回访
     *
     * @param users
     * @param returnVisitSubmitRequest
     * @return
     */
    ResultObject addOnlinePolicyReturnVisit(Users users, PolicyReturnVisitAddRequest returnVisitSubmitRequest);

    /**
     * 提交网销保单回访
     *
     * @param policyReceiptSubmitRequest
     * @return
     */
    ResultObject postOnlinePolicyReturnVisitSubmit(Users users, PolicyReturnVisitSubmitRequest policyReceiptSubmitRequest);

    ResultObject getOnlinePolicyReturnVisitDetail(String returnVisitId, String returnVisitChangeId);

    /**
     * 网销保单回访列表分页查询
     *
     * @param pageRequest
     * @return
     */
    ResultObject getOnlinePolicyReturnVisitList(ReturnVisitPageRequest pageRequest);
}
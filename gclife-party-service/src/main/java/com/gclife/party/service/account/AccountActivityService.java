package com.gclife.party.service.account;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.party.model.request.account.AccountOperateListRequest;
import com.gclife.party.model.request.account.AccountOperateRequest;
import com.gclife.party.model.request.account.AccountOperateReviewRequest;
import com.gclife.party.model.response.account.AccountOperateListResponse;
import com.gclife.party.model.response.account.AccountOperateReviewListResponse;

/**
 * <AUTHOR>
 * create 19-7-12
 * description:
 */
public interface AccountActivityService extends BaseBusinessService {

    /**
     * 账户解冻列表
     *
     * @param accountOperateListRequest 列表参数
     * @param users                     用户
     * @return AccountOperateListResponse
     */
    ResultObject<BasePageResponse<AccountOperateListResponse>> getAccountActivityList(AccountOperateListRequest accountOperateListRequest, Users users);

    /**
     * 申请账户解冻
     *
     * @param accountOperateRequest 申请参数
     * @param users                 用户
     * @return ResultObject
     */
    ResultObject postAccountActivity(AccountOperateRequest accountOperateRequest, Users users);

    /**
     * 账户解冻列表
     *
     * @param accountOperateListRequest 列表参数
     * @param users                     用户
     * @return AccountOperateReviewListResponse
     */
    ResultObject<BasePageResponse<AccountOperateReviewListResponse>> getAccountActivityReviewList(AccountOperateListRequest accountOperateListRequest, Users users);

    /**
     * 操作账户解冻审批
     *
     * @param accountOperateReviewRequest 申请参数
     * @param users                       用户
     * @return ResultObject
     */
    ResultObject postAccountActivityReview(AccountOperateReviewRequest accountOperateReviewRequest, Users users);
}

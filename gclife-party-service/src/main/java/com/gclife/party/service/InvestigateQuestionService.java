package com.gclife.party.service;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.party.model.request.InvestigateCustomerRequest;
import com.gclife.party.model.response.InvestigateCustomerResponse;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 11:16 2018/10/12
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
public interface InvestigateQuestionService {

    /**
     * 问卷调查表
     *
     * @param versionId
     * @param investigateCustomerId
     * @param currentLoginUsers
     * @return
     */
    ResultObject<InvestigateCustomerResponse> queryAppInvestigateQuestion(String versionId, String investigateCustomerId, Users currentLoginUsers);

    /**
     * 保存文件调查
     *
     * @param investigateCustomerRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject saveInvestigateQuestion(InvestigateCustomerRequest investigateCustomerRequest, Users currentLoginUsers);

    /**
     * 查询客户调问卷查详情
     * @param versionId
     * @param investigateCustomerId
     * @param currentLoginUsers
     * @return
     */
    ResultObject<InvestigateCustomerResponse> queryInvestigateQuestion(String versionId, String investigateCustomerId, Users currentLoginUsers);
}

package com.gclife.party.service;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.party.model.request.PositionSignInRequest;
import com.gclife.party.model.response.topic.PositionSignInDetailResponse;
import com.gclife.party.model.response.topic.PositionSignInResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 14:29 2019/1/16
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
public interface PositionSignInService {

    /**
     * 查询定位签到列表
     * @param positionSignInRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<BasePageResponse<PositionSignInResponse>> queryPositionSignIn(PositionSignInRequest positionSignInRequest, Users currentLoginUsers);

    /**
     * 查询定位签到列表(查询全部不分页)
     * @param positionSignInRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<List<PositionSignInResponse>> queryPositionSignInAll(PositionSignInRequest positionSignInRequest, Users currentLoginUsers);

    /**
     * 签到列表导出
     *
     * @param positionSignInRequest
     * @param currentLoginUsers
     */
    void exportPositionSignIn(HttpServletResponse response, PositionSignInRequest positionSignInRequest, Users currentLoginUsers);

    /**
     * 获取定位签到详情
     * @param topicId
     * @param currentLoginUsers
     * @return
     */
    ResultObject<PositionSignInDetailResponse> queryPositionSignInDetail(String topicId, Users currentLoginUsers);
}

package com.gclife.party.service.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentInfoResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.GB2Alpha;
import com.gclife.common.util.JackSonUtils;
import com.gclife.party.core.jooq.tables.daos.CustomerAgentDao;
import com.gclife.party.core.jooq.tables.pojos.*;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.*;
import com.gclife.party.model.response.*;
import com.gclife.party.model.response.customer.PartyCustomerResponse;
import com.gclife.party.service.CustomerClientBusinessService;
import com.gclife.party.service.base.ClientManagerBaseService;
import com.gclife.party.service.base.CustomerBaseService;
import com.gclife.platform.api.PlatformUsersApi;
import com.gclife.platform.model.request.UserInfoRequest;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyEndorseExtResponse;
import com.gclife.policy.model.response.PolicyServiceAgentResponse;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyErrorConfigEnum.PARTY_CUSTOMER_ID_IS_NOT_NULL;
import static com.gclife.party.model.config.PartyErrorConfigEnum.PARTY_USER_ID_NOT_NULL;
import static com.gclife.party.model.config.PartyTermEnum.CUSTOMER_TYPE.CLIENT;

/**
 * <AUTHOR>
 * create 2022/5/30 9:08
 * description:
 */
@Slf4j
@Service
public class CustomerClientBusinessServiceImpl extends BaseBusinessServiceImpl implements CustomerClientBusinessService {
    @Autowired
    private CustomerBaseService customerBaseService;
    @Autowired
    private ClientManagerBaseService clientManagerBaseService;
    @Autowired
    private CustomerAgentDao customerAgentDao;

    @Autowired
    private PolicyApi policyApi;

    @Autowired
    private PlatformUsersApi platformUsersApi;

    @Autowired
    private AgentApi agentApi;

    /**
     * 客户APP注册
     *
     * @param customerClientRequest 客户APP注册
     * @param appRequestHeads       请求头
     * @return Void
     */
    @Override
    @Transactional
    public ResultObject<CustomerAgentsResponse> registerClient(CustomerClientRequest customerClientRequest, AppRequestHeads appRequestHeads) {
        log.info("客户APP注册:{}", JackSonUtils.toJson(customerClientRequest));
        AssertUtils.isNotEmpty(log, customerClientRequest.getUserId(), PARTY_USER_ID_NOT_NULL);
        ResultObject<CustomerAgentsResponse> resultObject = ResultObject.success();
        CustomerAgentPo customerAgent = customerAgentDao.findById(customerClientRequest.getUserId());
        if (AssertUtils.isNotNull(customerAgent)) {
            CustomerAgentsResponse customerAgentsResponse = (CustomerAgentsResponse) this.converterObject(customerAgent, CustomerAgentsResponse.class);
            resultObject.setData(customerAgentsResponse);
            return resultObject;
        }
        CustomerAgentPo customerAgentPo = new CustomerAgentPo();
        customerAgentPo.setMobile(customerClientRequest.getMobile());
        customerAgentPo.setEmail(customerClientRequest.getEmail());
        customerAgentPo.setCustomerAgentId(customerClientRequest.getUserId());
        customerAgentPo.setCustomerNo(DateUtils.getJobNumberByTime("", Integer.toString((int) (Math.random() * 900 + 100)), DateUtils.FORMATE53, false));
        customerAgentPo.setCreatedDate(DateUtils.getCurrentTime());
        customerAgentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
        customerAgentPo.setCustomerType(CLIENT.name());
        customerAgentPo.setStatus(PartyTermEnum.CERTIFICATION_STATUS.NOT_CERTIFIED.name());
        customerAgentDao.insert(customerAgentPo);

        CustomerAgentsResponse customerAgentsResponse = (CustomerAgentsResponse) this.converterObject(customerAgentPo, CustomerAgentsResponse.class);
        resultObject.setData(customerAgentsResponse);
        return resultObject;
    }

    /**
     * 客户APP注册回滚
     *
     * @param userId          用户ID
     * @param appRequestHeads 请求头
     * @return Void
     */
    @Override
    @Transactional
    public ResultObject<Void> registerClientRollback(String userId, AppRequestHeads appRequestHeads) {
        AssertUtils.isNotEmpty(log, userId, PARTY_USER_ID_NOT_NULL);
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(userId);
        if (AssertUtils.isNotNull(customerAgentPo)) {
            customerAgentDao.delete(customerAgentPo);
        }
        return ResultObject.success();
    }

    @Override
    public ResultObject<ClientAgentResponse> getClientAgentInfo(String userId, AppRequestHeads appRequestHeads) {
        ResultObject<ClientAgentResponse> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(userId)) {
            return resultObject;
        }

        //查询客户信息
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(userId);
        ClientAgentResponse clientAgentResponse = (ClientAgentResponse) this.converterObject(customerAgentPo, ClientAgentResponse.class);
        resultObject.setData(clientAgentResponse);
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject saveClientAgentInfo(CustomerPersonalCenterRequest customerPersonalCenterRequest, Users users) {
        ResultObject resultObject = new ResultObject<>();
        try {
            String userId = customerPersonalCenterRequest.getUserId();
            if (!AssertUtils.isNotEmpty(userId)) {
                userId = users.getUserId();
            }
            AssertUtils.isNotEmpty(log, userId, PARTY_USER_ID_NOT_NULL);
            CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(userId);

            AssertUtils.isNotNull(this.getLogger(), customerAgentPo, PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);

            UserInfoRequest userInfoRequest = new UserInfoRequest();
            List<CustomerAgentChangeRecordPo> customerAgentChangeRecordPos = new ArrayList<>();

            //设置昵称
            if (AssertUtils.isNotNull(customerPersonalCenterRequest.getNickName())) {
                if (!customerPersonalCenterRequest.getNickName().equals(customerAgentPo.getNickName())) {
                    CustomerAgentChangeRecordPo customerAgentChangeRecordPo = new CustomerAgentChangeRecordPo();
                    customerAgentChangeRecordPo.setBeforeChange(customerAgentPo.getNickName());
                    customerAgentChangeRecordPo.setAfterChange(customerPersonalCenterRequest.getNickName());
                    customerAgentChangeRecordPo.setChangeDetailType(PartyTermEnum.CHANGE_DETAIL_TYPE.NICK_NAME.name());
                    customerAgentChangeRecordPos.add(customerAgentChangeRecordPo);
                }

                customerAgentPo.setNickName(customerPersonalCenterRequest.getNickName());
                userInfoRequest.setNikeName(customerPersonalCenterRequest.getNickName());
            }
            //设置身高
            if (AssertUtils.isNotNull(customerPersonalCenterRequest.getStature())) {
                customerAgentPo.setStature(customerPersonalCenterRequest.getStature());
            }
            //设置体重
            if (AssertUtils.isNotNull(customerPersonalCenterRequest.getAvoirdupois())) {
                customerAgentPo.setAvoirdupois(customerPersonalCenterRequest.getAvoirdupois());
            }
            //设置bmi
            if (AssertUtils.isNotNull(customerPersonalCenterRequest.getBmi())) {
                customerAgentPo.setBmi(customerPersonalCenterRequest.getBmi());
            }
            //设置手机号
            if (AssertUtils.isNotNull(customerPersonalCenterRequest.getMobile())) {
                if (!customerPersonalCenterRequest.getMobile().equals(customerAgentPo.getMobile())) {
                    CustomerAgentChangeRecordPo customerAgentChangeRecordPo = new CustomerAgentChangeRecordPo();
                    customerAgentChangeRecordPo.setBeforeChange(customerAgentPo.getMobile());
                    customerAgentChangeRecordPo.setAfterChange(customerPersonalCenterRequest.getMobile());
                    customerAgentChangeRecordPo.setChangeDetailType(PartyTermEnum.CHANGE_DETAIL_TYPE.MOBILE.name());
                    customerAgentChangeRecordPos.add(customerAgentChangeRecordPo);
                }
                customerAgentPo.setMobile(customerPersonalCenterRequest.getMobile());
            }
            //设置email
            if (AssertUtils.isNotNull(customerPersonalCenterRequest.getEmail())) {
                if (!customerPersonalCenterRequest.getEmail().equals(customerAgentPo.getEmail())) {
                    CustomerAgentChangeRecordPo customerAgentChangeRecordPo = new CustomerAgentChangeRecordPo();
                    customerAgentChangeRecordPo.setBeforeChange(customerAgentPo.getEmail());
                    customerAgentChangeRecordPo.setAfterChange(customerPersonalCenterRequest.getEmail());
                    customerAgentChangeRecordPo.setChangeDetailType(PartyTermEnum.CHANGE_DETAIL_TYPE.EMAIL.name());
                    customerAgentChangeRecordPos.add(customerAgentChangeRecordPo);
                }

                customerAgentPo.setEmail(customerPersonalCenterRequest.getEmail());
                //同时修改登录用户的邮箱
                userInfoRequest.setEmail(customerPersonalCenterRequest.getEmail());
            }
            //设置头像
            if (AssertUtils.isNotNull(customerPersonalCenterRequest.getAvatar())) {
                if (!customerPersonalCenterRequest.getAvatar().equals(customerAgentPo.getAvatar())) {
                    CustomerAgentChangeRecordPo customerAgentChangeRecordPo = new CustomerAgentChangeRecordPo();
                    customerAgentChangeRecordPo.setBeforeChange(customerAgentPo.getAvatar());
                    customerAgentChangeRecordPo.setAfterChange(customerPersonalCenterRequest.getAvatar());
                    customerAgentChangeRecordPo.setChangeDetailType(PartyTermEnum.CHANGE_DETAIL_TYPE.AVATAR.name());
                    customerAgentChangeRecordPos.add(customerAgentChangeRecordPo);
                }

                customerAgentPo.setAvatar(customerPersonalCenterRequest.getAvatar());
            }

            ResultObject resultObject1 = platformUsersApi.userInfoPut(userInfoRequest);
            this.getLogger().info("修改用户信息结果：" + resultObject1);

            //保存客户信息
            customerBaseService.saveCustomerAgent(customerAgentPo);
            //保存变更信息
            if (AssertUtils.isNotEmpty(customerAgentChangeRecordPos)) {
                customerAgentChangeRecordPos.forEach(customerAgentChangeRecordPo -> {
                    customerAgentChangeRecordPo.setCustomerAgentId(customerAgentPo.getCustomerAgentId());
                    customerAgentChangeRecordPo.setOptionUserId(customerAgentPo.getCustomerAgentId());
                    customerAgentChangeRecordPo.setEffectiveDate(DateUtils.getCurrentTime());
                    customerAgentChangeRecordPo.setCustomerChangeType(PartyTermEnum.CUSTOMER_CHANGE_TYPE.PERSONAL_INFO.name());
                });
                log.info("保存变更信息:{}", JackSonUtils.toJson(customerAgentChangeRecordPos));
                clientManagerBaseService.saveCustomerAgentChangeRecord(customerAgentPo.getCustomerAgentId(), customerAgentChangeRecordPos);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PartyErrorConfigEnum.PARTY_SAVE_CUSTOMER_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject saveClientInfo(CustomerIdentityRequest customerIdentityRequest, AppRequestHeads appRequestHeads, Users users) {
        ResultObject resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getFamilyName(), PartyErrorConfigEnum.PARTY_CUSTOMER_FAMILY_NAME_IS_NOT_NULL);
            AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getGivenName(), PartyErrorConfigEnum.PARTY_CUSTOMER_GIVEN_NAME_IS_NOT_NULL);
            AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getSex(), PartyErrorConfigEnum.PARTY_PARAMETER_SEX_IS_NOT_NULL);
            AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getBirthdayFormat(), PartyErrorConfigEnum.PARTY_PARAMETER_BIRTHDAY_IS_NOT_NULL);
            AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getIdType(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_ID_TYPE_IS_NOT_NULL);
            AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getIdNo(), PartyErrorConfigEnum.PARTY_PARAMETER_ID_NO_IS_NOT_NULL);
            AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getIsCoverFlag(), PartyErrorConfigEnum.PARTY_IS_COVER_FLAG_IS_NOT_NULL);
            if ("ID".equals(customerIdentityRequest.getIdType())) {
                AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getIdExpDateFormat(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_EXP_DATE_IS_NOT_NULL);
                AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getIdAttachId(), PartyErrorConfigEnum.PARTY_TOPIC_SIGNIN_ATTACHMENT_IS_NOT_NULL);
                customerIdentityRequest.setIdExpDate(DateUtils.stringToTime(customerIdentityRequest.getIdExpDateFormat(), DateUtils.FORMATE18));
            } else {
                AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getIdExpDateFormat(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_EXP_DATE_IS_NOT_NULL);
                //AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getVisaExpDateFormat(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_EXP_DATE_IS_NOT_NULL);
                AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getVisaAttachId(), PartyErrorConfigEnum.PARTY_TOPIC_SIGNIN_ATTACHMENT_IS_NOT_NULL);
                if (AssertUtils.isNotNull(customerIdentityRequest.getVisaExpDateFormat())) {
                    customerIdentityRequest.setVisaExpDate(DateUtils.stringToTime(customerIdentityRequest.getVisaExpDateFormat(), DateUtils.FORMATE18));
                }
                customerIdentityRequest.setIdExpDate(DateUtils.stringToTime(customerIdentityRequest.getIdExpDateFormat(), DateUtils.FORMATE18));
            }
            customerIdentityRequest.setBirthday(DateUtils.stringToTime(customerIdentityRequest.getBirthdayFormat(), DateUtils.FORMATE18));
            customerIdentityRequest.setName(customerIdentityRequest.getFamilyName() + " " + customerIdentityRequest.getGivenName());

            //验证用户是否重复绑定
            CustomerAgentPo customerAgentPoBind = customerBaseService.queryOneCustomerAgentByFourElements(customerIdentityRequest.getName(),
                    customerIdentityRequest.getIdNo(), customerIdentityRequest.getBirthday(), customerIdentityRequest.getSex(), CLIENT.name());

            if (AssertUtils.isNotNull(customerAgentPoBind)) {
                throwsException(log, PartyErrorConfigEnum.PARTY_CUSTOMER_IS_EXIT);
            }

            //查询customerAgent信息
            String userId = users.getUserId();
            AssertUtils.isNotEmpty(log, userId, PARTY_USER_ID_NOT_NULL);
            CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(userId);

            AssertUtils.isNotNull(this.getLogger(), customerAgentPo, PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);

            //手机号不一致覆盖处理
            if ("YES".equals(customerIdentityRequest.getIsCoverFlag())) {
                //根据四要素查出客户信息
                CustomerPo customerPo1 = customerBaseService.queryOneCustomerByFourElements(customerIdentityRequest.getName(),
                        customerIdentityRequest.getIdNo(), customerIdentityRequest.getBirthday(), customerIdentityRequest.getSex());

                AssertUtils.isNotNull(this.getLogger(), customerPo1, PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);

                String customerId = customerPo1.getCustomerId();
                String customerNo = customerPo1.getCustomerNo();

                List<CustomerAgentPo> customerAgentPos = customerBaseService.queryCustomerAgentByCustomerId(customerId);

                AssertUtils.isNotEmpty(this.getLogger(), customerAgentPos, PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);

                List<String> customerAgentIds = customerAgentPos.stream().map(CustomerAgentPo::getCustomerAgentId).distinct().collect(Collectors.toList());

                ResultObject<PolicyServiceAgentResponse> serviceAgentPoResultObject = policyApi.listClientPolicyRelation(customerAgentIds);

                AssertUtils.isResultObjectDataNull(this.getLogger(), serviceAgentPoResultObject, PartyErrorConfigEnum.PARTY_PARAMETER_USER_ID_IS_NOT_NULL);

                PolicyServiceAgentResponse policyServiceAgentResponse = serviceAgentPoResultObject.getData();

                String serviceAgentId = policyServiceAgentResponse.getServiceAgentId();

                customerAgentPo.setUserId(serviceAgentId);

                customerAgentPo.setCustomerNo(customerNo);

                //拼装customer数据,与旧的customer数据进行合并
                customerPo1.setFamilyName(customerIdentityRequest.getFamilyName());
                customerPo1.setGivenName(customerIdentityRequest.getGivenName());
                customerPo1.setName(customerIdentityRequest.getName());
                customerPo1.setSex(customerIdentityRequest.getSex());
                customerPo1.setBirthday(customerIdentityRequest.getBirthday());
                customerPo1.setIdType(customerIdentityRequest.getIdType());
                customerPo1.setIdNo(customerIdentityRequest.getIdNo());
                customerPo1.setMobile(customerIdentityRequest.getMobile());
                if ("ID".equals(customerIdentityRequest.getIdType())) {
                    customerPo1.setIdExpDate(customerIdentityRequest.getIdExpDate());
                    customerPo1.setIdAttachId(customerIdentityRequest.getIdAttachId());
                } else {
                    customerPo1.setIdExpDate(customerIdentityRequest.getIdExpDate());
                    customerPo1.setVisaExpDate(customerIdentityRequest.getVisaExpDate());
                    customerPo1.setVisaAttachId(customerIdentityRequest.getVisaAttachId());
                }

                //保存customer客户信息
                customerBaseService.saveCustomerPo(customerPo1);

                customerAgentPos.forEach(customerAgentPo1 -> {
                    customerAgentPo1.setMobile(customerIdentityRequest.getMobile());

                    //保存旧数据customerAgent客户信息
                    customerBaseService.saveCustomerAgent(customerAgentPo1);
                });

                com.gclife.policy.model.request.ClientMobileUpdateRequest clientMobileUpdateRequest = new com.gclife.policy.model.request.ClientMobileUpdateRequest();
                clientMobileUpdateRequest.setMobile(customerIdentityRequest.getMobile());
                clientMobileUpdateRequest.setCustomerIds(customerAgentIds);

                //查找关联的保单列表，修改手机号
                ResultObject resultObject1 = policyApi.updateClientMobile(clientMobileUpdateRequest);
                AssertUtils.isResultObjectError(this.getLogger(), resultObject1);
            } else {
                //根据四要素查出客户信息
                CustomerPo customerPo = customerBaseService.queryOneCustomerByFourElements(customerIdentityRequest.getName(),
                        customerIdentityRequest.getIdNo(), customerIdentityRequest.getBirthday(), customerIdentityRequest.getSex());

                if (AssertUtils.isNotNull(customerPo)) {
                    String customerId = customerPo.getCustomerId();
                    String customerNo = customerPo.getCustomerNo();

                    List<CustomerAgentPo> customerAgentPos = customerBaseService.queryCustomerAgentByCustomerId(customerId);

                    AssertUtils.isNotEmpty(this.getLogger(), customerAgentPos, PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);

                    List<String> customerAgentIds = customerAgentPos.stream().map(CustomerAgentPo::getCustomerAgentId).distinct().collect(Collectors.toList());

                    this.getLogger().info("用户客户id：=============" + JackSonUtils.toJson(customerAgentIds));
                    ResultObject<PolicyServiceAgentResponse> serviceAgentPoResultObject = policyApi.listClientPolicyRelation(customerAgentIds);

                    AssertUtils.isResultObjectDataNull(this.getLogger(), serviceAgentPoResultObject, PartyErrorConfigEnum.PARTY_PARAMETER_USER_ID_IS_NOT_NULL);

                    PolicyServiceAgentResponse policyServiceAgentResponse = serviceAgentPoResultObject.getData();

                    String serviceAgentId = policyServiceAgentResponse.getServiceAgentId();

                    customerAgentPo.setUserId(serviceAgentId);
                    customerAgentPo.setCustomerNo(customerNo);
                    //找到客户后，设置已有的客户勋章，方便客户管理
                    List<CustomerAgentMedalPo> medalPos = customerBaseService.listCustomerAgentMedal(customerAgentIds);
                    if (AssertUtils.isNotEmpty(medalPos)) {
                        List<String> medalIds = medalPos.stream().map(CustomerAgentMedalPo::getMedalId).distinct().collect(Collectors.toList());
                        medalIds.forEach(s -> {
                            CustomerAgentMedalPo customerAgentMedalPo = new CustomerAgentMedalPo();
                            customerAgentMedalPo.setCustomerId(customerId);
                            customerAgentMedalPo.setMedalId(s);
                            customerAgentMedalPo.setCreatedUserId(userId);
                            customerBaseService.saveCustomerAgentMedal(customerAgentMedalPo);
                        });
                    }
                } else {
                    customerAgentPo.setMobile(customerIdentityRequest.getMobile());
                }
            }

            //拼装customerAgent数据
            //如果新用户注册，默认随机分配一个业务员
            if (!AssertUtils.isNotEmpty(customerAgentPo.getUserId())) {
                //查询所有业务员，默认随机分配一个业务员
                ResultObject<AgentInfoResponse> randomServiceAgent = agentApi.getRandomServiceAgent();

                AssertUtils.isResultObjectDataNull(this.getLogger(), randomServiceAgent);

                AgentInfoResponse agentInfoResponse = randomServiceAgent.getData();

                customerAgentPo.setUserId(agentInfoResponse.getUserId());
            }
            customerAgentPo.setFamilyName(customerIdentityRequest.getFamilyName());
            customerAgentPo.setGivenName(customerIdentityRequest.getGivenName());
            customerAgentPo.setName(customerIdentityRequest.getName());
            customerAgentPo.setSex(customerIdentityRequest.getSex());
            customerAgentPo.setBirthday(customerIdentityRequest.getBirthday());
            customerAgentPo.setIdType(customerIdentityRequest.getIdType());
            customerAgentPo.setIdNo(customerIdentityRequest.getIdNo());
            customerAgentPo.setStatus("CERTIFIED");
            String groupNo = GB2Alpha.String2AlphaFirst(customerAgentPo.getName());
            customerAgentPo.setGroupCode(groupNo);
            if ("ID".equals(customerIdentityRequest.getIdType())) {
                customerAgentPo.setIdExpDate(customerIdentityRequest.getIdExpDate());
                customerAgentPo.setIdAttachId(customerIdentityRequest.getIdAttachId());
            } else {
                customerAgentPo.setIdExpDate(customerIdentityRequest.getIdExpDate());
                customerAgentPo.setVisaExpDate(customerIdentityRequest.getVisaExpDate());
                customerAgentPo.setVisaAttachId(customerIdentityRequest.getVisaAttachId());
            }

            //二次验证处理
            if ("YES".equals(customerIdentityRequest.getSameWithCustomerFlag())) {
                AssertUtils.isNotEmpty(getLogger(), customerIdentityRequest.getVerifyCustomerId(), PARTY_CUSTOMER_ID_IS_NOT_NULL);
                CustomerPo verifyCustomerPo = customerBaseService.queryOneCustomer(customerIdentityRequest.getVerifyCustomerId(), null);
                boolean notSameFlag = false;
                if (!verifyCustomerPo.getName().replace(" ", "").equalsIgnoreCase(customerIdentityRequest.getName().replace(" ", ""))) {
                    notSameFlag = true;
                }
                if (!verifyCustomerPo.getSex().equals(customerIdentityRequest.getSex())) {
                    notSameFlag = true;
                }
                if (verifyCustomerPo.getBirthday().compareTo(customerIdentityRequest.getBirthday()) != 0) {
                    notSameFlag = true;
                }
                if (AssertUtils.isNotEmpty(customerIdentityRequest.getVerifyIdNo())) {
                    //证件类型一致,号码不一致，说明客户填错了证件号码，需要报错
                    if (verifyCustomerPo.getIdType().equals(customerIdentityRequest.getVerifyIdType()) && !verifyCustomerPo.getIdNo().equals(customerIdentityRequest.getVerifyIdNo())) {
                        throwsException(log, PartyErrorConfigEnum.PARTY_ID_NO_IS_DIFFERENT);
                    }

                    //证件类型和号码都不一致，不是同一个人
                    if (!verifyCustomerPo.getIdType().equals(customerIdentityRequest.getVerifyIdType()) && !verifyCustomerPo.getIdNo().equals(customerIdentityRequest.getVerifyIdNo())) {
                        notSameFlag = true;
                    }
                }
                if (!notSameFlag) {
                    customerAgentPo.setCustomerNo(verifyCustomerPo.getCustomerNo());
                }
            }

            //保存customerAgent客户信息
            customerBaseService.saveCustomerAgent(customerAgentPo);

            //根据四要素查出客户信息(不需要再去保存新注册用户成为一个真正的用户）
            /*CustomerPo customerPo = customerBaseService.queryOneCustomerByFourElements(customerIdentityRequest.getName(),
                    customerIdentityRequest.getIdType(), customerIdentityRequest.getIdNo(), customerIdentityRequest.getBirthday(), customerIdentityRequest.getSex());

            if (!AssertUtils.isNotNull(customerPo)) {
                //数据转换
                customerPo = (CustomerPo) this.converterObject(customerAgentPo, CustomerPo.class);
            }

            //拼装customer数据,与旧的customer数据进行合并
            customerPo.setFamilyName(customerIdentityRequest.getFamilyName());
            customerPo.setGivenName(customerIdentityRequest.getGivenName());
            customerPo.setName(customerIdentityRequest.getName());
            customerPo.setSex(customerIdentityRequest.getSex());
            customerPo.setBirthday(customerIdentityRequest.getBirthday());
            customerPo.setIdType(customerIdentityRequest.getIdType());
            customerPo.setIdNo(customerIdentityRequest.getIdNo());
            if ("ID".equals(customerIdentityRequest.getIdType())) {
                customerPo.setIdExpDate(customerIdentityRequest.getIdExpDate());
                customerPo.setIdAttachId(customerIdentityRequest.getIdAttachId());
            } else {
                customerPo.setIdExpDate(customerIdentityRequest.getIdExpDate());
                customerPo.setVisaExpDate(customerIdentityRequest.getVisaExpDate());
                customerPo.setVisaAttachId(customerIdentityRequest.getVisaAttachId());
            }

            //保存customer客户信息
            customerBaseService.saveCustomerPo(customerPo);*/

            UserInfoRequest userInfoRequest = new UserInfoRequest();
            userInfoRequest.setName(customerAgentPo.getName());
            userInfoRequest.setGender(customerAgentPo.getSex());
            ResultObject resultObject1 = platformUsersApi.userInfoPut(userInfoRequest);
            this.getLogger().info("修改用户信息结果：" + resultObject1);

            //关联身份时需要将关联到的电子保单pdf转图片
            this.asyncPolicyAllBookToPicture(userId);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PartyErrorConfigEnum.PARTY_SAVE_CUSTOMER_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    public void asyncPolicyAllBookToPicture(String userId) {
        List<CustomerAgentPo> customerAgentPos = customerBaseService.getCustomerRelationByCustomerAgentId(userId);
        this.getLogger().info("查询到的客户数据customerAgentPos：===================" + JSON.toJSONString(customerAgentPos));

        if (AssertUtils.isNotEmpty(customerAgentPos)) {
            List<String> customerAgentIds = customerAgentPos.stream().filter(customerAgentPo -> "POLICY".equals(customerAgentPo.getCustomerType())).map(CustomerAgentPo::getCustomerAgentId).collect(Collectors.toList());
            this.getLogger().info("查询过滤后的customerAgentIds：===================" + customerAgentIds);

            if (AssertUtils.isNotEmpty(customerAgentIds)) {
                this.getLogger().info("：图片转化开始===================");
                policyApi.policyPdfTransform(customerAgentIds);
                this.getLogger().info("：图片转化完成===================");
            }

        }
    }

    @Override
    public ResultObject<VerifyRelationResponse> verifyClientRelation(CustomerIdentityRequest customerIdentityRequest, AppRequestHeads appRequestHeads, Users users) {
        ResultObject<VerifyRelationResponse> resultObject = new ResultObject<>();
        VerifyRelationResponse verifyRelationResponse = new VerifyRelationResponse();
        //参数验证
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getFamilyName(), PartyErrorConfigEnum.PARTY_CUSTOMER_FAMILY_NAME_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getGivenName(), PartyErrorConfigEnum.PARTY_CUSTOMER_GIVEN_NAME_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getSex(), PartyErrorConfigEnum.PARTY_PARAMETER_SEX_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getBirthdayFormat(), PartyErrorConfigEnum.PARTY_PARAMETER_BIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getIdType(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getIdNo(), PartyErrorConfigEnum.PARTY_PARAMETER_ID_NO_IS_NOT_NULL);

        customerIdentityRequest.setName(customerIdentityRequest.getFamilyName() + " " + customerIdentityRequest.getGivenName());
        customerIdentityRequest.setBirthday(DateUtils.stringToTime(customerIdentityRequest.getBirthdayFormat(), DateUtils.FORMATE18));

        //验证用户是否重复绑定
        CustomerAgentPo customerAgentPo1 = customerBaseService.queryOneCustomerAgentByFourElements(customerIdentityRequest.getName(),
                customerIdentityRequest.getIdNo(), customerIdentityRequest.getBirthday(), customerIdentityRequest.getSex(), CLIENT.name());

        if (AssertUtils.isNotNull(customerAgentPo1)) {
            throwsException(log, PartyErrorConfigEnum.PARTY_CUSTOMER_IS_EXIT);
        }

        //查询customerAgent信息
        String userId = users.getUserId();
        AssertUtils.isNotEmpty(log, userId, PARTY_USER_ID_NOT_NULL);
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(userId);

        AssertUtils.isNotNull(this.getLogger(), customerAgentPo, PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);

        //根据四要素查出客户信息
        CustomerPo customerPo = customerBaseService.queryOneCustomerByFourElements(customerIdentityRequest.getName(),
                customerIdentityRequest.getIdNo(), customerIdentityRequest.getBirthday(), customerIdentityRequest.getSex());

        //AssertUtils.isNotNull(this.getLogger(), customerPo, PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);
        if (AssertUtils.isNotNull(customerPo)) {
            String mobile = customerPo.getMobile();
            String identityRequestMobile = customerIdentityRequest.getMobile();// 当前登录的手机号

            //校验用户登录手机号是否和投保时的手机号是否一致
            if (AssertUtils.isNotEmpty(mobile)) {
                if (!AssertUtils.isNotEmpty(identityRequestMobile) || mobile.equals(identityRequestMobile)) {
                    verifyRelationResponse.setIsShowFlag("NO");
                    verifyRelationResponse.setIsSameFlag("YES");
                } else {
                    verifyRelationResponse.setIsSameFlag("NO");
                    verifyRelationResponse.setIsShowFlag("YES");
                }
            }
        }

        //根据四要素查询疑似客户
        List<CustomerPo> customerPos = customerBaseService.querySuspectedCustomerByFourElements(customerIdentityRequest.getName(), customerIdentityRequest.getIdNo(), customerIdentityRequest.getBirthday(), customerIdentityRequest.getSex());
        //若有存在客户就不用再找疑似客户了
        if (AssertUtils.isNotEmpty(customerPos) && !AssertUtils.isNotNull(customerPo)) {
            CustomerPo suspectedCustomer = customerPos.get(0);
            verifyRelationResponse.setReVerifyFlag(TerminologyConfigEnum.WHETHER.YES.name());
            verifyRelationResponse.setVerifyCustomerId(suspectedCustomer.getCustomerId());
            if (!suspectedCustomer.getName().replace(" ", "").equalsIgnoreCase(customerIdentityRequest.getName().replace(" ", ""))) {
                verifyRelationResponse.setNameFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }
            if (!suspectedCustomer.getSex().equals(customerIdentityRequest.getSex())) {
                verifyRelationResponse.setSexFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }
            if (suspectedCustomer.getBirthday().compareTo(customerIdentityRequest.getBirthday()) != 0) {
                verifyRelationResponse.setBirthdayFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }
            if (!suspectedCustomer.getIdNo().equals(customerIdentityRequest.getIdNo())) {
                verifyRelationResponse.setIdFlag(TerminologyConfigEnum.WHETHER.YES.name());

                StringBuilder a = new StringBuilder(suspectedCustomer.getIdNo());
                String verifyIdNo;
                if (a.length() > 4) {
                    verifyIdNo = a.replace(0, a.length() - 4, "****").toString();
                } else {
                    verifyIdNo = a.replace(0, 1, "****").toString();
                }
                verifyRelationResponse.setVerifyIdNo(verifyIdNo);
                verifyRelationResponse.setVerifyIdType(suspectedCustomer.getIdType());
            }
        }
        //二次验证处理
        if ("YES".equals(customerIdentityRequest.getSameWithCustomerFlag())) {
            AssertUtils.isNotEmpty(getLogger(), customerIdentityRequest.getVerifyCustomerId(), PARTY_CUSTOMER_ID_IS_NOT_NULL);
            CustomerPo verifyCustomerPo = customerBaseService.queryOneCustomer(customerIdentityRequest.getVerifyCustomerId(), null);
            if (AssertUtils.isNotEmpty(customerIdentityRequest.getVerifyIdNo())) {
                //证件类型一致,号码不一致，说明客户填错了证件号码，需要报错
                if (verifyCustomerPo.getIdType().equals(customerIdentityRequest.getVerifyIdType()) && !verifyCustomerPo.getIdNo().equals(customerIdentityRequest.getVerifyIdNo())) {
                    throwsException(log, PartyErrorConfigEnum.PARTY_ID_NO_IS_DIFFERENT);
                }
            }
        }
        resultObject.setData(verifyRelationResponse);

        return resultObject;
    }

    @Override
    public ResultObject<VerifyRelationResponse> verifyClientAppOpen(CustomerIdentityRequest customerIdentityRequest, AppRequestHeads appRequestHeads, Users users) {
        ResultObject<VerifyRelationResponse> resultObject = new ResultObject<>();
        VerifyRelationResponse verifyRelationResponse = new VerifyRelationResponse();
        List<String> policyStatus = new ArrayList<>();
        policyStatus.add("POLICY_STATUS_INVALID");
        policyStatus.add("POLICY_STATUS_INVALID_THOROUGH");
        policyStatus.add("POLICY_STATUS_SURRENDER");
        policyStatus.add("POLICY_STATUS_HESITATION_REVOKE");
        //参数验证
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getFamilyName(), PartyErrorConfigEnum.PARTY_CUSTOMER_FAMILY_NAME_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getGivenName(), PartyErrorConfigEnum.PARTY_CUSTOMER_GIVEN_NAME_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getSex(), PartyErrorConfigEnum.PARTY_PARAMETER_SEX_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getBirthdayFormat(), PartyErrorConfigEnum.PARTY_PARAMETER_BIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getIdType(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), customerIdentityRequest.getIdNo(), PartyErrorConfigEnum.PARTY_PARAMETER_ID_NO_IS_NOT_NULL);
        //AssertUtils.isNotEmpty(getLogger(), customerIdentityRequest.getVerifyCustomerId(), PARTY_CUSTOMER_ID_IS_NOT_NULL);

        customerIdentityRequest.setName(customerIdentityRequest.getFamilyName() + " " + customerIdentityRequest.getGivenName());
        customerIdentityRequest.setBirthday(DateUtils.stringToTime(customerIdentityRequest.getBirthdayFormat(), DateUtils.FORMATE18));

        //1.首先验证是否是本人重复投保
        //根据customerId去查询所有的customerAgent信息
        if (AssertUtils.isNotEmpty(customerIdentityRequest.getVerifyCustomerId())) {
            ResultObject<List<PolicyEndorseExtResponse>> listResultObject = policyApi.listPolicyCustomerRelation(customerIdentityRequest.getVerifyCustomerId());
            this.getLogger().info("客户id关联的所有保单：=============" + JSON.toJSONString(listResultObject));
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                List<PolicyEndorseExtResponse> endorseExtResponses = listResultObject.getData();
                //已经买过20A产品，不允许再买
                Optional<PolicyEndorseExtResponse> first = endorseExtResponses.stream()
                        .filter(policyEndorseExtResponse -> "PRO880000000000020A".equals(policyEndorseExtResponse.getPolicyCoverage()
                                .getProductId())).findFirst();
                if (first.isPresent()) {
                    PolicyEndorseExtResponse policyEndorseExtResponse = first.get();

                    if (!policyStatus.contains(policyEndorseExtResponse.getPolicyStatus())) {
                        verifyRelationResponse.setIsBuyFlag(TerminologyConfigEnum.WHETHER.YES.name());
                    }
                }


            }
        }


        //根据四要素查询疑似客户
        if (TerminologyConfigEnum.WHETHER.NO.name().equals(verifyRelationResponse.getIsBuyFlag())) {
            List<CustomerPo> customerPos = customerBaseService.querySuspectedCustomerByFourElements(
                    customerIdentityRequest.getName(), customerIdentityRequest.getIdNo(), customerIdentityRequest.getBirthday(), customerIdentityRequest.getSex());

            this.getLogger().info("查询到的疑似客户数据：=====================" + JSON.toJSONString(customerPos));

            if (AssertUtils.isNotEmpty(customerPos)) {
                Optional<CustomerPo> first = customerPos.stream().filter(customerPo -> customerIdentityRequest.getVerifyCustomerId().equals(customerPo.getCustomerId())).findFirst();

                //2.如果是本人，判断是否可以买
                if (first.isPresent()) {
                    verifyRelationResponse.setIsOneselfFlag(TerminologyConfigEnum.WHETHER.YES.name());
                    verifyRelationResponse.setIsBuyFlag(TerminologyConfigEnum.WHETHER.NO.name());
                    customerPos.remove(first.get());
                }

                //如果不是本人，判断疑似客户
                if (TerminologyConfigEnum.WHETHER.NO.name().equals(verifyRelationResponse.getIsOneselfFlag())) {
                    if (AssertUtils.isNotEmpty(customerPos)) {
                        verifyRelationResponse.setIsSusCusFlag(TerminologyConfigEnum.WHETHER.YES.name());
                        verifyRelationResponse.setIsBuyFlag(TerminologyConfigEnum.WHETHER.YES.name());
                    }
                }
            }
        }

        resultObject.setData(verifyRelationResponse);
        return resultObject;
    }

    @Transactional
    @Override
    public ResultObject agentInviteCustomerSave(AgentInviteCustomerRequest agentInviteCustomerRequest, Users users) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), agentInviteCustomerRequest.getAgentId(), PartyErrorConfigEnum.PARTY_USER_ID_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), agentInviteCustomerRequest.getUserId(), PartyErrorConfigEnum.PARTY_USER_ID_NOT_NULL);

            AgentInviteCustomerRecordPo agentInviteCustomerRecordPo1 = customerBaseService.queryOneAgentInviteCustomerRecordPo(agentInviteCustomerRequest.getAgentId(), agentInviteCustomerRequest.getUserId());

            if (!AssertUtils.isNotNull(agentInviteCustomerRecordPo1)) {
                AgentInviteCustomerRecordPo agentInviteCustomerRecordPo = new AgentInviteCustomerRecordPo();
                agentInviteCustomerRecordPo.setUserId(agentInviteCustomerRequest.getUserId());
                agentInviteCustomerRecordPo.setAgentId(agentInviteCustomerRequest.getAgentId());
                customerBaseService.saveAgentInviteCustomerRecordPo(agentInviteCustomerRecordPo);
            }

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PartyErrorConfigEnum.PARTY_AGENT_INVITE_CUSTOMER_SAVE_FORMAT_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<InviteCustomerResponse> agentInviteCustomerQuery(AgentInviteCustomerQueryRequest agentInviteCustomerQueryRequest, Users users) {
        ResultObject<InviteCustomerResponse> resultObject = new ResultObject<>();
        InviteCustomerResponse inviteCustomerResponse = new InviteCustomerResponse();
        long startTime;
        long endTime;

        String agentId = agentInviteCustomerQueryRequest.getAgentId();
        String type = agentInviteCustomerQueryRequest.getType();

        if (!AssertUtils.isNotEmpty(agentId)) {
            return resultObject;
        }

        String startDate = agentInviteCustomerQueryRequest.getStartDate();
        String endDate = agentInviteCustomerQueryRequest.getEndDate();

        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(startDate)) {
            startTime = DateUtils.stringToTime(startDate, DateUtils.FORMATE18);
        } else {
            startTime = DateUtils.getThisMonthFirstDay(currentTime);
        }
        if (AssertUtils.isNotEmpty(endDate)) {
            endTime = DateUtils.timeToTimeTop(DateUtils.stringToTime(endDate, DateUtils.FORMATE18));
        } else {
            endTime = DateUtils.getThisMonthLastDay(currentTime);
        }

        //查询当月业务员邀请客户关系表
        List<AgentInviteCustomerRecordPo> agentInviteCustomerRecordPosThisMonth = customerBaseService.queryAgentInviteCustomerRecord(agentId, startTime, endTime);

        //查询业务员邀请客户关系表
        List<AgentInviteCustomerRecordPo> agentInviteCustomerRecordPos = customerBaseService.queryAgentInviteCustomerRecord(agentId, null, null);

        if ("AGENT".equals(type)) {
            this.queryListAgentApp(agentInviteCustomerRecordPosThisMonth, agentInviteCustomerRecordPos, inviteCustomerResponse);
        } else {
            this.queryListBmp(agentInviteCustomerRecordPosThisMonth, agentInviteCustomerRecordPos, inviteCustomerResponse);
        }

        List<AgentInviteCustomerResponse> agentInviteCustomerResponses = inviteCustomerResponse.getAgentInviteCustomerResponses();

        if (AssertUtils.isNotEmpty(agentInviteCustomerResponses)) {
            agentInviteCustomerResponses.sort(Comparator.comparing(AgentInviteCustomerResponse::getCreatedDate, Comparator.nullsLast(Long::compareTo)).reversed());
        }

        resultObject.setData(inviteCustomerResponse);
        return resultObject;
    }

    @Override
    public ResultObject<PartyCustomerResponse> getCustomerByFourElements(CustomerIdentityRequest customerIdentityRequest) {
        ResultObject<PartyCustomerResponse> resultObject = new ResultObject<>();
        String familyName = customerIdentityRequest.getFamilyName();
        String givenName = customerIdentityRequest.getGivenName();
        String idNo = customerIdentityRequest.getIdNo();
        String birthdayFormat = customerIdentityRequest.getBirthdayFormat();
        String sex = customerIdentityRequest.getSex();
        AssertUtils.isNotNull(this.getLogger(), familyName, PartyErrorConfigEnum.PARTY_CUSTOMER_FAMILY_NAME_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), givenName, PartyErrorConfigEnum.PARTY_CUSTOMER_GIVEN_NAME_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), idNo, PartyErrorConfigEnum.PARTY_PARAMETER_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), birthdayFormat, PartyErrorConfigEnum.PARTY_PARAMETER_BIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), sex, PartyErrorConfigEnum.PARTY_PARAMETER_SEX_IS_NOT_NULL);
        // 拼接姓名
        CustomerPo customerPo = customerBaseService.queryOneCustomerByFourElements(familyName + " " + givenName,
                idNo, DateUtils.stringToTime(birthdayFormat, DateUtils.FORMATE18), sex);
        AssertUtils.isNotNull(this.getLogger(), customerPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);

        PartyCustomerResponse partyCustomerResponse = (PartyCustomerResponse) converterObject(customerPo, PartyCustomerResponse.class);
        resultObject.setData(partyCustomerResponse);
        return resultObject;
    }

    @Override
    public ResultObject saveBaseClientAgentInfo(CustomerPersonalCenterRequest customerPersonalCenterRequest, String userId) {
        Users users = new Users();
        users.setUserId(userId);
        return saveClientAgentInfo(customerPersonalCenterRequest, users);
    }

    public InviteCustomerResponse queryListAgentApp(List<AgentInviteCustomerRecordPo> agentInviteCustomerRecordPosThisMonth, List<AgentInviteCustomerRecordPo> agentInviteCustomerRecordPos, InviteCustomerResponse inviteCustomerResponse) {
        //设置当月邀请客户数
        if (AssertUtils.isNotEmpty(agentInviteCustomerRecordPosThisMonth)) {
            //筛选出关联的用户id
            List<String> userIds = agentInviteCustomerRecordPosThisMonth.stream().map(AgentInviteCustomerRecordPo::getUserId).distinct().collect(Collectors.toList());

            if (AssertUtils.isNotEmpty(userIds)) {
                //根据用户id去查询users表里的用户登录信息
                ResultObject<List<UserResponse>> listResultObject = platformUsersApi.userInfoDetail(userIds);

                if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                    List<UserResponse> userResponses = listResultObject.getData();
                    List<AgentInviteCustomerResponse> agentInviteCustomerResponses = (List<AgentInviteCustomerResponse>) this.converterList(userResponses, new TypeToken<List<AgentInviteCustomerResponse>>() {
                    }.getType());

                    agentInviteCustomerResponses.removeIf(agentInviteCustomerResponse -> (null == agentInviteCustomerResponse.getLoginCount() || 0 == agentInviteCustomerResponse.getLoginCount()));

                    int size = agentInviteCustomerResponses.size();
                    inviteCustomerResponse.setInviteCustomerThisMonthNum(size + "");
                }
            }
        } else {
            inviteCustomerResponse.setInviteCustomerThisMonthNum(0 + "");
        }

        if (AssertUtils.isNotEmpty(agentInviteCustomerRecordPos)) {
            //筛选出关联的用户id
            List<String> userIds = agentInviteCustomerRecordPos.stream().map(AgentInviteCustomerRecordPo::getUserId).distinct().collect(Collectors.toList());

            if (AssertUtils.isNotEmpty(userIds)) {
                //根据用户id去查询users表里的用户登录信息
                ResultObject<List<UserResponse>> listResultObject = platformUsersApi.userInfoDetail(userIds);

                if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                    List<UserResponse> userResponses = listResultObject.getData();
                    List<AgentInviteCustomerResponse> agentInviteCustomerResponses = (List<AgentInviteCustomerResponse>) this.converterList(userResponses, new TypeToken<List<AgentInviteCustomerResponse>>() {
                    }.getType());

                    //手机※号校验
                    agentInviteCustomerResponses.forEach(agentInviteCustomerResponse -> {
                        if (AssertUtils.isNotEmpty(agentInviteCustomerResponse.getMobile())) {
                            String mobile = agentInviteCustomerResponse.getMobile();
                            char[] chars = mobile.toCharArray();
                            int length = chars.length;
                            if (length > 7) {
                                String mobileName = mobile.substring(0, 3) + "****" + mobile.substring(7, length);
                                agentInviteCustomerResponse.setMobileFormat(mobileName);
                            } else {
                                agentInviteCustomerResponse.setMobileFormat(mobile);
                            }
                        }
                    });

                    agentInviteCustomerResponses.removeIf(agentInviteCustomerResponse -> (null == agentInviteCustomerResponse.getLoginCount() || 0 == agentInviteCustomerResponse.getLoginCount()));

                    int size = agentInviteCustomerResponses.size();
                    inviteCustomerResponse.setInviteCustomerTotalNum(size + "");

                    inviteCustomerResponse.setAgentInviteCustomerResponses(agentInviteCustomerResponses);
                }
            } else {
                inviteCustomerResponse.setInviteCustomerTotalNum(0 + "");
            }
        } else {
            inviteCustomerResponse.setInviteCustomerTotalNum(0 + "");
        }
        return inviteCustomerResponse;
    }

    public InviteCustomerResponse queryListBmp(List<AgentInviteCustomerRecordPo> agentInviteCustomerRecordPosThisMonth, List<AgentInviteCustomerRecordPo> agentInviteCustomerRecordPos, InviteCustomerResponse inviteCustomerResponse) {
        //设置总邀请客户数
        if (AssertUtils.isNotEmpty(agentInviteCustomerRecordPos)) {
            //筛选出关联的用户id
            List<String> userIds = agentInviteCustomerRecordPos.stream().map(AgentInviteCustomerRecordPo::getUserId).distinct().collect(Collectors.toList());

            if (AssertUtils.isNotEmpty(userIds)) {
                //根据用户id去查询users表里的用户登录信息
                ResultObject<List<UserResponse>> listResultObject = platformUsersApi.userInfoDetail(userIds);

                if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                    List<UserResponse> userResponses = listResultObject.getData();
                    List<AgentInviteCustomerResponse> agentInviteCustomerResponses = (List<AgentInviteCustomerResponse>) this.converterList(userResponses, new TypeToken<List<AgentInviteCustomerResponse>>() {
                    }.getType());

                    agentInviteCustomerResponses.removeIf(agentInviteCustomerResponse -> (null == agentInviteCustomerResponse.getLoginCount() || 0 == agentInviteCustomerResponse.getLoginCount()));

                    int size = agentInviteCustomerResponses.size();
                    inviteCustomerResponse.setInviteCustomerTotalNum(size + "");
                }
            }
        } else {
            inviteCustomerResponse.setInviteCustomerTotalNum(0 + "");
        }

        if (AssertUtils.isNotEmpty(agentInviteCustomerRecordPosThisMonth)) {
            //筛选出关联的用户id
            List<String> userIds = agentInviteCustomerRecordPosThisMonth.stream().map(AgentInviteCustomerRecordPo::getUserId).distinct().collect(Collectors.toList());

            if (AssertUtils.isNotEmpty(userIds)) {
                //根据用户id去查询users表里的用户登录信息
                ResultObject<List<UserResponse>> listResultObject = platformUsersApi.userInfoDetail(userIds);

                if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                    List<UserResponse> userResponses = listResultObject.getData();
                    List<AgentInviteCustomerResponse> agentInviteCustomerResponses = (List<AgentInviteCustomerResponse>) this.converterList(userResponses, new TypeToken<List<AgentInviteCustomerResponse>>() {
                    }.getType());

                    //手机※号校验
                    agentInviteCustomerResponses.forEach(agentInviteCustomerResponse -> {
                        if (AssertUtils.isNotEmpty(agentInviteCustomerResponse.getMobile())) {
                            String mobile = agentInviteCustomerResponse.getMobile();
                            char[] chars = mobile.toCharArray();
                            int length = chars.length;
                            if (length > 7) {
                                String mobileName = mobile.substring(0, 3) + "****" + mobile.substring(7, length);
                                agentInviteCustomerResponse.setMobileFormat(mobileName);
                            } else {
                                agentInviteCustomerResponse.setMobileFormat(mobile);
                            }
                        }
                    });

                    agentInviteCustomerResponses.removeIf(agentInviteCustomerResponse -> (null == agentInviteCustomerResponse.getLoginCount() || 0 == agentInviteCustomerResponse.getLoginCount()));

                    int size = agentInviteCustomerResponses.size();
                    inviteCustomerResponse.setInviteCustomerThisMonthNum(size + "");

                    inviteCustomerResponse.setAgentInviteCustomerResponses(agentInviteCustomerResponses);
                }
            } else {
                inviteCustomerResponse.setInviteCustomerThisMonthNum(0 + "");
            }
        } else {
            inviteCustomerResponse.setInviteCustomerThisMonthNum(0 + "");
        }
        return inviteCustomerResponse;
    }
}

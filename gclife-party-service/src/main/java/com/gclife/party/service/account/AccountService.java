package com.gclife.party.service.account;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.party.model.request.account.AccountInputRecordRequest;
import com.gclife.party.model.request.account.AccountRequest;
import com.gclife.party.model.response.account.AccountDetailResponse;
import com.gclife.party.model.response.account.AccountInputRecordResponse;
import com.gclife.party.model.response.account.AccountOutputRecordResponse;
import com.gclife.party.model.response.account.AccountResponse;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description
 * @date 2019-07-12 13:02
 */
public interface AccountService {

    /**
     * 查询账户列表
     *
     * @param accountRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<BasePageResponse<AccountResponse>> queryAccount(AccountRequest accountRequest, Users currentLoginUsers);


    /**
     * 账户流入记录
     *
     * @param accountInputRecordRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<BasePageResponse<AccountInputRecordResponse>> inputRecord(AccountInputRecordRequest accountInputRecordRequest, Users currentLoginUsers);

    /**
     * 账户流出记录
     *
     * @param accountInputRecordRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<BasePageResponse<AccountOutputRecordResponse>> outputRecord(AccountInputRecordRequest accountInputRecordRequest, Users currentLoginUsers);

    /**
     * 查询账户详情
     *
     * @param userAccountId
     * @param currentLoginUsers
     * @return
     */
    ResultObject<AccountDetailResponse> queryAccountDetail(String userAccountId, Users currentLoginUsers);

    /**
     * 导出账户
     *
     * @param httpServletResponse
     * @param accountRequest
     * @param currentLoginUsers
     */
    void exportAccount(HttpServletResponse httpServletResponse, AccountRequest accountRequest, Users currentLoginUsers);

    /**
     * 导出账户流入详情
     *
     * @param httpServletResponse
     * @param accountInputRecordRequest
     * @param currentLoginUsers
     */
    void exportAccountInput(HttpServletResponse httpServletResponse, AccountInputRecordRequest accountInputRecordRequest, Users currentLoginUsers);

    /**
     * 账户流出详情
     *
     * @param httpServletResponse
     * @param accountInputRecordRequest
     * @param currentLoginUsers
     */
    void exportAccountOutput(HttpServletResponse httpServletResponse, AccountInputRecordRequest accountInputRecordRequest, Users currentLoginUsers);

    /**
     * 锁定用户账户
     *
     * @param userId 用户ID
     * @param reasonCode
     * @return
     */
    ResultObject lockUserAccount(String userId, String typeCode, String reasonCode);

}

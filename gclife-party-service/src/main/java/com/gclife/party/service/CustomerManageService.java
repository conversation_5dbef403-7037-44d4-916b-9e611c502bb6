package com.gclife.party.service;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.party.model.request.UserCustomerBusinessRequest;
import com.gclife.party.model.request.UserCustomerRequest;
import com.gclife.party.model.request.customer.ContactRecordRequest;
import com.gclife.party.model.request.customer.CustomerListRequest;
import com.gclife.party.model.response.CustomerMessageResponse;
import com.gclife.party.model.response.UserCustomerResponse;
import com.gclife.party.model.response.customer.ContactRecordResponse;
import com.gclife.party.model.response.customer.CustomerListResponse;
import com.gclife.party.model.response.customer.CustomerChooseResponse;
import com.gclife.party.model.response.customer.MemberResponse;

import java.util.List;


/**
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
public interface CustomerManageService {

    /**
     * 客户列表
     * @param customerListRequest
     * @param users 用户
     * @return
     */
    ResultObject<BasePageResponse<CustomerListResponse>> listCustomer(CustomerListRequest customerListRequest, Users users);

    /**
     * 选择客户列表
     * @param customerListRequest
     * @param users 用户
     * @return
     */
    ResultObject<BasePageResponse<CustomerChooseResponse>> listChooseCustomer(CustomerListRequest customerListRequest, Users users);

    /**
     * 家庭成员列表
     * @param customerId 客户ID
     * @param keyword 模糊搜索关键字
     * @return
     */
    ResultObject<List<MemberResponse>> listMember(String customerId, String keyword);

    /**
     * 移除家庭成员
     * @param oneselfCustomerId 本人客户ID
     * @param customerId 待移除成员客户ID
     * @return
     */
    ResultObject removeMember(String oneselfCustomerId, String customerId);

    /**
     * 删除客户
     * @param customerId 客户ID
     * @return
     */
    ResultObject deleteCustomer(String customerId);

    /**
     * 添加 客户信息
     *
     * @param customerRequest
     * @return
     */
    ResultObject<UserCustomerResponse> saveCustomerMessage(UserCustomerRequest customerRequest, Users users);


    /**
     * 获取 我的客户信息  根据客户ID
     *
     * @return
     */
    ResultObject<CustomerMessageResponse> getCustomerDetail(String customerId, String oneselfCustomerId, Users users);



    /**
     * 添加 用户客户所有数据
     *
     * @param userCustomerBusiness
     * @param users
     * @return
     */
    ResultObject<UserCustomerResponse> saveCustomerBusiness(UserCustomerBusinessRequest userCustomerBusiness, Users users);


    /**
     * 保存客户列表
     * @param userCustomerBusinessList
     * @param currentLoginUsers
     * @return
     */
    ResultObject<List<UserCustomerResponse>> saveCustomerBusinessList(List<UserCustomerBusinessRequest> userCustomerBusinessList, Users currentLoginUsers);

    /**
     * 修改客户信息
     * @param userCustomerBusinessList
     * @param currentLoginUsers
     * @return
     */
    ResultObject<List<UserCustomerResponse>> updateCustomerBusinessList(List<UserCustomerBusinessRequest> userCustomerBusinessList, Users currentLoginUsers);

    /**
     * 修改单个客户信息
     * @param userCustomerBusinessRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<UserCustomerResponse> updateCustomerBusiness(UserCustomerBusinessRequest userCustomerBusinessRequest, Users currentLoginUsers);

    /**
     * 根据主键修改Customer客户(不包含CustomerAgent)
     * @param userCustomerBusiness
     * @param users
     * @return
     */
    ResultObject<UserCustomerResponse> updateRealCustomerBusinessSingle(UserCustomerBusinessRequest userCustomerBusiness, Users users);

    /**
     * 用所选择的CustomerId数据覆盖CustomerAgent数据
     * @param customerId
     * @param customerAgentId
     * @param users
     * @return
     */
    ResultObject<UserCustomerResponse> updateCustomerAgentDataByCustomer(String customerId, String customerAgentId, Users users);

    /**
     * 保存沟通记录
     * @param contactRecordRequest
     * @param users 用户
     * @return
     */
    ResultObject saveContact(ContactRecordRequest contactRecordRequest, Users users);

    /**
     * 查询沟通记录列表
     * @param customerId 客户ID
     * @param contactEvent 沟通事件
     * @return
     */
    ResultObject<List<ContactRecordResponse>> listContactRecord(String customerId, String contactEvent);

    /**
     * 查询沟通记录
     * @param contactRecordId 沟通记录ID
     * @return
     */
    ResultObject<ContactRecordResponse> queryContactRecord(String contactRecordId);

    /**
     * 删除沟通记录
     * @param contactRecordId 沟通记录ID
     * @return
     */
    ResultObject deleteContactRecord(String contactRecordId);

    /**
     * 生日客户列表
     * @return
     * @param users
     */
    ResultObject<List<CustomerListResponse>> listBirthdayCustomer(Users users);

    /**
     * 保存客户手机号
     * @param customerId 客户ID
     * @param mobile 手机号
     * @return
     */
    ResultObject saveCustomerMobile(String customerId, String mobile);
}
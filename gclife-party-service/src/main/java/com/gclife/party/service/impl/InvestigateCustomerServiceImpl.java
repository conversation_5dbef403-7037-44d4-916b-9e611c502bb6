package com.gclife.party.service.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.party.model.bo.InvestigateCustomerBo;
import com.gclife.party.model.request.InvestigateCustomerQueryRequest;
import com.gclife.party.model.response.InvestigateCustomerListResponse;
import com.gclife.party.service.InvestigateCustomerService;
import com.gclife.party.service.base.InvestigateCustomerBaseService;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.google.common.reflect.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_QUESTION_LIST_ERROR;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 17:17 2018/10/15
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Service
public class InvestigateCustomerServiceImpl extends BaseBusinessServiceImpl implements InvestigateCustomerService {
    @Autowired
    private InvestigateCustomerBaseService investigateCustomerBaseService;

    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;

    @Override
    public ResultObject<BasePageResponse<InvestigateCustomerListResponse>> queryAppInvestigateCustomerList(InvestigateCustomerQueryRequest investigateCustomerRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<InvestigateCustomerListResponse>> resultObject = new ResultObject<>();
        try {
            investigateCustomerRequest.setUserId(currentLoginUsers.getUserId());
            List<InvestigateCustomerBo> investigateCustomerBoList = investigateCustomerBaseService.queryInvestigateCustomerList(investigateCustomerRequest, currentLoginUsers);
            if (!AssertUtils.isNotEmpty(investigateCustomerBoList)) {
                return resultObject;
            }
            List<InvestigateCustomerListResponse> investigateCustomerList = (List<InvestigateCustomerListResponse>) this.converterList(investigateCustomerBoList, new TypeToken<List<InvestigateCustomerListResponse>>() {
            }.getType());

            Integer totalLine = AssertUtils.isNotNull(investigateCustomerBoList) ? investigateCustomerBoList.get(0).getTotalLine() : null;

            BasePageResponse<InvestigateCustomerListResponse> basePageResponse = BasePageResponse.getData(investigateCustomerRequest.getCurrentPage(), investigateCustomerRequest.getPageSize(), totalLine, investigateCustomerList);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PARTY_QUERY_INVESTIGATE_QUESTION_LIST_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<InvestigateCustomerListResponse>> queryInvestigateCustomerList(InvestigateCustomerQueryRequest investigateCustomerRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<InvestigateCustomerListResponse>> resultObject = new ResultObject<>();
        try {

            if(AssertUtils.isNotEmpty(investigateCustomerRequest.getBranchId())){
                ResultObject<List<BranchResponse>> branchResultObject = platformBranchBaseApi.queryBranchTreeLeafListById(investigateCustomerRequest.getBranchId());
                AssertUtils.isResultObjectError(this.getLogger(),branchResultObject);
                List<BranchResponse> branchList = branchResultObject.getData();
                if(AssertUtils.isNotEmpty(branchList)){
                    List<String> branchIdList = branchList.stream().map(branchBaseRespFc -> branchBaseRespFc.getBranchId()).collect(Collectors.toList());
                    investigateCustomerRequest.setBranchIdList(branchIdList);
                }
            }
            List<InvestigateCustomerBo> investigateCustomerBoList = investigateCustomerBaseService.queryInvestigateCustomerList(investigateCustomerRequest, currentLoginUsers);
            if (!AssertUtils.isNotEmpty(investigateCustomerBoList)) {
                return resultObject;
            }
            List<InvestigateCustomerListResponse> investigateCustomerList = (List<InvestigateCustomerListResponse>) this.converterList(investigateCustomerBoList, new TypeToken<List<InvestigateCustomerListResponse>>() {
            }.getType());
            Integer totalLine = AssertUtils.isNotNull(investigateCustomerBoList) ? investigateCustomerBoList.get(0).getTotalLine() : null;
            BasePageResponse<InvestigateCustomerListResponse> basePageResponse = BasePageResponse.getData(investigateCustomerRequest.getCurrentPage(), investigateCustomerRequest.getPageSize(), totalLine, investigateCustomerList);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PARTY_QUERY_INVESTIGATE_QUESTION_LIST_ERROR);
            }
        }
        return resultObject;
    }



}

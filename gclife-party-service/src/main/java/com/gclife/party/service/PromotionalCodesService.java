package com.gclife.party.service;

import com.gclife.common.model.ResultObject;
import com.gclife.party.model.response.PromotionalCodesResponse;


/**
 * @ Author     : LiChongFu
 * @ Date       : Created in 20:09 2022/11/22
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
public interface PromotionalCodesService {
    /**
     * 优惠码查询
     *
     * @param code
     * @param flag
     * @param channelType
     * @return
     */
    ResultObject<PromotionalCodesResponse> queryPromotionalCodes(String code, String flag, String channelType);

    /**
     * 优惠码校验
     *
     * @param code
     * @return
     */
    String verifyPromotionalCodes(String url,String code);
}

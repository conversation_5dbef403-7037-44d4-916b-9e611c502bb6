package com.gclife.party.service;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.party.model.request.*;
import com.gclife.party.model.response.*;
import com.gclife.party.model.response.customer.PartyCustomerResponse;

/**
 * <AUTHOR>
 * create 2022/5/30 9:08
 * description:
 */
public interface CustomerClientBusinessService extends BaseBusinessService {

    /**
     * 客户APP注册
     *
     * @param customerClientRequest 客户APP注册
     * @param appRequestHeads       请求头
     * @return Void
     */
    ResultObject<CustomerAgentsResponse> registerClient(CustomerClientRequest customerClientRequest, AppRequestHeads appRequestHeads);

    /**
     * 客户APP注册回滚
     *
     * @param userId          用户ID
     * @param appRequestHeads 请求头
     * @return Void
     */
    ResultObject<Void> registerClientRollback(String userId, AppRequestHeads appRequestHeads);

    /**
     * 根据userId获取客户信息
     *
     * @param userId          用户ID
     * @param appRequestHeads 请求头
     * @return CustomerAgentResponse
     */
    ResultObject<ClientAgentResponse> getClientAgentInfo(String userId, AppRequestHeads appRequestHeads);

    /**
     * 修改clientCustomerAgent客户信息
     * @param customerPersonalCenterRequest  用户请求信息
     * @param users   用户
     * @return ResultObject
     */
    ResultObject saveClientAgentInfo(CustomerPersonalCenterRequest customerPersonalCenterRequest, Users users);

    /**
     * 修改clientCustomer客户信息
     * @param customerIdentityRequest  用户请求信息
     * @param appRequestHeads 请求头
     * @param users   用户
     * @return ResultObject
     */
    ResultObject saveClientInfo(CustomerIdentityRequest customerIdentityRequest, AppRequestHeads appRequestHeads, Users users);

    /**
     * 客户信息关联验证
     * @param customerIdentityRequest  用户请求信息
     * @param appRequestHeads 请求头
     * @param users   用户
     * @return ResultObject
     */
    ResultObject<VerifyRelationResponse> verifyClientRelation(CustomerIdentityRequest customerIdentityRequest, AppRequestHeads appRequestHeads, Users users);

    /**
     * 客户app身份放开验证
     * @param customerIdentityRequest  用户请求信息
     * @param appRequestHeads 请求头
     * @param users   用户
     * @return ResultObject
     */
    ResultObject<VerifyRelationResponse> verifyClientAppOpen(CustomerIdentityRequest customerIdentityRequest, AppRequestHeads appRequestHeads, Users users);

    /**
     * 客户APP注册
     *
     * @param agentInviteCustomerRequest 业务员邀请客户关系保存
     * @param users       用户
     * @return ResultObject
     */
    ResultObject agentInviteCustomerSave(AgentInviteCustomerRequest agentInviteCustomerRequest, Users users);

    /**
     * 客户APP注册
     *
     * @param agentInviteCustomerQueryRequest 业务员邀请客户关系查询
     * @param users       用户
     * @return ResultObject
     */
    ResultObject<InviteCustomerResponse> agentInviteCustomerQuery(AgentInviteCustomerQueryRequest agentInviteCustomerQueryRequest, Users users);

    /**
     * 根据四要素查询客户
     *
     * @param customerIdentityRequest
     * @return
     */
    ResultObject<PartyCustomerResponse> getCustomerByFourElements(CustomerIdentityRequest customerIdentityRequest);

    /**
     * 无需登录 修改clientCustomerAgent客户信息
     *
     * @param customerPersonalCenterRequest
     * @param userId
     * @return
     */
    ResultObject saveBaseClientAgentInfo(CustomerPersonalCenterRequest customerPersonalCenterRequest, String userId);
}

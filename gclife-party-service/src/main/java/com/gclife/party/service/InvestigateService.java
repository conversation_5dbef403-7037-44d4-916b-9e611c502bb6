package com.gclife.party.service;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.party.model.request.InvestigatePaperRequest;
import com.gclife.party.model.response.InvestigatePaperResponse;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 17:24 2018/10/10
 * @ Description: 调查title
 * @ Modified By:
 * @ Version: $version
 */
public interface InvestigateService {


    /**
     * 查询调查标题列表
     * 系统时间 大于调查title 开始时间结束时间
     * @param investigatePaperRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<BasePageResponse<InvestigatePaperResponse>> queryInvestigateList(InvestigatePaperRequest investigatePaperRequest,Users currentLoginUsers);
}

package com.gclife.party.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.response.AgentBaseResponse;
import com.gclife.agent.model.response.AgentDetailBaseResponse;
import com.gclife.agent.model.response.AgentLevelBaseResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.party.model.bo.*;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.CustomerAgentRequest;
import com.gclife.party.model.request.InvestigateCustomerRequest;
import com.gclife.party.model.request.InvestigateQuestionRequest;
import com.gclife.party.model.response.CustomerAgentResponse;
import com.gclife.party.model.response.InvestigateAgentResponse;
import com.gclife.party.model.response.InvestigateCustomerResponse;
import com.gclife.party.model.response.InvestigateQuestionResponse;
import com.gclife.party.model.vo.InvestigateQuestionVo;
import com.gclife.party.service.InvestigateQuestionService;
import com.gclife.party.service.base.*;
import com.gclife.party.transform.LanguageUtils;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.gclife.party.model.config.PartyErrorConfigEnum.*;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 11:16 2018/10/12
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Service
public class InvestigateQuestionServiceImpl extends BaseBusinessServiceImpl implements InvestigateQuestionService {

    @Autowired
    private InvestigateQuestionBaseService investigateQuestionBaseService;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private InvestigateCustomerBaseService investigateCustomerBaseService;
    @Autowired
    private InvestigateAnswerBaseService investigateAnswerBaseService;
    @Autowired
    private InvestigateAgentBaseService investigateAgentBaseService;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;
    @Autowired
    private InvestigateVersionBaseService investigateVersionBaseService;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;

    @Override
    public ResultObject<InvestigateCustomerResponse> queryAppInvestigateQuestion(String versionId, String investigateCustomerId, Users currentLoginUsers) {
        ResultObject<InvestigateCustomerResponse> resultObject = new ResultObject<>();
        try {
            InvestigateCustomerResponse investigateCustomerResponse = this.getInvestigateCustomerResponse(versionId, investigateCustomerId, currentLoginUsers);
            resultObject.setData(investigateCustomerResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PARTY_QUERY_INVESTIGATE_QUESTION_LIST_ERROR);
            }
        }
        return resultObject;
    }

    private InvestigateCustomerResponse getInvestigateCustomerResponse(String versionId, String investigateCustomerId, Users currentLoginUsers) {
        InvestigateCustomerResponse investigateCustomerResponse = new InvestigateCustomerResponse();

        InvestigateVersionBo investigateVersionBo = investigateVersionBaseService.queryInvestigateVersion(versionId);
        if (!AssertUtils.isNotNull(investigateVersionBo)) {
            throw new RequestException(PARTY_SAVE_INVESTIGATE_PAPER_NOT_NULL);
        }
        List<InvestigateQuestionResponse> investigateQuestionResponseList = new ArrayList<>();
        //查询问题作答列表
        List<InvestigateQuestionBo> investigateQuestionBoList = investigateQuestionBaseService.queryInvestigateQuestionList(versionId);
        //获取作答客户答案
        Map<String, List<InvestigateAnswerBo>> questionMapList = investigateCustomerBaseService.queryInvestigateCustomerAnswerMapList(investigateCustomerId);
        //国际化 如果有客户答案设置答案
        List<SyscodeResponse> syscodeRespFcList = platformInternationalBaseApi.queryInternational(investigateVersionBo.getPaperId(), currentLoginUsers.getLanguage()).getData();
        investigateQuestionBoList.forEach(investigateQuestionBo -> {
            investigateQuestionBo.setQuestionTitle(LanguageUtils.getCodeName(syscodeRespFcList, investigateQuestionBo.getQuestionTitle()));
            investigateQuestionBo.setQuestionDesc(LanguageUtils.getCodeName(syscodeRespFcList, investigateQuestionBo.getQuestionDesc()));

            if (PartyTermEnum.QUESTION_TYPE.TEXT.name().equals(investigateQuestionBo.getQuestionType())) {
                if (AssertUtils.isNotEmpty(questionMapList)) {
                    List<InvestigateAnswerBo> investigateAnswerBoList = questionMapList.get(investigateQuestionBo.getQuestionId());
                    if (AssertUtils.isNotEmpty(investigateAnswerBoList)) {
                        investigateQuestionBo.setInvestigateAnswer(investigateAnswerBoList.get(0));
                    }
                }
            } else {
                List<InvestigateQuestionOptionBo> optionList = investigateQuestionBo.getOptionList();
                if (AssertUtils.isNotEmpty(optionList)) {
                    optionList.forEach(investigateQuestionOptionBo -> {
                        investigateQuestionOptionBo.setQuestionOptionContent(LanguageUtils.getCodeName(syscodeRespFcList, investigateQuestionOptionBo.getQuestionOptionContent()));
                        if (AssertUtils.isNotEmpty(questionMapList)) {
                            List<InvestigateAnswerBo> investigateAnswerBoList = questionMapList.get(investigateQuestionBo.getQuestionId());
                            if (AssertUtils.isNotEmpty(investigateAnswerBoList)) {
                                Optional<InvestigateAnswerBo> answerOptional = investigateAnswerBoList.stream().filter(investigateAnswerBo -> investigateQuestionOptionBo.getQuestionOptionId().equals(investigateAnswerBo.getAnswerQuestionOptionId())).findFirst();
                                answerOptional.ifPresent(investigateAnswerBo -> investigateQuestionOptionBo.setInvestigateAnswer(investigateAnswerBo));
                            }
                        }
                    });
                }
            }

        });
        if (AssertUtils.isNotEmpty(investigateQuestionBoList)) {
            investigateQuestionResponseList = (List<InvestigateQuestionResponse>) this.converterList(investigateQuestionBoList, new TypeToken<List<InvestigateQuestionResponse>>() {
            }.getType());
        }
        investigateCustomerResponse.setInvestigateQuestionList(investigateQuestionResponseList);
        //受访者信息
        InvestigateCustomerBo investigateCustomerBo = investigateCustomerBaseService.queryInvestigateCustomer(investigateCustomerId, currentLoginUsers);
        if (AssertUtils.isNotNull(investigateCustomerBo)) {
            CustomerAgentResponse customerAgent = (CustomerAgentResponse) this.converterObject(investigateCustomerBo, CustomerAgentResponse.class);
            investigateCustomerResponse.setCustomerAgent(customerAgent);
        }
        return investigateCustomerResponse;
    }


    @Override
    @Transactional
    public ResultObject saveInvestigateQuestion(InvestigateCustomerRequest investigateCustomerRequest, Users currentLoginUsers) {
        ResultObject resultObject = new ResultObject();
        try {

            String userId = investigateCustomerRequest.getUserId();
            if (!AssertUtils.isNotEmpty(userId)) {
                throw new RequestException(PARTY_USER_ID_NOT_NULL);
            }
            if (!AssertUtils.isNotEmpty(investigateCustomerRequest.getVersionId())) {
                throw new RequestException(PARTY_SAVE_INVESTIGATE_PAPER_ID_NOT_NULL);
            }
            InvestigateVersionBo investigateVersionBo = investigateVersionBaseService.queryInvestigateVersion(investigateCustomerRequest.getVersionId());
            if (!AssertUtils.isNotNull(investigateVersionBo)) {
                throw new RequestException(PARTY_SAVE_INVESTIGATE_PAPER_NOT_NULL);
            }
            Long createdDate = null;
            String createdDateFormat = investigateCustomerRequest.getCreatedDateFormat();
            if (AssertUtils.isDateFormat(createdDateFormat, DateUtils.FORMATE20)) {
                createdDate = DateUtils.stringToTime(createdDateFormat, DateUtils.FORMATE20);
            }
            if (!AssertUtils.isNotNull(createdDate)) {
                createdDate = System.currentTimeMillis();
            }
            this.getLogger().info("investigateCustomerRequest:::::" + JSON.toJSONString(investigateCustomerRequest));
            this.getLogger().info("currentLoginUsers:::::" + JSON.toJSONString(currentLoginUsers));
            String investigateId = UUIDUtils.getUUIDShort();
            //保存客户答案信息
            List<InvestigateQuestionRequest> investigateQuestionList = investigateCustomerRequest.getInvestigateQuestionList();
            if (AssertUtils.isNotEmpty(investigateQuestionList)) {
                investigateAnswerBaseService.insertInvestigateCustomerAnswerList(userId, investigateId,
                        JSONObject.parseArray(JSON.toJSON(investigateQuestionList).toString(), InvestigateQuestionVo.class), createdDate);
            }
            //保存客户信息
            CustomerAgentRequest customerAgent = investigateCustomerRequest.getCustomerAgent();
            if (AssertUtils.isNotNull(customerAgent)) {
                investigateCustomerBaseService.saveInvestigateCustomer(investigateVersionBo, userId, investigateId, customerAgent, createdDate);
            }

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PARTY_SAVE_INVESTIGATE_QUESTION_LIST_ERROR);
            }
        }
        return resultObject;
    }


    @Override
    public ResultObject<InvestigateCustomerResponse> queryInvestigateQuestion(String versionId, String investigateCustomerId, Users currentLoginUsers) {
        ResultObject<InvestigateCustomerResponse> resultObject = new ResultObject<>();
        try {
            InvestigateAgentBo investigateAgentBo = investigateAgentBaseService.queryInvestigateAgentByCustomerId(investigateCustomerId);
            if (!AssertUtils.isNotNull(investigateAgentBo)) {
                throw new RequestException(PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);
            }
            ResultObject<AgentBaseResponse> agentBaseResultObject = agentBaseAgentApi.queryOneAgentById(investigateAgentBo.getAgentId());
            AssertUtils.isResultObjectError(this.getLogger(), agentBaseResultObject);
            AgentBaseResponse agent = agentBaseResultObject.getData();
            AgentDetailBaseResponse agentDetail = agent.getAgentDetail();
            //设置手机
            if (AssertUtils.isNotNull(agentDetail)) {
                investigateAgentBo.setAgentMobile(agentDetail.getMobile());
            }
            //设置职级
            AgentLevelBaseResponse agentLevel = agent.getAgentLevel();
            if (AssertUtils.isNotNull(agentLevel)) {
                investigateAgentBo.setAgentLevelName(agentLevel.getAgentLevelName());
            }
            BranchResponse branch = platformBranchBaseApi.queryOneBranchById(agent.getBranchId()).getData();
            if (AssertUtils.isNotNull(branch)) {
                investigateAgentBo.setBranchName(branch.getBranchName());
            }
            InvestigateCustomerResponse investigateCustomerResponse = this.getInvestigateCustomerResponse(versionId, investigateCustomerId, currentLoginUsers);
            InvestigateAgentResponse investigateAgentResponse = (InvestigateAgentResponse) this.converterObject(investigateAgentBo, InvestigateAgentResponse.class);
            investigateCustomerResponse.setInvestigateAgent(investigateAgentResponse);
            resultObject.setData(investigateCustomerResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PARTY_QUERY_INVESTIGATE_QUESTION_LIST_ERROR);
            }
        }
        return resultObject;
    }
}

package com.gclife.party.service;

import com.gclife.common.service.BaseBusinessService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create 19-6-24
 * description:
 */
public interface MessageBusinessService extends BaseBusinessService {
    /**
     * 发送消息
     * @param businessCode 业务类型
     * @param userIdList 用户LIST
     * @param messageParam 消息体
     */
    void pushBusinessMessage(String businessCode, List<String> userIdList, Map<String,String> messageParam);
}

package com.gclife.party.service.impl;

import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentKeyWordResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.client.api.ClientFamilyApi;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.party.core.jooq.tables.pojos.CustomerAgentChangeRecordPo;
import com.gclife.party.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.party.core.jooq.tables.pojos.PolicyPo;
import com.gclife.party.model.bo.ClientManageListBo;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.client.ClientChangeServiceRequest;
import com.gclife.party.model.request.client.ClientManageListRequest;
import com.gclife.party.model.response.client.ClientChangeRecordResponse;
import com.gclife.party.model.response.client.ClientDicResponse;
import com.gclife.party.model.response.client.ClientManageDetailResponse;
import com.gclife.party.model.response.client.ClientManageListResponse;
import com.gclife.party.model.response.customer.MemberResponse;
import com.gclife.party.service.ClientAppManageService;
import com.gclife.party.service.CustomerManageService;
import com.gclife.party.service.base.ClientManagerBaseService;
import com.gclife.party.service.base.CustomerBaseService;
import com.gclife.platform.api.PlatformEmployeeApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.api.PlatformUsersBaseApi;
import com.gclife.platform.model.response.EmployeResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.request.ClientServiceAgentChangeRequest;
import com.gclife.policy.model.request.PolicyQueryRequest;
import com.gclife.policy.model.response.PolicyListResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 2022/8/10 9:44
 * description:
 */
@Service
@Slf4j
public class ClientAppManageServiceImpl extends BaseBusinessServiceImpl implements ClientAppManageService {
    @Autowired
    private ClientManagerBaseService clientManagerBaseService;
    @Autowired
    private CustomerBaseService customerBaseService;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PlatformEmployeeApi platformEmployeeApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private PlatformUsersBaseApi platformUsersBaseApi;
    @Autowired
    private ClientFamilyApi clientFamilyApi;
    @Autowired
    private CustomerManageService customerManageService;
    @Autowired
    private AttachmentApi attachmentApi;
    @Autowired
    private PolicyApi policyApi;

    /**
     * 客户管理列表
     *
     * @param clientManageListRequest 列表请求
     * @return ClientManageListResponses
     */
    @Override
    public ResultObject<BasePageResponse<ClientManageListResponse>> queryClientManageList(ClientManageListRequest clientManageListRequest) {
        ResultObject<BasePageResponse<ClientManageListResponse>> resultObject = new ResultObject<>();
        List<ClientManageListResponse> clientManageListResponses = new ArrayList<>();

        if (AssertUtils.isNotEmpty(clientManageListRequest.getKeyword())) {
            List<AgentKeyWordResponse> agentKeyWordResponseList = agentApi.loadAllAgentsByKeyword(clientManageListRequest.getKeyword()).getData();
            if (AssertUtils.isNotEmpty(agentKeyWordResponseList)) {
                List<String> agentIdList = agentKeyWordResponseList.stream().map(AgentKeyWordResponse::getAgentId).distinct().collect(Collectors.toList());
                clientManageListRequest.setServiceAgentIds(agentIdList);
            }
        }
        List<ClientManageListBo> clientManageListBos = clientManagerBaseService.queryClientManageList(clientManageListRequest);
        if (!AssertUtils.isNotEmpty(clientManageListBos)) {
            return resultObject;
        }

        List<String> serviceAgentIds = clientManageListBos.stream().map(ClientManageListBo::getUserId).distinct().collect(Collectors.toList());
        AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
        agentApplyQueryRequest.setListAgentId(serviceAgentIds);
        ResultObject<List<AgentResponse>> serviceAgentResponseList = agentApi.agentsGet(agentApplyQueryRequest);

        List<String> customerAgentIds = clientManageListBos.stream().map(ClientManageListBo::getCustomerAgentId).distinct().collect(Collectors.toList());
        ResultObject<List<UserResponse>> customerUsers = platformUsersBaseApi.queryUsersPoByIds(customerAgentIds);

        List<SyscodeResponse> idTypeSysCodes = platformInternationalBaseApi.queryInternational(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.ID_TYPE.name(), null).getData();

        clientManageListBos.forEach(clientManageListBo -> {
            ClientManageListResponse clientManageListResponse = new ClientManageListResponse();
            ClazzUtils.copyPropertiesIgnoreNull(clientManageListBo, clientManageListResponse);
            if (!AssertUtils.isResultObjectListDataNull(serviceAgentResponseList)) {
                serviceAgentResponseList.getData().stream().filter(agentResponse -> agentResponse.getAgentId().equals(clientManageListBo.getServiceAgentId()))
                        .findFirst().ifPresent(agentResponse -> {
                    clientManageListResponse.setServiceAgentName(agentResponse.getAgentName());
                    clientManageListResponse.setServiceAgentCode(agentResponse.getAgentCode());
                    clientManageListResponse.setServiceAgentNameCode(agentResponse.getAgentName() + "/" + agentResponse.getAgentCode());
                });
            }
            if (!AssertUtils.isResultObjectListDataNull(customerUsers)) {
                customerUsers.getData().stream().filter(userResponse -> userResponse.getUserId().equals(clientManageListBo.getCustomerAgentId()))
                        .findFirst().ifPresent(userResponse -> {
                    clientManageListResponse.setLoginCount(userResponse.getLoginCount());
                    clientManageListResponse.setLastLogin(userResponse.getLoginLast());
                });
            }
            if (AssertUtils.isNotEmpty(clientManageListResponse.getIdNo())) {
                idTypeSysCodes.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals(clientManageListResponse.getIdType())).findFirst().ifPresent(syscodeResponse -> {
                    clientManageListResponse.setIdTypeNo(syscodeResponse.getCodeName() + "/" + clientManageListResponse.getIdNo());
                });
            }
            clientManageListResponses.add(clientManageListResponse);
        });

        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(clientManageListBos) ? clientManageListBos.get(0).getTotalLine() : null;
        BasePageResponse basePageResponse = BasePageResponse.getData(clientManageListRequest.getCurrentPage(), clientManageListRequest.getPageSize(), totalLine, clientManageListResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    /**
     * 客户信息详情
     *
     * @param customerAgentId 客户ID
     * @return ClientManageDetailResponse
     */
    @Override
    public ResultObject<ClientManageDetailResponse> queryClientInfoDetail(String customerAgentId) {
        ResultObject<ClientManageDetailResponse> resultObject = new ResultObject<>();
        ClientManageDetailResponse clientManageDetailResponse = new ClientManageDetailResponse();

        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(customerAgentId);
        AssertUtils.isNotNull(log, customerAgentPo, PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);

        ClazzUtils.copyPropertiesIgnoreNull(customerAgentPo, clientManageDetailResponse);
        clientManageDetailResponse.setRegisterDate(customerAgentPo.getCreatedDate());

        ResultObject<AgentResponse> agentResponseResultObject = agentApi.agentByIdGet(customerAgentPo.getUserId());
        if (!AssertUtils.isResultObjectDataNull(agentResponseResultObject)) {
            clientManageDetailResponse.setServiceAgentCode(agentResponseResultObject.getData().getAgentCode());
            clientManageDetailResponse.setServiceAgentName(agentResponseResultObject.getData().getAgentName());
            clientManageDetailResponse.setServiceAgentBranchName(agentResponseResultObject.getData().getBranchName());
            clientManageDetailResponse.setServiceAgentLevelCodeName(agentResponseResultObject.getData().getAgentLevelCodeName());
            clientManageDetailResponse.setServiceAgentMobile(agentResponseResultObject.getData().getMobile());
            clientManageDetailResponse.setServiceAgentStatus(agentResponseResultObject.getData().getAgentStatus());
        }
        UserResponse userResponse = platformUsersBaseApi.queryOneUsersPoById(customerAgentPo.getCustomerAgentId()).getData();
        if (AssertUtils.isNotNull(userResponse)) {
            clientManageDetailResponse.setLoginCount(userResponse.getLoginCount());
            clientManageDetailResponse.setLastLogin(userResponse.getLoginLast());
            if (AssertUtils.isNotEmpty(userResponse.getListThirdParty())) {
                userResponse.getListThirdParty().forEach(userThirdPartyResponse -> {
                    if (PartyTermEnum.THIRD_PARTY_CODE.GOOGLE.name().equals(userThirdPartyResponse.getThirdPartyCode())) {
                        clientManageDetailResponse.setGoogle(userThirdPartyResponse.getNickname());
                    }
                    if (PartyTermEnum.THIRD_PARTY_CODE.FACEBOOK.name().equals(userThirdPartyResponse.getThirdPartyCode())) {
                        clientManageDetailResponse.setFacebook(userThirdPartyResponse.getNickname());
                    }
                });
            }
        }
        //家庭列表
        ResultObject<List<MemberResponse>> members = customerManageService.listMember(customerAgentId, null);
        if (!AssertUtils.isResultObjectListDataNull(members)) {
            members.getData().stream().filter(memberResponse -> "ONESELF".equals(memberResponse.getRelationship()))
                    .findFirst().ifPresent(memberResponse -> {
                List<CustomerAgentPo> customerAgentPos = customerBaseService.getCustomerRelationByCustomerAgentId(customerAgentId);
                PolicyQueryRequest policyQueryRequest = new PolicyQueryRequest();
                policyQueryRequest.setCustomerIds(customerAgentPos.stream().map(CustomerAgentPo::getCustomerAgentId).distinct().collect(Collectors.toList()));
                policyQueryRequest.setPageSize(1000);
                policyQueryRequest.setCurrentPage(1);
                //保单列表
                ResultObject<BasePageResponse<PolicyListResponse>> policyListResultObject = policyApi.getClientPolicyQueryList(policyQueryRequest);
                if (!AssertUtils.isResultObjectDataNull(policyListResultObject) && AssertUtils.isNotNull(policyListResultObject.getData())
                        && AssertUtils.isNotEmpty(policyListResultObject.getData().getData())) {
                    clientManageDetailResponse.setPolicyNum(policyListResultObject.getData().getData().size());
                }
                List<PolicyPo> policyPos = customerBaseService.listPolicy(customerAgentId);
                clientManageDetailResponse.setPolicyNum(clientManageDetailResponse.getPolicyNum() + policyPos.size());
            });
            List<MemberResponse> collect = members.getData().stream().filter(memberResponse -> !"ONESELF".equals(memberResponse.getRelationship()))
                    .collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(collect)) {
                collect.forEach(memberListResponse -> {
                    List<PolicyPo> policyPos = customerBaseService.listPolicy(memberListResponse.getCustomerId());
                    memberListResponse.setPolicyNum(policyPos.size());
                    if (AssertUtils.isNotEmpty(memberListResponse.getAvoirdupois())) {
                        memberListResponse.setAvoirdupois(memberListResponse.getAvoirdupois() + "KG");
                    }
                    if (AssertUtils.isNotEmpty(memberListResponse.getStature())) {
                        memberListResponse.setStature(memberListResponse.getStature() + "CM");
                    }
                });
            }
            clientManageDetailResponse.setMembers(collect);
        }
        resultObject.setData(clientManageDetailResponse);
        return resultObject;
    }

    /**
     * 变更保障顾问
     *
     * @param clientChangeServiceRequest 变更保障顾问
     * @param users                      用户
     * @return Void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<Void> postChangeService(ClientChangeServiceRequest clientChangeServiceRequest, Users users) {
        log.info("变更保障顾问:{}", JackSonUtils.toJson(clientChangeServiceRequest));
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(clientChangeServiceRequest.getCustomerAgentId());
        if (!AssertUtils.isNotNull(customerAgentPo) || !PartyTermEnum.CUSTOMER_TYPE.CLIENT.name().equals(customerAgentPo.getCustomerType())) {
            AssertUtils.isNotNull(log, customerAgentPo, PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);
        }
        AssertUtils.isNotNull(log, clientChangeServiceRequest.getServiceAgentId(), PartyErrorConfigEnum.PARTY_SERVICE_AGENT_ID_NOT_NULL);
        AssertUtils.isNotNull(log, clientChangeServiceRequest.getRemark(), PartyErrorConfigEnum.PARTY_REMARK_IS_NOT_NULL_ERROR);

        if (clientChangeServiceRequest.getServiceAgentId().equals(customerAgentPo.getUserId())) {
            throwsException(log, PartyErrorConfigEnum.PARTY_CHOOSE_NEW_SERVICE_AGENT);
        }
        //保存变更信息
        CustomerAgentChangeRecordPo customerAgentChangeRecordPo = new CustomerAgentChangeRecordPo();
        customerAgentChangeRecordPo.setCustomerAgentId(clientChangeServiceRequest.getCustomerAgentId());
        customerAgentChangeRecordPo.setBeforeChange(customerAgentPo.getUserId());
        customerAgentChangeRecordPo.setCustomerChangeType(PartyTermEnum.CUSTOMER_CHANGE_TYPE.CHANGE_SERVICE_AGENT.name());
        customerAgentChangeRecordPo.setOptionUserId(users.getUserId());
        customerAgentChangeRecordPo.setEffectiveDate(DateUtils.getCurrentTime());
        customerAgentChangeRecordPo.setRemark(clientChangeServiceRequest.getRemark());

        customerAgentPo.setUserId(clientChangeServiceRequest.getServiceAgentId());
        customerBaseService.saveCustomerAgent(customerAgentPo);

        customerAgentChangeRecordPo.setAfterChange(clientChangeServiceRequest.getServiceAgentId());
        clientManagerBaseService.saveCustomerAgentChangeRecord(users.getUserId(), Collections.singletonList(customerAgentChangeRecordPo));
        //同时变更该客户作为投保人下的所有保单的服务业务人员
        List<CustomerAgentPo> customerAgentPos = customerBaseService.getCustomerRelationByCustomerAgentId(clientChangeServiceRequest.getCustomerAgentId());
        ClientServiceAgentChangeRequest clientServiceAgentChangeRequest = new ClientServiceAgentChangeRequest();
        clientServiceAgentChangeRequest.setCustomerAgentIds(customerAgentPos.stream().map(CustomerAgentPo::getCustomerAgentId).distinct().collect(Collectors.toList()));
        clientServiceAgentChangeRequest.setServiceAgentId(clientChangeServiceRequest.getServiceAgentId());
        log.info("同时变更该客户作为投保人下的所有保单的服务业务人员:{}", JackSonUtils.toJson(clientServiceAgentChangeRequest));
        ResultObject<Void> postClientServiceAgentChange = policyApi.postClientServiceAgentChange(clientServiceAgentChangeRequest);
        AssertUtils.isResultObjectError(log, postClientServiceAgentChange);

        return ResultObject.success();
    }

    /**
     * 异动列表
     *
     * @param customerAgentId 客户ID
     * @param appRequestHeads
     * @return 异动列表
     */
    @Override
    public ResultObject<List<ClientChangeRecordResponse>> getChangeRecordList(String customerAgentId, AppRequestHeads appRequestHeads) {
        ResultObject<List<ClientChangeRecordResponse>> resultObject = new ResultObject<>();

        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(customerAgentId);
        if (!AssertUtils.isNotNull(customerAgentPo) || !PartyTermEnum.CUSTOMER_TYPE.CLIENT.name().equals(customerAgentPo.getCustomerType())) {
            AssertUtils.isNotNull(log, customerAgentPo, PartyErrorConfigEnum.PARTY_QUERY_INVESTIGATE_CUSTOMER_IS_NULL);
        }

        List<CustomerAgentChangeRecordPo> customerAgentChangeRecordPos = clientManagerBaseService.queryCustomerAgentChangeRecord(customerAgentId);
        if (!AssertUtils.isNotEmpty(customerAgentChangeRecordPos)) {
            return resultObject;
        }
        List<SyscodeResponse> changeDetailTypes = platformInternationalBaseApi.queryInternational("CHANGE_DETAIL_TYPE", appRequestHeads.getLanguage()).getData();

        List<ClientChangeRecordResponse> clientChangeRecordResponses = new ArrayList<>();
        customerAgentChangeRecordPos.forEach(customerAgentChangeRecordPo -> {
            ClientChangeRecordResponse clientChangeRecordResponse = new ClientChangeRecordResponse();
            ClazzUtils.copyPropertiesIgnoreNull(customerAgentChangeRecordPo, clientChangeRecordResponse);
            //变更保障顾问
            if (PartyTermEnum.CUSTOMER_CHANGE_TYPE.CHANGE_SERVICE_AGENT.name().equals(customerAgentChangeRecordPo.getCustomerChangeType())) {
                //变更前后
                ResultObject<AgentResponse> agentBefore = agentApi.agentByIdGet(customerAgentChangeRecordPo.getBeforeChange());
                if (!AssertUtils.isResultObjectDataNull(agentBefore)) {
                    clientChangeRecordResponse.setBeforeChange(agentBefore.getData().getAgentName() + "/" + agentBefore.getData().getAgentCode());
                }
                ResultObject<AgentResponse> agentAfter = agentApi.agentByIdGet(customerAgentChangeRecordPo.getAfterChange());
                if (!AssertUtils.isResultObjectDataNull(agentAfter)) {
                    clientChangeRecordResponse.setAfterChange(agentAfter.getData().getAgentName() + "/" + agentAfter.getData().getAgentCode());
                }
                //操作用户
                ResultObject<EmployeResponse> employeResponseResultObject = platformEmployeeApi.employeGet(customerAgentChangeRecordPo.getOptionUserId());
                if (!AssertUtils.isResultObjectDataNull(employeResponseResultObject)) {
                    clientChangeRecordResponse.setOptionUserName(employeResponseResultObject.getData().getEmployeName());
                }
            }
            //个人信息
            if (PartyTermEnum.CUSTOMER_CHANGE_TYPE.PERSONAL_INFO.name().equals(customerAgentChangeRecordPo.getCustomerChangeType())) {
                clientChangeRecordResponse.setOptionUserName(customerAgentPo.getName());
                //变更前后
                //前缀
                final String[] prefix = {null};
                if (AssertUtils.isNotEmpty(changeDetailTypes)) {
                    changeDetailTypes.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals(customerAgentChangeRecordPo.getChangeDetailType()))
                            .findFirst().ifPresent(syscodeResponse -> prefix[0] = syscodeResponse.getCodeName());
                }
                if (PartyTermEnum.CHANGE_DETAIL_TYPE.AVATAR.name().equals(customerAgentChangeRecordPo.getChangeDetailType())) {
                    ResultObject<AttachmentResponse> attachmentBefore = attachmentApi.attachmentGet(customerAgentChangeRecordPo.getBeforeChange());
                    if (!AssertUtils.isResultObjectDataNull(attachmentBefore)) {
                        clientChangeRecordResponse.setBeforeChange(prefix[0] + ": ");
                        clientChangeRecordResponse.setBeforeChangeFileUrl(attachmentBefore.getData().getUrl());
                    } else {
                        clientChangeRecordResponse.setBeforeChange(prefix[0] + ": ");
                        clientChangeRecordResponse.setBeforeChangeFileUrl("https://release-oss.gc-life.com/gclife/client/app/avatar/client_default_avatar.svg");
                    }
                    ResultObject<AttachmentResponse> attachmentAfter = attachmentApi.attachmentGet(customerAgentChangeRecordPo.getAfterChange());
                    if (!AssertUtils.isResultObjectDataNull(attachmentAfter)) {
                        clientChangeRecordResponse.setAfterChange(prefix[0] + ": ");
                        clientChangeRecordResponse.setAfterChangeFileUrl(attachmentAfter.getData().getUrl());
                    }
                } else {
                    clientChangeRecordResponse.setBeforeChange(prefix[0] + ": " + customerAgentChangeRecordPo.getBeforeChange());
                    clientChangeRecordResponse.setAfterChange(prefix[0] + ": " + customerAgentChangeRecordPo.getAfterChange());
                }
            }
            clientChangeRecordResponses.add(clientChangeRecordResponse);
        });
        resultObject.setData(clientChangeRecordResponses);
        return resultObject;
    }

    /**
     * 字典数据
     *
     * @param appRequestHeads 请求头
     * @return ClientDicResponse
     */
    @Override
    public ResultObject<ClientDicResponse> getDic(AppRequestHeads appRequestHeads) {
        ResultObject<ClientDicResponse> resultObject = new ResultObject<>();
        ClientDicResponse policyDicResponse = new ClientDicResponse();

        // 批量查询国际化
        List<String> types = Arrays.asList("PRODUCT_PREMIUM_FREQUENCY",
                "PRODUCT_PERIOD_AGE",
                "PRODUCT_PERIOD_YEAR",
                "CLIENT_CERTIFICATION_STATUS",
                "PRODUCT_MAIN_PRODUCT_FLAG",
                "RELATIONSHIP",
                "MSG_STATUS",
                "ADD_METHOD",
                "POLICY_INPUT_STATUS",
                "CLIENT_APP_MESSAGE_BUSINESS_CODE",
                "POLICY_STATUS",
                "POLICY_INPUT_STATUS",
                "GENDER",
                TerminologyTypeEnum.ID_TYPE.name()
        );
        Map<String, List<SyscodeResponse>> internationalList = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(appRequestHeads.getLanguage(), types).getData();

        List<SyscodeResponse> premiumFrequencySyscodes = internationalList.get("PRODUCT_PREMIUM_FREQUENCY");
        premiumFrequencySyscodes.removeIf(syscodeResponse -> "MAIN".equals(syscodeResponse.getCodeKey()));
        policyDicResponse.setPremiumFrequency(premiumFrequencySyscodes);
        policyDicResponse.setProductPeriodAge(internationalList.get("PRODUCT_PERIOD_AGE"));
        policyDicResponse.setProductPeriodYear(internationalList.get("PRODUCT_PERIOD_YEAR"));
        policyDicResponse.setRelationship(internationalList.get("RELATIONSHIP"));
        policyDicResponse.setClientCertificationStatus(internationalList.get("CLIENT_CERTIFICATION_STATUS"));
        policyDicResponse.setMainProductFlag(internationalList.get("PRODUCT_MAIN_PRODUCT_FLAG"));
        policyDicResponse.setAddMethod(internationalList.get("ADD_METHOD"));
        policyDicResponse.setPolicyStatus(internationalList.get("POLICY_STATUS"));
        policyDicResponse.setInputStatus(internationalList.get("POLICY_INPUT_STATUS"));
        policyDicResponse.setMessageType(internationalList.get("CLIENT_APP_MESSAGE_BUSINESS_CODE"));
        policyDicResponse.setSendStatus(internationalList.get("MSG_STATUS"));
        policyDicResponse.setSex(internationalList.get("GENDER"));
        policyDicResponse.setIdType(internationalList.get(TerminologyTypeEnum.ID_TYPE.name()));

        resultObject.setData(policyDicResponse);
        return resultObject;
    }
}

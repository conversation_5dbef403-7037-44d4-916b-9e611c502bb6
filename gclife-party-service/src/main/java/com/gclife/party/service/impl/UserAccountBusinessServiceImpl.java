package com.gclife.party.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.common.PartyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.*;
import com.gclife.party.core.jooq.tables.daos.UserAccountDao;
import com.gclife.party.core.jooq.tables.pojos.*;
import com.gclife.party.dao.UserAccountBaseDao;
import com.gclife.party.model.bo.AccountCashWithdrawalBatchBo;
import com.gclife.party.model.bo.UserAccountBo;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.*;
import com.gclife.party.model.response.CashWithdrawalResponse;
import com.gclife.party.model.response.UserAccountDetailResponse;
import com.gclife.party.model.response.UserAccountResponse;
import com.gclife.party.model.response.UserAccountTransactionRecordResponse;
import com.gclife.party.model.vo.AccountDetailVo;
import com.gclife.party.service.UserAccountBusinessService;
import com.gclife.party.service.base.UserAccountBaseService;
import com.gclife.party.validate.parameter.UserAccountParameterValidate;
import com.gclife.party.validate.transfer.UserAccountTransfer;
import com.gclife.payment.api.ReceiptBaseApi;
import com.gclife.payment.model.request.ReceiptItemRequest;
import com.gclife.payment.model.request.ReceiptRequest;
import com.gclife.payment.model.response.ReceiptResponse;
import com.gclife.platform.api.PlatformAccountApi;
import com.gclife.platform.api.PlatformConfigApi;
import com.gclife.platform.model.response.AccountResponse;
import com.gclife.platform.model.response.NotifyConfigResponse;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyErrorConfigEnum.*;
import static com.gclife.party.model.config.PartyTermEnum.*;

/**
 * <AUTHOR>
 * create 19-6-13
 * description:
 */
@Service
@Slf4j
public class UserAccountBusinessServiceImpl extends BaseBusinessServiceImpl implements UserAccountBusinessService {
    @Autowired
    private UserAccountParameterValidate userAccountParameterValidate;
    @Autowired
    private UserAccountBaseService userAccountBaseService;
    @Autowired
    private UserAccountBaseDao userAccountBaseDao;
    @Autowired
    private UserAccountTransfer userAccountTransfer;

    @Autowired
    private UserAccountDao userAccountDao;

    @Autowired
    private PlatformAccountApi platformAccountApi;
    @Autowired
    private PlatformConfigApi platformConfigApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private ReceiptBaseApi receiptBaseApi;

    /**
     * 批量保存账户收入
     *
     * @param userAccountIncomeRequests 收入参数
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postUserAccountIncomeList(List<UserAccountIncomeRequest> userAccountIncomeRequests) {
        ResultObject resultObject = new ResultObject();
        AssertUtils.isNotEmpty(log, userAccountIncomeRequests, PARTY_USER_ACCOUNT_INCOME_IS_NOT_NULL);
        userAccountIncomeRequests.forEach(userAccountIncomeRequest -> userAccountTransfer.saveAccountIncome(userAccountIncomeRequest));
        return resultObject;
    }

    /**
     * 保存账户收入
     *
     * @param userAccountIncomeRequest 收入参数
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postUserAccountIncome(UserAccountIncomeRequest userAccountIncomeRequest) {
        ResultObject resultObject = new ResultObject();
        userAccountTransfer.saveAccountIncome(userAccountIncomeRequest);
        return resultObject;
    }

    /**
     * 保存账户支出
     *
     * @param userAccountOutlayRequest 收入参数
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject<UserAccountTransactionRecordResponse> postUserAccountOutlay(UserAccountOutlayRequest userAccountOutlayRequest) {
        ResultObject<UserAccountTransactionRecordResponse> resultObject = new ResultObject<>();

        UserAccountTransactionRecordResponse userAccountTransactionRecordResponse = userAccountTransfer.getUserAccountTransactionRecordResponse(userAccountOutlayRequest);

        resultObject.setData(userAccountTransactionRecordResponse);
        return resultObject;
    }

    /**
     * 批量保存账户支出
     *
     * @param userAccountOutlayRequests 支出参数
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject<List<UserAccountTransactionRecordResponse>> postUserAccountOutlayBatch(List<UserAccountOutlayRequest> userAccountOutlayRequests) {
        ResultObject<List<UserAccountTransactionRecordResponse>> resultObject = new ResultObject<>();
        List<UserAccountTransactionRecordResponse> userAccountTransactionRecordResponses = new ArrayList<>();

        AssertUtils.isNotEmpty(log, userAccountOutlayRequests, PARTY_USER_ACCOUNT_OUTLAY_IS_NOT_NULL);
        userAccountOutlayRequests.forEach(userAccountOutlayRequest -> {
            UserAccountTransactionRecordResponse userAccountTransactionRecordResponse = userAccountTransfer.getUserAccountTransactionRecordResponse(userAccountOutlayRequest);
            if (AssertUtils.isNotNull(userAccountTransactionRecordResponse)) {
                userAccountTransactionRecordResponses.add(userAccountTransactionRecordResponse);
            }
        });
        resultObject.setData(userAccountTransactionRecordResponses);
        return resultObject;
    }


    /**
     * 账户初始化
     *
     * @param userId 用户ID
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject initializationAccountByUserId(String userId) {
        ResultObject resultObject = new ResultObject();
        List<UserAccountBo> userAccountBoList = userAccountBaseService.getUserAccountBo(userId);

        if (AssertUtils.isNotEmpty(userAccountBoList)) {
            return resultObject;
        }

        UserAccountPo cashAccountPo = new UserAccountPo();
        cashAccountPo.setUserId(userId);
        cashAccountPo.setUserAccountTypeCode(PartyTermEnum.USER_ACCOUNT_TYPE.CASH_ACCOUNT.name());
        cashAccountPo.setCurrencyCode(TerminologyConfigEnum.CURRENCY.USD.name());
        cashAccountPo.setAccountStatus(PartyConfigEnum.ACCOUNT_STATUS.WAIT_ACTIVITY.name());
        cashAccountPo.setRevenueAmount(new BigDecimal(0));
        cashAccountPo.setExpendedAmount(new BigDecimal(0));
        cashAccountPo.setResidueAmount(new BigDecimal(0));
        cashAccountPo.setUserAccountNo(DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE54));
        userAccountBaseService.saveUserAccount(cashAccountPo, userId);

        UserAccountPo integralAccountPo = new UserAccountPo();
        integralAccountPo.setUserId(userId);
        integralAccountPo.setUserAccountTypeCode(PartyTermEnum.USER_ACCOUNT_TYPE.INTEGRAL_ACCOUNT.name());
        integralAccountPo.setCurrencyCode(TerminologyConfigEnum.CURRENCY.USD.name());
        integralAccountPo.setAccountStatus(PartyConfigEnum.ACCOUNT_STATUS.WAIT_ACTIVITY.name());
        integralAccountPo.setRevenueAmount(new BigDecimal(0));
        integralAccountPo.setExpendedAmount(new BigDecimal(0));
        integralAccountPo.setResidueAmount(new BigDecimal(0));
        integralAccountPo.setUserAccountNo(DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE54));
        userAccountBaseService.saveUserAccount(integralAccountPo, userId);
        return resultObject;
    }

    @Override
    public ResultObject activityAccountByUserId(String userId) {
        ResultObject resultObject = new ResultObject();
        List<UserAccountPo> userAccountBoList = userAccountBaseDao.getUserAccountPo(userId);
        if (!AssertUtils.isNotEmpty(userAccountBoList)) {
            return resultObject;
        }
        userAccountBoList.forEach(userAccountBo -> {
            userAccountBo.setAccountStatus(PartyConfigEnum.ACCOUNT_STATUS.ACTIVITY.name());
        });
        userAccountDao.update(userAccountBoList);
        return resultObject;
    }


    /**
     * 支出状态调整
     *
     * @param userAccountNotifyRequest 业务ID
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject updateAccountStatus(UserAccountNotifyRequest userAccountNotifyRequest) {
        ResultObject resultObject = new ResultObject();
        //非空验证
        userAccountParameterValidate.validateUserAccountNotifyRequest(userAccountNotifyRequest);
        String userId = userAccountNotifyRequest.getUserId();

        UserAccountTransactionRecordPo accountTransactionRecordPo = userAccountBaseService.getAccountTransactionRecordById(userAccountNotifyRequest.getBusinessId(), STREAM_CODE.OUTPUT.name());
        AssertUtils.isNotNull(log, accountTransactionRecordPo, PARTY_ACCOUNT_TRANSACTION_RECORD_IS_NOT_EXIST);

        UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBoById(accountTransactionRecordPo.getUserAccountId());
        AssertUtils.isNotNull(log, userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);

//        if (ACCOUNT_STATUS.FROZEN.name().equals(userAccountBo.getAccountStatus())) {
//            throw new RequestException(PARTY_THE_ACCOUNT_IS_FROZEN_AND_CANNOT_BE_OPERATED);
//        }

        //验证字符串
        String validString = accountTransactionRecordPo.getValidString();

        if (!STREAM_STATUS.PROCESSING.name().equals(accountTransactionRecordPo.getStatus())) {
            throw new RequestException(PARTY_PROCESSING_ACCOUNT_TRANSACTION_RECORD_IS_NOT_EXIST);
        }

        if (userAccountNotifyRequest.getAmount().compareTo(accountTransactionRecordPo.getAmount()) != 0) {
            throw new RequestException(PARTY_INCONSISTENT_AMOUNT_ERROR);
        }

        if (PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(userAccountNotifyRequest.getStatus())) {
            accountTransactionRecordPo.setValidString(StringUtil.getFixLenthString(6));
            accountTransactionRecordPo.setStatus(STREAM_STATUS.SUCCESS.name());
            accountTransactionRecordPo.setDescription(userAccountNotifyRequest.getRemark());
            userAccountBaseService.saveUserAccountTransactionRecord(accountTransactionRecordPo, userId);
        } else {
            //支出失败，复原其金额
            userAccountBo.setExpendedAmount(userAccountBo.getExpendedAmount().subtract(userAccountNotifyRequest.getAmount()));
            userAccountBo.setResidueAmount(userAccountBo.getResidueAmount().add(userAccountNotifyRequest.getAmount()));
            userAccountBaseService.saveUserAccount(userAccountBo, userId);

            accountTransactionRecordPo.setDescription(userAccountNotifyRequest.getRemark());
            accountTransactionRecordPo.setStatus(STREAM_STATUS.FAIL.name());
            userAccountBaseService.saveUserAccountTransactionRecord(accountTransactionRecordPo, userId);
        }
        return resultObject;
    }

    /**
     * 支出状态调整异常回滚
     *
     * @param userAccountRollbackRequest 用户
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject updateAccountStatusRollBack(UserAccountRollbackRequest userAccountRollbackRequest) {
        ResultObject resultObject = new ResultObject();
        userAccountTransfer.saveAccountRollback(userAccountRollbackRequest);
        return resultObject;
    }

    /**
     * 批量支出状态调整异常回滚
     *
     * @param userAccountRollbackRequests 请求参数
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject updateAccountStatusRollBackBatch(List<UserAccountRollbackRequest> userAccountRollbackRequests) {
        ResultObject resultObject = new ResultObject();
        AssertUtils.isNotEmpty(log, userAccountRollbackRequests, PARTY_ACCOUNT_STATUS_ROLL_BACK_IS_NOT_NULL);
        userAccountRollbackRequests.forEach(userAccountRollbackRequest -> userAccountTransfer.saveAccountRollback(userAccountRollbackRequest));
        return resultObject;
    }

    @Override
    public ResultObject updateAccountIncomeRollBackBatch(List<UserAccountRollbackRequest> userAccountRollbackRequests) {
        ResultObject resultObject = new ResultObject();
        AssertUtils.isNotEmpty(log, userAccountRollbackRequests, PARTY_ACCOUNT_STATUS_ROLL_BACK_IS_NOT_NULL);
        userAccountRollbackRequests.forEach(userAccountRollbackRequest -> userAccountTransfer.saveAccountIncomeRollback(userAccountRollbackRequest));
        return resultObject;
    }

    /**
     * 不可用收入处理
     *
     * @param userAccountDisableRequests 不可用收入
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject dealAccountDisable(List<UserAccountDisableRequest> userAccountDisableRequests) {
        ResultObject resultObject = new ResultObject();
        AssertUtils.isNotEmpty(log, userAccountDisableRequests, PARTY_USER_ACCOUNT_DISABLE_IS_NOT_NULL);
        userAccountDisableRequests.forEach(userAccountDisableRequest -> {
            String userId = userAccountDisableRequest.getUserId();
            userAccountParameterValidate.validateUserAccountDisableRequest(userAccountDisableRequest);
            UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBo(userId, userAccountDisableRequest.getUserAccountTypeCode());
            AssertUtils.isNotNull(log, userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);

            //5.2.0 新增黑名单设置，黑名单用户不算账户收入
            UserAccountBlackPo userAccountBlackPo = userAccountBaseService.queryUserAccountBlack(userId, userAccountDisableRequest.getUserAccountTypeCode());
            if (AssertUtils.isNotNull(userAccountBlackPo)) {
                log.info("该用户已设置成黑名单，无法操作账户，userId：{}", userId);
                return;
            }

            //锁定则插入一笔新的数据.
            if (STREAM_STATUS.LOCK.name().equals(userAccountDisableRequest.getStatus())) {
                userAccountTransfer.saveLookAccountDisable(userId, userAccountDisableRequest, userAccountBo);
            }

            //解锁状态的数据先查询是否存在相对应的数据，并判断金额是否一致，如一致才解锁
            if (STREAM_STATUS.UNLOCK.name().equals(userAccountDisableRequest.getStatus())) {
                UserAccountDisableRecordPo accountDisableRecordPo = userAccountBaseService.getAccountDisableRecordById(userAccountDisableRequest.getBizId(), userAccountDisableRequest.getBusinessTypeCode());
                if (AssertUtils.isNotNull(accountDisableRecordPo)) {
                    accountDisableRecordPo.setStatus(STREAM_STATUS.UNLOCK.name());
                    userAccountBaseService.saveUserAccountDisableRecord(accountDisableRecordPo, userId);
                }
            }
        });
        return resultObject;
    }

    /**
     * 根据账户类型获取用户账户信息
     *
     * @param userAccountTypeCode 用户账户类型
     * @param userId              用户
     * @return ResultObject
     */
    @Override
    public ResultObject<UserAccountResponse> userAccountGet(String userAccountTypeCode, String userId) {
        ResultObject<UserAccountResponse> resultObject = new ResultObject<>();
        UserAccountResponse userAccountResponse = new UserAccountResponse();
        AssertUtils.isNotEmpty(log, userAccountTypeCode, PARTY_USER_ACCOUNT_TYPE_CODE_IS_NOT_NULL);
        UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBo(userId, userAccountTypeCode);
        AssertUtils.isNotNull(log, userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);

        ClazzUtils.copyPropertiesIgnoreNull(userAccountBo, userAccountResponse);
        userAccountResponse.setTotalAmount(userAccountBo.getRevenueAmount());
        userAccountResponse.setUseAmount(userAccountBo.getExpendedAmount());
        userAccountResponse.setResidueAmount(userAccountBo.getResidueAmount());
        userAccountResponse.setFrozenAmount(userAccountTransfer.getDisableAmount(userAccountBo));
        userAccountResponse.setExtractAmount(userAccountTransfer.getExtractAmount(userAccountBo));
        BigDecimal cashWithdrawalAmount = userAccountBo.getResidueAmount().subtract(userAccountResponse.getFrozenAmount());
        if (cashWithdrawalAmount.compareTo(BigDecimal.ZERO) < 0) {
            cashWithdrawalAmount = BigDecimal.ZERO;
        }
        userAccountResponse.setCashWithdrawalAmount(cashWithdrawalAmount);
        resultObject.setData(userAccountResponse);
        return resultObject;
    }

    /**
     * 根据用户ID、账户类型、流水类型获取用户账户信息
     *
     * @param userId              用户
     * @param userAccountTypeCode 账户类型编码
     * @param streamCode          流水类型
     * @param currentPage         当前页面
     * @param pageSize            页面大小
     * @return UserAccountDetailResponse
     */
    @Override
    public ResultObject<List<UserAccountDetailResponse>> userAccountDetailGet(String userId, String userAccountTypeCode, String streamCode, Integer currentPage, Integer pageSize) {
        ResultObject<List<UserAccountDetailResponse>> resultObject = new ResultObject<>();
        AccountDetailVo accountDetailReqFc = new AccountDetailVo();
        accountDetailReqFc.setUserAccountTypeCode(userAccountTypeCode);
        accountDetailReqFc.setStreamCode(streamCode);
        if (AssertUtils.isNotNull(currentPage)) {
            accountDetailReqFc.setCurrentPage(currentPage);
        }
        if (AssertUtils.isNotNull(pageSize)) {
            accountDetailReqFc.setPageSize(pageSize);
        }

        //参数验证
        AssertUtils.isNotEmpty(log, userAccountTypeCode, PARTY_USER_ACCOUNT_TYPE_CODE_IS_NOT_NULL);

        //查询账户信息
        List<UserAccountTransactionRecordPo> userAccountTransactionRecordPos = userAccountBaseDao.loadAccountDetail(userId, accountDetailReqFc);
        //数据转换
        List<UserAccountDetailResponse> userAccountDetailResponses = (List<UserAccountDetailResponse>) this.converterList(userAccountTransactionRecordPos, new TypeToken<List<UserAccountDetailResponse>>() {
        }.getType());

        resultObject.setData(userAccountDetailResponses);
        return resultObject;
    }

    /**
     * 新增提现记录
     *
     * @param cashWithdrawalRequest 提现参数
     * @return CashWithdrawalResponse
     */
    @Override
    @Transactional
    public ResultObject<CashWithdrawalResponse> postCashWithdrawal(CashWithdrawalRequest cashWithdrawalRequest) {
        ResultObject<CashWithdrawalResponse> resultObject = new ResultObject<>();
        userAccountParameterValidate.validateCashWithdrawalRequest(cashWithdrawalRequest);
        CashWithdrawalRecordPo cashWithdrawalRecordPo = new CashWithdrawalRecordPo();
        ClazzUtils.copyPropertiesIgnoreNull(cashWithdrawalRequest, cashWithdrawalRecordPo);
        cashWithdrawalRecordPo.setCashWithdrawalDate(AssertUtils.isNotNull(cashWithdrawalRequest.getCashWithdrawalDate()) ? cashWithdrawalRequest.getCashWithdrawalDate() : DateUtils.getCurrentTime());
        cashWithdrawalRecordPo.setStatus(WITHDRAW_STATUS.FIANCE_PROCESS.name());
        userAccountBaseService.saveCashWithdrawal(cashWithdrawalRecordPo, cashWithdrawalRecordPo.getUserId());

        //返回数据
        CashWithdrawalResponse cashWithdrawalResponse = new CashWithdrawalResponse();
        ClazzUtils.copyPropertiesIgnoreNull(cashWithdrawalRecordPo, cashWithdrawalResponse);
        resultObject.setData(cashWithdrawalResponse);
        return resultObject;
    }

    /**
     * 提现成功回调
     *
     * @param userAccountNotifyRequest 回调参数
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postCashWithdrawalNotify(UserAccountNotifyRequest userAccountNotifyRequest) {
        log.info("提现成功回调,userAccountNotifyRequest:{}", JackSonUtils.toJson(userAccountNotifyRequest));
        ResultObject resultObject = new ResultObject();
        String status;
        CashWithdrawalRecordPo cashWithdrawalRecordPo = userAccountBaseService.getCashWithdrawalRecordPoById(userAccountNotifyRequest.getBusinessId());
        AssertUtils.isNotNull(log, cashWithdrawalRecordPo, PARTY_ACCOUNT_TRANSACTION_RECORD_IS_NOT_EXIST);

        ResultObject updateAccountStatusResultObject = this.updateAccountStatus(userAccountNotifyRequest);
        AssertUtils.isResultObjectError(log, updateAccountStatusResultObject);
        if (PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(userAccountNotifyRequest.getStatus())) {
            status = WITHDRAW_STATUS.SUCCESS.name();
        } else {
            status = WITHDRAW_STATUS.FAIL.name();
        }
        cashWithdrawalRecordPo.setStatus(status);
        userAccountBaseService.saveCashWithdrawal(cashWithdrawalRecordPo, userAccountNotifyRequest.getUserId());
        return resultObject;
    }

    /**
     * 提现异常回滚
     *
     * @param userAccountRollbackRequest 回滚参数
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject updateAccountWithdrawRollBack(UserAccountRollbackRequest userAccountRollbackRequest) {
        ResultObject resultObject = new ResultObject();
        CashWithdrawalRecordPo cashWithdrawalRecordPo = userAccountBaseService.getCashWithdrawalRecordPoById(userAccountRollbackRequest.getBizId());
        AssertUtils.isNotNull(log, cashWithdrawalRecordPo, PARTY_ACCOUNT_TRANSACTION_RECORD_IS_NOT_EXIST);
        cashWithdrawalRecordPo.setStatus(WITHDRAW_STATUS.FAIL.name());
        userAccountBaseService.saveCashWithdrawal(cashWithdrawalRecordPo, userAccountRollbackRequest.getUserId());

        userAccountTransfer.saveAccountRollback(userAccountRollbackRequest);
        return resultObject;
    }

    /**
     * 发起批量提现
     *
     * @param users users
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postCashWithdrawalBatch(Users users) {
        ResultObject resultObject = new ResultObject();
        List<String> receiptIds = new ArrayList<>();
        try {
            //1.筛选所有可提现金额大于0的用户
            List<AccountCashWithdrawalBatchBo> cashWithdrawalBatch = userAccountBaseService.getCashWithdrawalBatch();
            if (!AssertUtils.isNotEmpty(cashWithdrawalBatch)) {
                log.info("本次批量提现无可提现数据");
                return resultObject;
            }

            List<String> userIds = cashWithdrawalBatch.stream().map(AccountCashWithdrawalBatchBo::getUserId).distinct().collect(Collectors.toList());

            //2.查询用户所配置的银行帐号
            ResultObject<List<AccountResponse>> listResultObject = platformAccountApi.accountPost(userIds);
            if (AssertUtils.isResultObjectListDataNull(listResultObject)) {
                return resultObject;
            }
            //3.是否配置了iCBC或ABA的银行帐号
            AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
            applyAgentRequest.setListAgentId(userIds);
            List<AgentResponse> agentResponses = agentApi.agentsGet(applyAgentRequest).getData();

            cashWithdrawalBatch.forEach(accountCashWithdrawalBatchBo -> {
                List<AccountResponse> accountResponses = listResultObject.getData().stream().filter(accountResponse ->
                        accountResponse.getUserId().equals(accountCashWithdrawalBatchBo.getUserId())
                                && "AUDIT_PASS".equals(accountResponse.getAuditStatus())
                ).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(accountResponses)) {
                    //4.两个银行都配置了则提现至ICBC 和 ABA 其中的默认银行
                    List<String> ICBC_ABA = Arrays.asList("ICBC","ABA BANK");
                    Optional<AccountResponse> matchICBC = accountResponses.stream().filter(accountResponse ->
                            ICBC_ABA.contains(accountResponse.getBankCode()) && "MAIN".equals(accountResponse.getPrimaryFlag())
                    ).findAny();
                    if (matchICBC.isPresent()) {
                        AccountResponse accountResponse = matchICBC.get();
                        accountCashWithdrawalBatchBo.setAccountId(accountResponse.getAccountId());
                        accountCashWithdrawalBatchBo.setBankCode(accountResponse.getBankCode());
                        accountCashWithdrawalBatchBo.setAccountNo(accountResponse.getAccountNo());
                        accountCashWithdrawalBatchBo.setAccountOwner(accountResponse.getAccountOwner());
                        accountCashWithdrawalBatchBo.setAccountTypeCode(accountResponse.getAccountTypeCode());
                    }
                }
                agentResponses.stream().filter(agentResponse -> accountCashWithdrawalBatchBo.getUserId().equals(agentResponse.getUserId()))
                        .findFirst().ifPresent(agentResponse -> accountCashWithdrawalBatchBo.setBranchId(agentResponse.getBranchId()));
            });

            //4.移除掉没有账户ID的数据
            cashWithdrawalBatch.removeIf(accountCashWithdrawalBatchBo -> !AssertUtils.isNotEmpty(accountCashWithdrawalBatchBo.getAccountId()));
            if (!AssertUtils.isNotEmpty(cashWithdrawalBatch)) {
                log.info("本次批量提现无可提现AccountId");
                return resultObject;
            }

            //5.发起批量提现
            List<CashWithdrawalRecordPo> cashWithdrawalRecordPos = this.saveCashWithdrawalBatch(cashWithdrawalBatch, users.getUserId());

            //6.设置批量提现的业务ID
            cashWithdrawalBatch.forEach(accountCashWithdrawalBatchBo -> {
                cashWithdrawalRecordPos.stream().filter(cashWithdrawalRecordPo -> cashWithdrawalRecordPo.getUserId().equals(accountCashWithdrawalBatchBo.getUserId())
                ).findFirst().ifPresent(cashWithdrawalRecordPo -> accountCashWithdrawalBatchBo.setBizId(cashWithdrawalRecordPo.getCwrId()));
            });

            //7.批量保存账户支出
            List<UserAccountOutlayRequest> userAccountOutlayRequests = new ArrayList<>();
            cashWithdrawalBatch.forEach(accountCashWithdrawalBatchBo -> {
                UserAccountOutlayRequest userAccountOutlayRequest = new UserAccountOutlayRequest();
                userAccountOutlayRequest.setUserAccountTypeCode(PartyTermEnum.USER_ACCOUNT_TYPE.CASH_ACCOUNT.name());
                userAccountOutlayRequest.setAmount(accountCashWithdrawalBatchBo.getCashWithdrawalAmount());
                if (AssertUtils.isNotEmpty(accountCashWithdrawalBatchBo.getAccountId())) {
                    userAccountOutlayRequest.setBankAccountId(accountCashWithdrawalBatchBo.getAccountId());
                }
                userAccountOutlayRequest.setBusinessTypeCode(BUSINESS_TYPE_CODE.WITHDRAW.name());
                userAccountOutlayRequest.setUserId(accountCashWithdrawalBatchBo.getUserId());
                userAccountOutlayRequest.setWithdrawalMethod("TRANSFER");
                userAccountOutlayRequest.setBizId(accountCashWithdrawalBatchBo.getBizId());
                userAccountOutlayRequests.add(userAccountOutlayRequest);
            });
            userAccountOutlayRequests.forEach(userAccountOutlayRequest -> userAccountTransfer.getUserAccountTransactionRecordResponse(userAccountOutlayRequest));

            //8.支付通知接口需要配置化,回调接口路径
            ResultObject<NotifyConfigResponse> notifyConfig = platformConfigApi.getNotifyConfig(PAYMENT_BUSINESS_TYPE.AGENT_WITHDRAW.name());
            AssertUtils.isResultObjectError(log, notifyConfig);

            //9.发起付费申请
            cashWithdrawalBatch.forEach(accountCashWithdrawalBatchBo -> {
                ReceiptRequest receiptRequest = this.transApplyExtract(accountCashWithdrawalBatchBo, notifyConfig.getData());
                ResultObject<ReceiptResponse> receiptResultObject = receiptBaseApi.applyReceipt(receiptRequest);
                AssertUtils.isResultObjectError(log, receiptResultObject);
                receiptIds.add(receiptResultObject.getData().getReceiptId());
            });
        } catch (Exception e) {
            if (AssertUtils.isNotEmpty(receiptIds)) {
                ResultObject deletetPaymentReceipt = receiptBaseApi.deletetPaymentReceipt(receiptIds);
                log.info("异常撤销付费记录,关闭订单{}", JSONObject.toJSONString(deletetPaymentReceipt));
            }
            throwsException(log, APP_WITHDRAW_ERROR);
        }
        return resultObject;
    }

    private List<CashWithdrawalRecordPo> saveCashWithdrawalBatch(List<AccountCashWithdrawalBatchBo> cashWithdrawalBatch, String userId) {
        List<CashWithdrawalRecordPo> cashWithdrawalRecordPos = new ArrayList<>();
        cashWithdrawalBatch.forEach(cashWithdrawalBatchBo -> {
            AssertUtils.isNotEmpty(log, cashWithdrawalBatchBo.getUserId(), PARTY_USER_ID_NOT_NULL);
            AssertUtils.isNotNull(log, cashWithdrawalBatchBo.getCashWithdrawalAmount(), PARTY_USER_ACCOUNT_AMOUNT_IS_NOT_NULL);
            if (cashWithdrawalBatchBo.getCashWithdrawalAmount().compareTo(BigDecimal.ZERO) < 0) {
                throw new RequestException(PARTY_THE_AMOUNT_CANNOT_BE_NEGATIVE);
            }
            CashWithdrawalRecordPo cashWithdrawalRecordPo = new CashWithdrawalRecordPo();
            cashWithdrawalRecordPo.setUserId(cashWithdrawalBatchBo.getUserId());
            cashWithdrawalRecordPo.setCashWithdrawalDate(DateUtils.getCurrentTime());
            cashWithdrawalRecordPo.setStatus(WITHDRAW_STATUS.FIANCE_PROCESS.name());
            cashWithdrawalRecordPo.setAmount(cashWithdrawalBatchBo.getCashWithdrawalAmount());
            cashWithdrawalRecordPos.add(cashWithdrawalRecordPo);
        });
        userAccountBaseService.saveCashWithdrawalBatch(cashWithdrawalRecordPos, userId);
        return cashWithdrawalRecordPos;
    }

    public ReceiptRequest transApplyExtract(AccountCashWithdrawalBatchBo accountCashWithdrawalBatchBo, NotifyConfigResponse NotifyConfigResponse) {
        ReceiptRequest receiptRequest = new ReceiptRequest();
        receiptRequest.setBranchId(accountCashWithdrawalBatchBo.getBranchId());
        receiptRequest.setBusinessId(accountCashWithdrawalBatchBo.getBizId());
        receiptRequest.setBusinessNo(accountCashWithdrawalBatchBo.getBizId());
        receiptRequest.setBusinessType(PAYMENT_BUSINESS_TYPE.AGENT_WITHDRAW.name());
        receiptRequest.setCurrency(TerminologyConfigEnum.CURRENCY.USD.name());
        receiptRequest.setDeviceChannelId("gclife_agent_app");
        receiptRequest.setDuePayAmount(accountCashWithdrawalBatchBo.getCashWithdrawalAmount());
        receiptRequest.setUserId(accountCashWithdrawalBatchBo.getUserId());
        receiptRequest.setWithdrawalMethod("TRANSFER");
        receiptRequest.setPaymentHandler(NotifyConfigResponse.getNotifyUrl());
        //付款详情
        List<ReceiptItemRequest> receiptItemReqFcs = new ArrayList<>();
        ReceiptItemRequest receiptItemReqFc = new ReceiptItemRequest();
        receiptItemReqFc.setDuePayAmount(accountCashWithdrawalBatchBo.getCashWithdrawalAmount());
        receiptItemReqFc.setPaymentMethodCode(PAYMENT_METHODS.BANK_DEDUCT.name());
        receiptItemReqFc.setPaymentTypeCode(PAYMENT_TYPE.ACTUAL.name());

        //账号信息
        Map<String, String> map = new HashMap<>();
        map.put("bankCode", accountCashWithdrawalBatchBo.getBankCode());
        map.put("bankAccountNo", accountCashWithdrawalBatchBo.getAccountNo());
        map.put("bankAccountName", accountCashWithdrawalBatchBo.getAccountOwner());
        map.put("bankAccountType", accountCashWithdrawalBatchBo.getAccountTypeCode());
        receiptItemReqFc.setPaymentParam(JSON.toJSONString(map));
        receiptItemReqFcs.add(receiptItemReqFc);
        receiptRequest.setItems(receiptItemReqFcs);
        log.info("申请提现所需参数:{}", JSON.toJSONString(receiptRequest));
        return receiptRequest;
    }
}

package com.gclife.party.service.visit.impl;

import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.TerminologyTypeEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.*;
import com.gclife.endorse.api.EndorseAcceptApi;
import com.gclife.endorse.model.response.AcceptApplyResponse;
import com.gclife.party.api.ReturnVisitApi;
import com.gclife.party.core.jooq.tables.daos.ReturnVisitAttachmentDao;
import com.gclife.party.core.jooq.tables.daos.ReturnVisitChangeAttachmentDao;
import com.gclife.party.core.jooq.tables.daos.ReturnVisitChangeDao;
import com.gclife.party.core.jooq.tables.pojos.*;
import com.gclife.party.dao.CustomerBaseDao;
import com.gclife.party.dao.ReturnVisitAttachmentBaseDao;
import com.gclife.party.dao.ReturnVisitChangeAttachmentBaseDao;
import com.gclife.party.dao.ReturnVisitExtDao;
import com.gclife.party.model.bo.ReturnVisitBo;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.ReturnVisitNotifyRequest;
import com.gclife.party.model.request.visit.*;
import com.gclife.party.model.response.visit.*;
import com.gclife.party.service.MessageBusinessService;
import com.gclife.party.service.base.ReturnVisitBaseService;
import com.gclife.party.service.visit.PolicyReturnVisitBusinessService;
import com.gclife.party.transform.LanguageUtils;
import com.gclife.platform.api.*;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.platform.model.response.UserInfoResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyResponse;
import com.gclife.policy.model.vo.PolicyAgentVo;
import com.gclife.policy.model.vo.PolicyCoverageVo;
import com.gclife.renewal.api.GroupRenewalApi;
import com.gclife.renewal.model.response.GroupRenewalReturnVisistResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.ONLINE_POLICY;
import static com.gclife.party.model.config.PartyTermEnum.RETURN_VISIT_STATISTICS_TYPE.*;
import static com.gclife.party.model.config.PartyTermEnum.RETURN_VISIT_TYPE.*;

/**
 * @Auther: chenjinrong
 * @Date: 19-10-21 11:10
 * @Description:
 */
@Service
@Slf4j
public class PolicyReturnVisitBusinessServiceImpl extends BaseBusinessServiceImpl implements PolicyReturnVisitBusinessService {

    @Autowired
    PolicyApi policyApi;

    @Autowired
    AgentApi agentApi;

    @Autowired
    PlatformBranchApi platformBranchApi;

    @Autowired
    PlatformAreaApi platformAreaApi;
    @Autowired
    EndorseAcceptApi endorseAcceptApi;
    @Autowired
    ReturnVisitBaseService returnVisitBaseService;

    @Autowired
    PlatformBaseInternationServiceApi platformBaseInternationServiceApi;

    @Autowired
    ReturnVisitExtDao returnVisitExtDao;

    @Autowired
    ReturnVisitAttachmentDao returnVisitAttachmentDao;
    @Autowired
    PlatformEmployeeApi platformEmployeeApi;
    @Autowired
    PlatformUsersApi platformUsersApi;
    @Autowired
    ReturnVisitChangeDao returnVisitChangeDao;
    @Autowired
    ReturnVisitChangeAttachmentDao returnVisitChangeAttachmentDao;
    @Autowired
    ReturnVisitAttachmentBaseDao returnVisitAttachmentBaseDao;
    @Autowired
    ReturnVisitChangeAttachmentBaseDao returnVisitChangeAttachmentBaseDao;
    @Autowired
    ReturnVisitApi returnVisitApi;
    @Autowired
    CustomerBaseDao customerBaseDao;
    @Autowired
    AttachmentApi attachmentApi;
    @Autowired
    GroupRenewalApi groupRenewalApi;
    @Autowired
    PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    MessageBusinessService messageBusinessService;


    @Override
    public ResultObject<ReturnVisitAddResponse> addPolicyReturnVisit(Users users, PolicyReturnVisitAddRequest returnVisitSubmitRequest) {
        ResultObject resultObject = ResultObject.success();
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getBusinessId(), PartyErrorConfigEnum.PARTY_PARAMETER_BUSINESS_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getBusinessType(), PartyErrorConfigEnum.PARTY_PARAMETER_BUSINESS_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getReturnVisitType(), PartyErrorConfigEnum.PARTY_PARAMETER_RETURN_VISIT_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getCustomId(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), returnVisitSubmitRequest.getPolicyNo(), PartyErrorConfigEnum.PARTY_PARAMETER_POLICY_NO_IS_NOT_NULL);
        log.info("businessid:{},request:{}", returnVisitSubmitRequest.getBusinessId(), JackSonUtils.toJson(returnVisitSubmitRequest));
        if (!AssertUtils.isNotNull(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.fromCode(returnVisitSubmitRequest.getBusinessType()))) {
            throwsException(PartyErrorConfigEnum.PARTY_RETURN_BUSINESS_TYPE_NOT_EXIST);
        }
        if (!AssertUtils.isNotNull(PartyTermEnum.RETURN_VISIT_TYPE.fromCode(returnVisitSubmitRequest.getReturnVisitType()))) {
            throwsException(PartyErrorConfigEnum.PARTY_RETURN_RETURN_VISIT_TYPE_NOT_EXIST);
        }

        ReturnVisitPo returnVisitPo = new ReturnVisitPo();
        returnVisitPo.setBusinessId(returnVisitSubmitRequest.getBusinessId());
        returnVisitPo.setBusinessNo(returnVisitSubmitRequest.getPolicyNo());
        returnVisitPo.setReturnVisitType(returnVisitSubmitRequest.getReturnVisitType());
        returnVisitPo.setReturnVisitStatus(PartyTermEnum.RETURN_VISIT_STATUS.NO_RETURN_VISIT.name());
        returnVisitPo.setBusinessType(returnVisitSubmitRequest.getBusinessType());
        returnVisitPo.setCustomerId(returnVisitSubmitRequest.getCustomId());
        returnVisitPo.setReturnVisitService(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.fromCode(returnVisitSubmitRequest.getBusinessType()).getServiceName());
        if (!AssertUtils.isNotEmpty(returnVisitSubmitRequest.getApplyNo())) {
            String policyId = "";
            if (PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.POLICY.name().equalsIgnoreCase(returnVisitSubmitRequest.getBusinessType())) {
                if (GROUP_RENEWAL.name().equalsIgnoreCase(returnVisitSubmitRequest.getReturnVisitType())) {
                    ResultObject<GroupRenewalReturnVisistResponse> groupReturnVisitResultObject = groupRenewalApi.getGroupRenewalReturnVisitInfo(returnVisitSubmitRequest.getBusinessId());
                    policyId = groupReturnVisitResultObject.getData().getPolicyId();
                } else {
                    policyId = returnVisitSubmitRequest.getBusinessId();
                }
            } else if (PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.ENDORSE.name().equalsIgnoreCase(returnVisitSubmitRequest.getBusinessType())) {
                ResultObject<AcceptApplyResponse> acceptApplyResponseResultObject = endorseAcceptApi.queryApplyInfo(returnVisitSubmitRequest.getBusinessId());
                if (!AssertUtils.isResultObjectDataNull(acceptApplyResponseResultObject)) {
                    policyId = acceptApplyResponseResultObject.getData().getApplyId();
                }

            }
            ResultObject<PolicyResponse> policyResponseResultObject = policyApi.queryOnePolicy(policyId);
            if (!AssertUtils.isResultObjectDataNull(policyResponseResultObject)) {
                returnVisitPo.setApplyNo(policyResponseResultObject.getData().getApplyNo());
            }
        } else {
            returnVisitPo.setApplyNo(returnVisitSubmitRequest.getApplyNo());
        }
        returnVisitPo.setMustReturnFlag(returnVisitSubmitRequest.getMustReturnFlag());
        returnVisitBaseService.saveReturnVisitPo(users.getUserId(), returnVisitPo);

        //异步发送消息
        new Thread(() -> this.sendVisitMessage(returnVisitPo)).start();

        ReturnVisitAddResponse addResponse = new ReturnVisitAddResponse();
        ReflectionUtils.copyProperties(addResponse, returnVisitPo);
        resultObject.setData(addResponse);
        return resultObject;
    }

    private void sendVisitMessage(ReturnVisitPo returnVisitPo) {
        try {
            if (NEW_POLICY.name().equals(returnVisitPo.getReturnVisitType())
                    || PartyTermEnum.RETURN_VISIT_TYPE.GROUP_ADD_ADDITIONAL.name().equals(returnVisitPo.getReturnVisitType())
                    || PartyTermEnum.RETURN_VISIT_TYPE.ADD_SUBTRACT_INSURED.name().equals(returnVisitPo.getReturnVisitType())
                    || GROUP_RENEWAL.name().equals(returnVisitPo.getReturnVisitType())
            ) {
                log.info("开始发送客户回访消息");
                String serviceUrl = returnVisitPo.getReturnVisitService();
                URI uri = URI.create(serviceUrl);
                //新单承保直接查询不到
                Thread.sleep(20 * 1000);
                ResultObject resultObjectRefc = returnVisitApi.getReturnVisitDetail(uri, returnVisitPo.getBusinessId(), returnVisitPo.getReturnVisitType());
                AssertUtils.isResultObjectError(this.getLogger(), resultObjectRefc);
                JSONObject jsonObject = JSONObject.parseObject(JackSonUtils.objectToJsonStr(resultObjectRefc.getData()));
                String applicantName = "";
                if (returnVisitPo.getBusinessType().equalsIgnoreCase(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.POLICY.name())) {
                    String policyType = jsonObject.getString("policyType");
                    if (PartyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name().equalsIgnoreCase(policyType)) {
                        applicantName = jsonObject.getString("applicantName");
                    } else {
                        applicantName = jsonObject.getString("companyName");
                    }
                } else {
                    applicantName = jsonObject.getString("companyName");
                }
                //ResultObject<SyscodeResponse> accountBusinessTypeResultObject = platformInternationalBaseApi.queryOneInternational("RETURN_VISIT_TYPE", returnVisitPo.getReturnVisitType(), TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
                Map messageParamMap = new HashMap<>();
                //TODO 需要加上 注意：其中团险保单投保人取单位名称
                messageParamMap.put("applicantName", applicantName);
                messageParamMap.put("policyNo", returnVisitPo.getBusinessNo());
                //传入国际化的条件key和type，到消息微服务那边根据消息微服务发送给内部人员的语言进行国际化取值
                Map<String,String> hashMap = new HashMap();
                hashMap.put("key",returnVisitPo.getReturnVisitType());
                hashMap.put("type","RETURN_VISIT_TYPE");
                //messageParamMap.put("returnVisitType", AssertUtils.isResultObjectDataNull(accountBusinessTypeResultObject) ? returnVisitPo.getReturnVisitType() : accountBusinessTypeResultObject.getData().getCodeName());
                messageParamMap.put("returnVisitType",hashMap);
                messageParamMap.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE6));
                String saleBranchId = jsonObject.getString("salesBranchId");
                List<String> userIds = platformUsersApi.getBusinessUsers(PartyTermEnum.MSG_BUSINESS_TYPE.RETURN_VISIT_MESSAGE_REMINDER.name(), saleBranchId, null).getData();
                log.info("客户回访消息参数:{}", JackSonUtils.toJson(messageParamMap));
                messageBusinessService.pushBusinessMessage(PartyTermEnum.MSG_BUSINESS_TYPE.RETURN_VISIT_MESSAGE_REMINDER.name(), userIds, messageParamMap);
            }

        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            log.info("发送客户回访消息失败");
        }
    }

    @Override
    public ResultObject getPolicyReturnVisitList(ReturnVisitPageRequest pageRequest) {
        ResultObject<BasePageResponse<PolicyReturnVisitResponse>> resultObject = ResultObject.success();
        List<PolicyReturnVisitResponse> policyReturnVisitResponseList = new ArrayList<>();
        List<ReturnVisitBo> returnVisitBoList = returnVisitExtDao.queryReturnVisitList(pageRequest);
        if (AssertUtils.isNotEmpty(returnVisitBoList)) {
            returnVisitBoList.stream().forEach(returnVisitBo -> {
                log.info("--保单回访ID：{}", returnVisitBo.getReturnVisitId());
                //保全的话从保全获取保全申请信息
                String policyId = getPolicyIdByReturnVisit(returnVisitBo);
                PolicyReturnVisitResponse policyReturnVisitResponse = new PolicyReturnVisitResponse();

                if (ONLINE_POLICY.name().equals(returnVisitBo.getBusinessType())) {
                    policyReturnVisitResponse.setCustomerName(returnVisitBo.getApplyName());
                    policyReturnVisitResponse.setCustomerMobile(returnVisitBo.getOnlineMobile());
                    policyReturnVisitResponse.setPhone(returnVisitBo.getOnlineMobile());
                } else {
                    //销售机构
                    ResultObject<PolicyAgentVo> policyAgentVoResultObject = policyApi.queryOnePolicyAgent(policyId);
                    log.info("--保单代理人基本信息：{}", JackSonUtils.toJson(policyAgentVoResultObject));
                    if (!AssertUtils.isResultObjectDataNull(policyAgentVoResultObject)) {
                        //根据代理人code查询代理人信息
                        ResultObject<AgentResponse> agentResponseResultObject = agentApi.agentByIdGet(policyAgentVoResultObject.getData().getAgentId());
                        ReflectionUtils.copyProperties(policyReturnVisitResponse, agentResponseResultObject.getData());
                        log.info("--代理人详情{}", JackSonUtils.toJson(agentResponseResultObject));
                    }
                    policyReturnVisitResponse.setCreatedDate(returnVisitBo.getCreatedDate());
                    ResultObject<PolicyResponse> policyResponseResultObject = policyApi.queryOnePolicy(policyId);
                    if (!AssertUtils.isResultObjectDataNull(policyResponseResultObject)) {
                        PolicyResponse policyResponse = policyResponseResultObject.getData();
                        policyReturnVisitResponse.setPolicyNo(policyResponse.getPolicyNo());
                        policyReturnVisitResponse.setApplyNo(policyResponse.getApplyNo());
                        policyReturnVisitResponse.setApproveDate(policyResponse.getApproveDate());
                        policyReturnVisitResponse.setPolicyType(policyResponse.getPolicyType());

                        if (PartyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name().equalsIgnoreCase(policyResponse.getPolicyType())) {
                            CustomerAgentPo customerAgentPo = returnVisitBo.getCustomerAgentPo();
                            if (AssertUtils.isNotNull(customerAgentPo)) {
                                policyReturnVisitResponse.setPhone(customerAgentPo.getMobile());
                                policyReturnVisitResponse.setCustomerIdNo(customerAgentPo.getIdNo());
                                policyReturnVisitResponse.setCustomerIdTypeCode(customerAgentPo.getIdType());
                                policyReturnVisitResponse.setCustomerName(customerAgentPo.getName());
                                policyReturnVisitResponse.setCustomerMobile(customerAgentPo.getMobile());
                            }

                        } else if (PartyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name().equalsIgnoreCase(policyResponse.getPolicyType())) {
                            CustomerAgentPo customerAgentPo = returnVisitBo.getCustomerAgentPo();
                            if (AssertUtils.isNotNull(customerAgentPo)) {
                                policyReturnVisitResponse.setPhone(customerAgentPo.getDelegateMobile());
                                policyReturnVisitResponse.setCustomerIdNo(customerAgentPo.getDelegateIdNo());
                                policyReturnVisitResponse.setCustomerIdTypeCode(customerAgentPo.getDelegateIdType());
                                policyReturnVisitResponse.setCustomerName(customerAgentPo.getDelegateName());
                                policyReturnVisitResponse.setCustomerMobile(customerAgentPo.getDelegateMobile());
                            }
                        }
                    }
                    log.info("--保单基本信息:" + JackSonUtils.toJson(policyResponseResultObject));
                    ResultObject<PolicyCoverageVo> policyCoverageVoResultObject = policyApi.queryOneMainPolicyCoverage(policyId);
                    if (!AssertUtils.isResultObjectDataNull(policyCoverageVoResultObject)) {
                        policyReturnVisitResponse.setProductName(policyCoverageVoResultObject.getData().getProductName());
                    }
                    UserInfoResponse userResponse = platformUsersApi.userInfoGet(returnVisitBo.getReturnVisitUserId()).getData();
                    if (AssertUtils.isNotNull(userResponse)) {
                        policyReturnVisitResponse.setReturnVisitUserId(returnVisitBo.getReturnVisitUserId());
                        policyReturnVisitResponse.setReturnVisitUserName(userResponse.getName());
                    }
                }
                    //回访信息
                    policyReturnVisitResponse.setReturnVisitStatus(returnVisitBo.getReturnVisitStatus());
                    policyReturnVisitResponse.setReturnVisitStatusName(PartyTermEnum.RETURN_VISIT_STATUS.fromCode(returnVisitBo.getReturnVisitStatus()).desc());
                    policyReturnVisitResponse.setReturnVisitType(returnVisitBo.getReturnVisitType());
                    policyReturnVisitResponse.setReturnVisitId(returnVisitBo.getReturnVisitId());
                    policyReturnVisitResponse.setBusinessTypeName(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.fromCode(returnVisitBo.getBusinessType()).desc());
                    policyReturnVisitResponse.setBusinessType(returnVisitBo.getBusinessType());
                    policyReturnVisitResponse.setBusinessId(returnVisitBo.getBusinessId());
                    policyReturnVisitResponse.setReturnVisitDate(returnVisitBo.getUpdatedDate());
                    policyReturnVisitResponse.setMustReturnFlag(returnVisitBo.getMustReturnFlag());
                    policyReturnVisitResponseList.add(policyReturnVisitResponse);

            });
        }
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(returnVisitBoList) ? returnVisitBoList.get(0).getTotalLine() : null;

        BasePageResponse basePageResponse = BasePageResponse.getData(pageRequest.getCurrentPage(), pageRequest.getPageSize(), totalLine, policyReturnVisitResponseList);
        resultObject.setData(basePageResponse);
        System.out.println(JackSonUtils.toJson(returnVisitBoList));
        return resultObject;
    }

    /**
     * 获取保单回访明细详情
     *
     * @param returnVisitId
     * @return
     */
    @Override
    public ResultObject getPolicyReturnVisitDetail(String returnVisitId, String returnVisitChangeId) {
        ResultObject<PolicyReturnVisitDetailResponse> resultObject = ResultObject.success();
        ReturnVisitPo returnVisitPo = returnVisitBaseService.getByReturnVisitId(returnVisitId);
        AssertUtils.isNotNull(this.getLogger(), returnVisitPo, PartyErrorConfigEnum.PARTY_RETURN_VISIT_ID_NOT_NULL);
        PolicyReturnVisitDetailResponse returnVisitDetailResponse = new PolicyReturnVisitDetailResponse();
        //获取各子系统的详情信息（目前只有保单和保全）
        String serviceUrl = returnVisitPo.getReturnVisitService();
        URI uri = URI.create(serviceUrl);
        ResultObject<Object> resultObjectRefc = returnVisitApi.getReturnVisitDetail(uri, returnVisitPo.getBusinessId(), returnVisitPo.getReturnVisitType());
        AssertUtils.isResultObjectError(this.getLogger(), resultObjectRefc);
        returnVisitDetailResponse.setBasicDetailResponse(resultObjectRefc.getData());

        //回访详情
        ReturnVisitResponse returnVisitResponse = new ReturnVisitResponse();
        returnVisitResponse.setReturnVisitId(returnVisitPo.getReturnVisitId());
        ReflectionUtils.copyProperties(returnVisitResponse, returnVisitPo);

        //回访修改信息
        if (AssertUtils.isNotNull(returnVisitChangeId)) {
            ReturnVisitChangePo returnVisitChangePo = returnVisitBaseService.getReturnVisitChangeById(returnVisitChangeId);
            if (AssertUtils.isNotNull(returnVisitChangePo)) {
                ReturnVisitChangeRespone changeRespone = new ReturnVisitChangeRespone();
                changeRespone.setChangeRemark(returnVisitChangePo.getChangeReamrk());
                returnVisitDetailResponse.setChangeRespone(changeRespone);
                returnVisitResponse.setReturnVisitChangeId(returnVisitChangeId);
            }
        }

        //保全的话从保全获取保全申请信息
        String policyId = "";
        if (PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.POLICY.name().equalsIgnoreCase(returnVisitPo.getBusinessType())) {
            policyId = returnVisitPo.getBusinessId();
            ResultObject<PolicyResponse> policyResponseResultObject = policyApi.queryOnePolicy(policyId);
            if (!AssertUtils.isResultObjectDataNull(policyResponseResultObject)) {
                PolicyResponse policyResponse = policyResponseResultObject.getData();
                returnVisitResponse.setPolicyType(policyResponse.getPolicyType());

            }
        }
        //保单信息
        returnVisitDetailResponse.setPolicyReturnVisit(returnVisitResponse);
        if (!AssertUtils.isNotNull(returnVisitChangeId)) {
            ReturnVisitChangePo returnVisitChangePo = returnVisitBaseService.queryReturnVisitChangePo(returnVisitId);
            if (AssertUtils.isNotNull(returnVisitChangePo)) {
                returnVisitChangeId = returnVisitChangePo.getReturnVisitChangeId();
            }
        }
        //附件信息
        List<ReturnVisitAttachmentPo> returnVisitAttachmentPos = new ArrayList<>();
        if (AssertUtils.isNotNull(returnVisitChangeId)) {
            List<ReturnVisitChangeAttachmentPo> changeAttachmentPos = returnVisitChangeAttachmentBaseDao.getAttachmentByReturnVisitChangeId(returnVisitChangeId);
            returnVisitAttachmentPos = (List<ReturnVisitAttachmentPo>) this.converterList(changeAttachmentPos, new TypeToken<List<ReturnVisitAttachmentPo>>() {
            }.getType());
        } else {
            returnVisitAttachmentPos = returnVisitAttachmentBaseDao.getReturnVisitAttachmentByReturnVisitId(returnVisitId);
        }


        if (AssertUtils.isNotEmpty(returnVisitAttachmentPos)) {
            List<String> attachmentList = returnVisitAttachmentPos.stream().map(ReturnVisitAttachmentPo::getAttachmentId).collect(Collectors.toList());
            ResultObject<List<AttachmentResponse>> attachmentResultObject = attachmentApi.attachmentList(attachmentList);
            AssertUtils.isResultObjectError(attachmentResultObject);
            List<AttachmentResponse> attachmentResponseList = attachmentResultObject.getData();

            List<ReturnVisitAttachmentResponse> attachements = new ArrayList<>();
            returnVisitAttachmentPos.stream().forEach(attachement -> {
                ReturnVisitAttachmentResponse attachmentResponse = new ReturnVisitAttachmentResponse();
                attachmentResponse.setAttachmentId(attachement.getAttachmentId());
                attachmentResponseList.stream().filter(temp -> temp.getMediaId().equals(attachement.getAttachmentId()))
                        .findFirst().ifPresent(a -> {
                    attachmentResponse.setAttachmentName(a.getFileName() + "." + a.getFileSuffix());
                    attachmentResponse.setUrl(a.getUrl());
                });
                attachements.add(attachmentResponse);
            });
            returnVisitDetailResponse.setPolicyReturnAttachmentVisitList(attachements);
        }

        resultObject.setData(returnVisitDetailResponse);
        return resultObject;
    }

    @Override
    public ResultObject postPolicyReturnVisitSubmit(Users users, PolicyReturnVisitSubmitRequest policyReceiptSubmitRequest) {
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitId(), PartyErrorConfigEnum.PARTY_PARAMETER_RETURN_VISIT_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitType(), PartyErrorConfigEnum.PARTY_PARAMETER_RETURN_VISIT_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitDateFormat(), PartyErrorConfigEnum.PARTY_PARAMETER_RETURN_VISIT_DATE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitChannel(), PartyErrorConfigEnum.PARTY_SAVE_POLICY_RETURN_VISIT_CHANNEL_IS_NOT_NULL_ERROR);
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitResult(), PartyErrorConfigEnum.PARTY_SAVE_POLICY_RETURN_VISIT_RESULT_IS_NOT_NULL_ERROR);
        AssertUtils.isNotNull(this.getLogger(), policyReceiptSubmitRequest.getReturnVisitRemark(), PartyErrorConfigEnum.PARTY_SAVE_POLICY_RETURN_VISIT_REMARK_IS_NOT_NULL_ERROR);

        ReturnVisitPo returnVisitPo = returnVisitBaseService.getByReturnVisitId(policyReceiptSubmitRequest.getReturnVisitId());
        if (!AssertUtils.isNotNull(returnVisitPo)) {
            throwsException(PartyErrorConfigEnum.PARTY_RETURN_VISIT_IS_NOT_EXIST);
        }

        long returnVisitDate = DateUtils.stringToTime(policyReceiptSubmitRequest.getReturnVisitDateFormat(), DateUtils.FORMATE3);
        if (returnVisitDate > DateUtils.timeToTimeLow(DateUtils.getCurrentTime())) {
            throw new RequestException(PartyErrorConfigEnum.PARTY_RETURN_VISIT_DATE_CANNOT_BE_GREATER_THAN_THE_CURRENT_DATE);
        }
        ReflectionUtils.copyProperties(returnVisitPo, policyReceiptSubmitRequest);
        returnVisitPo.setReturnVisitStatus(PartyTermEnum.RETURN_VISIT_STATUS.YES_RETURN_VISIT.name());
        returnVisitPo.setReturnVisitDate(returnVisitDate);
        returnVisitPo.setReturnVisitUserId(users.getUserId());
        returnVisitBaseService.saveReturnVisitPo(users.getUserId(), returnVisitPo);

        String returnVisitId = returnVisitPo.getReturnVisitId();
        List<ReturnVisitAttachmentPo> policyReturnVisitAttachmentPoList = new ArrayList<>();
        List<PolicyReturnVisitAttachmentRequest> policyReturnAttachmentVisitList = policyReceiptSubmitRequest.getPolicyReturnAttachmentVisitList();
        policyReturnAttachmentVisitList.forEach(policyReturnVisitAttachmentRequest -> {
            ReturnVisitAttachmentPo policyReturnVisitAttachmentPo = new ReturnVisitAttachmentPo();
            policyReturnVisitAttachmentPo.setReturnVisitAttachmentId(UUIDUtils.getUUIDShort());
            policyReturnVisitAttachmentPo.setAttachmentId(policyReturnVisitAttachmentRequest.getAttachmentId());
            policyReturnVisitAttachmentPo.setCreatedUserId(users.getUserId());
            policyReturnVisitAttachmentPo.setCreatedDate(System.currentTimeMillis());
            policyReturnVisitAttachmentPo.setReturnVisitId(returnVisitId);
            policyReturnVisitAttachmentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyReturnVisitAttachmentPoList.add(policyReturnVisitAttachmentPo);
        });
        returnVisitAttachmentDao.insert(policyReturnVisitAttachmentPoList);

        //保全回访回调
        if (NEW_POLICY.name().equals(policyReceiptSubmitRequest.getReturnVisitType()) ||
                PartyTermEnum.RETURN_VISIT_TYPE.GROUP_ADD_INSURED.name().equals(policyReceiptSubmitRequest.getReturnVisitType())
                || PartyTermEnum.RETURN_VISIT_TYPE.GROUP_ADD_ADDITIONAL.name().equals(policyReceiptSubmitRequest.getReturnVisitType())
                || PartyTermEnum.RETURN_VISIT_TYPE.ADD_SUBTRACT_INSURED.name().equals(policyReceiptSubmitRequest.getReturnVisitType())
                || GROUP_RENEWAL.name().equals(policyReceiptSubmitRequest.getReturnVisitType())
        ) {
            ReturnVisitNotifyRequest notifyRequest = new ReturnVisitNotifyRequest();
            ReflectionUtils.copyProperties(notifyRequest, returnVisitPo);
            String serviceUrl = returnVisitPo.getReturnVisitService();
            URI uri = URI.create(serviceUrl);
            ResultObject result = returnVisitApi.returnVisitResultNotify(uri, notifyRequest);
            log.info("回调结果:" + JackSonUtils.toJson(result));
        }

        return ResultObject.success();
    }

    /**
     * 保单回访记录
     *
     * @param pageRequest
     * @return
     */
    @Override
    public ResultObject getReturnVisitChangeList(ReturnVisitPageRequest pageRequest) {
        ResultObject<BasePageResponse<ReturnVisitChangeListResponse>> resultObject = ResultObject.success();
        List<ReturnVisitChangeListResponse> returnVisitResponseList = new ArrayList<>();
        List<ReturnVisitBo> returnVisitBoList = returnVisitExtDao.queryReturnVisitChangeList(pageRequest);

        returnVisitBoList.stream().forEach(returnVisitBo -> {
            ReturnVisitChangeListResponse changeListResponse = new ReturnVisitChangeListResponse();
            changeListResponse.setReturnVisitId(returnVisitBo.getReturnVisitId());
            //保全的话从保全获取保全申请信息
            String policyId = getPolicyIdByReturnVisit(returnVisitBo);

            //保单信息
            ResultObject<PolicyResponse> policyResponseResultObject = policyApi.queryOnePolicy(policyId);
            if (!AssertUtils.isResultObjectDataNull(policyResponseResultObject)) {
                PolicyResponse policyResponse = policyResponseResultObject.getData();
                changeListResponse.setPolicyNo(policyResponse.getPolicyNo());
                changeListResponse.setApplyNo(policyResponse.getApplyNo());

                if (PartyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name().equalsIgnoreCase(policyResponse.getPolicyType())) {
                    CustomerAgentPo customerAgentPo = returnVisitBo.getCustomerAgentPo();
                    if (AssertUtils.isNotNull(customerAgentPo)) {
                        changeListResponse.setCustomerIdNo(customerAgentPo.getIdNo());
                        changeListResponse.setCustomerIdTypeCode(customerAgentPo.getIdType());
                        changeListResponse.setCustomerName(customerAgentPo.getName());
                        changeListResponse.setCustomerMobile(customerAgentPo.getMobile());
                    }

                } else if (PartyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name().equalsIgnoreCase(policyResponse.getPolicyType())) {
                    CustomerAgentPo customerAgentPo = returnVisitBo.getCustomerAgentPo();
                    if (AssertUtils.isNotNull(customerAgentPo)) {
                        changeListResponse.setCustomerIdNo(customerAgentPo.getDelegateIdNo());
                        changeListResponse.setCustomerIdTypeCode(customerAgentPo.getDelegateIdType());
                        changeListResponse.setCustomerName(customerAgentPo.getDelegateName());
                        changeListResponse.setCustomerMobile(customerAgentPo.getDelegateMobile());
                    }
                }

            }

            if (ONLINE_POLICY.name().equals(returnVisitBo.getBusinessType())) {
                changeListResponse.setCustomerName(returnVisitBo.getApplyName());
                changeListResponse.setCustomerMobile(returnVisitBo.getOnlineMobile());
            }

            System.out.println("--保单基本信息:" + JackSonUtils.toJson(policyResponseResultObject));
            changeListResponse.setBusinessType(returnVisitBo.getBusinessType());
            changeListResponse.setReturnVisitType(returnVisitBo.getReturnVisitType());
            //展示为操作时间
            changeListResponse.setReturnVisitDate(returnVisitBo.getUpdatedDate());
            UserInfoResponse userResponse = platformUsersApi.userInfoGet(returnVisitBo.getReturnVisitUserId()).getData();
            if (AssertUtils.isNotNull(userResponse)) {
                changeListResponse.setReturnVisitUserName(userResponse.getName());
            }

            returnVisitResponseList.add(changeListResponse);

        });
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(returnVisitBoList) ? returnVisitBoList.get(0).getTotalLine() : null;

        BasePageResponse basePageResponse = BasePageResponse.getData(pageRequest.getCurrentPage(), pageRequest.getPageSize(), totalLine, returnVisitResponseList);
        resultObject.setData(basePageResponse);

        return resultObject;
    }

    @Override
    public ResultObject postPolicyReturnVisitChangeSubmit(Users currentLoginUsers, PolicyReturnVisitChangeRequest policyReceiptSubmitRequest) {
        ResultObject resultObject = ResultObject.success();
        String returnVisitId = policyReceiptSubmitRequest.getReturnVisitId();
        AssertUtils.isNotEmpty(this.getLogger(), returnVisitId, PartyErrorConfigEnum.PARTY_RETURN_VISIT_ID_NOT_NULL);

        //创建保单回访修改申请
        Long currentTime = DateUtils.getCurrentTime();
        ReturnVisitChangePo returnVisitChangePo = new ReturnVisitChangePo();
        returnVisitChangePo.setReturnVisitChangeId(UUIDUtils.getUUIDShort());
        returnVisitChangePo.setReturnVisitId(returnVisitId);
        returnVisitChangePo.setCreatedDate(currentTime);
        returnVisitChangePo.setCreatedUserId(currentLoginUsers.getUserId());
        returnVisitChangePo.setAuditResult(PartyTermEnum.CHANGE_AUDIT_FLAG.wait_audit.name());
        returnVisitChangePo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
        returnVisitChangePo.setChangeReamrk(policyReceiptSubmitRequest.getChangeRemark());
        returnVisitChangeDao.insert(returnVisitChangePo);

        //修改主表状态
        /*ReturnVisitPo returnVisitPo = returnVisitBaseService.getByReturnVisitId(returnVisitId);
        returnVisitPo.setReturnVisitStatus(PartyTermEnum.RETURN_VISIT_STATUS.UNDER_EDIT_VISIT.name());
        returnVisitBaseService.saveReturnVisitPo(currentLoginUsers.getUserId(),returnVisitPo);*/

        List<PolicyReturnVisitAttachmentRequest> attachmentList = policyReceiptSubmitRequest.getPolicyReturnAttachmentVisitList();

        if (AssertUtils.isNotEmpty(attachmentList)) {
            List<ReturnVisitChangeAttachmentPo> attachmentPoList = new ArrayList<>();
            attachmentList.forEach(attachment -> {
                ReturnVisitChangeAttachmentPo attachmentPo = new ReturnVisitChangeAttachmentPo();
                attachmentPo.setRvcaId(UUIDUtils.getUUIDShort());
                attachmentPo.setValidFlag(PartyTermEnum.VALID_FLAG.effective.name());
                attachmentPo.setCreatedUserId(currentLoginUsers.getUserId());
                attachmentPo.setCreatedDate(currentTime);
                attachmentPo.setAttachmentId(attachment.getAttachmentId());
                attachmentPo.setReturnVisitChangeId(returnVisitChangePo.getReturnVisitChangeId());
                attachmentPoList.add(attachmentPo);
            });
            returnVisitChangeAttachmentDao.insert(attachmentPoList);
        }

        return resultObject;
    }

    @Override
    public ResultObject getPolicyReturnVisitAudit(ReturnVisitChangePageRequest pageRequest) {
        ResultObject resultObject = ResultObject.success();
        List<ReturnVisitAuditListResponse> returnVisitAuditListResponses = new ArrayList<>();
        List<ReturnVisitChangePo> returnVisitChangePoList = returnVisitBaseService.getReturnVisitAuditList(pageRequest);
        if (!AssertUtils.isNotEmpty(returnVisitChangePoList)) {
            return resultObject;
        }
        returnVisitChangePoList.stream().forEach(returnVisitChangePo -> {
            ReturnVisitAuditListResponse auditListResponse = new ReturnVisitAuditListResponse();
            auditListResponse.setReturnVisitChangeId(returnVisitChangePo.getReturnVisitChangeId());
            ReturnVisitPo returnVisitPo = returnVisitBaseService.getByReturnVisitId(returnVisitChangePo.getReturnVisitId());
            ReflectionUtils.copyProperties(auditListResponse, returnVisitPo);
            //显示为操作时间
            auditListResponse.setReturnVisitDate(returnVisitPo.getUpdatedDate());
            auditListResponse.setReturnVisitType(returnVisitPo.getReturnVisitType());
            auditListResponse.setBusinessTypeName(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.fromCode(returnVisitPo.getBusinessType()).desc());
            ResultObject<UserInfoResponse> userResponseResultObject = platformUsersApi.userInfoGet(returnVisitPo.getReturnVisitUserId());
            if (!AssertUtils.isResultObjectDataNull(userResponseResultObject)) {
                auditListResponse.setReturnVisitUserName(userResponseResultObject.getData().getName());
            }

            //申请用户
            auditListResponse.setOperationUserId(returnVisitChangePo.getCreatedUserId());
            UserInfoResponse userResponse = platformUsersApi.userInfoGet(returnVisitChangePo.getCreatedUserId()).getData();
            if (AssertUtils.isNotNull(userResponse)) {
                auditListResponse.setOperationUserName(userResponse.getName());
            }

            auditListResponse.setOperationDate(returnVisitChangePo.getCreatedDate());
            auditListResponse.setReturnVisitStatusName("待审核");

            //保全的话从保全获取保全申请信息
            String policyId = getPolicyIdByReturnVisit(returnVisitPo);

            //联系人信息
            CustomerAgentPo customerAgentPo = customerBaseDao.queryOneCustomerAgent(returnVisitPo.getCustomerId());
            //保单信息
            ResultObject<PolicyResponse> policyResponseResultObject = policyApi.queryOnePolicy(policyId);
            if (!AssertUtils.isResultObjectDataNull(policyResponseResultObject)) {
                PolicyResponse policyResponse = policyResponseResultObject.getData();
                auditListResponse.setPolicyNo(policyResponse.getPolicyNo());
                auditListResponse.setApplyNo(policyResponse.getApplyNo());

                if (PartyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name().equalsIgnoreCase(policyResponse.getPolicyType())) {
                    if (AssertUtils.isNotNull(customerAgentPo)) {
                        auditListResponse.setCustomerIdNo(customerAgentPo.getIdNo());
                        auditListResponse.setCustomerIdTypeCode(customerAgentPo.getIdType());
                        auditListResponse.setCustomerMobile(customerAgentPo.getMobile());
                        auditListResponse.setCustomerName(customerAgentPo.getName());
                    }

                } else if (PartyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name().equalsIgnoreCase(policyResponse.getPolicyType())) {
                    if (AssertUtils.isNotNull(customerAgentPo)) {
                        auditListResponse.setCustomerIdNo(customerAgentPo.getDelegateIdNo());
                        auditListResponse.setCustomerIdTypeCode(customerAgentPo.getDelegateIdType());
                        auditListResponse.setCustomerName(customerAgentPo.getDelegateName());
                        auditListResponse.setCustomerMobile(customerAgentPo.getDelegateMobile());
                    }
                }

            }

            if (ONLINE_POLICY.name().equals(returnVisitPo.getBusinessType())) {
                auditListResponse.setCustomerName(returnVisitPo.getApplyName());
                auditListResponse.setCustomerMobile(returnVisitPo.getOnlineMobile());
            }

            returnVisitAuditListResponses.add(auditListResponse);
        });

        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(returnVisitChangePoList) ? returnVisitChangePoList.get(0).getTotalLine() : null;

        BasePageResponse basePageResponse = BasePageResponse.getData(pageRequest.getCurrentPage(), pageRequest.getPageSize(), totalLine, returnVisitAuditListResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    @Override
    public ResultObject postReturnVisitAuditSubmit(Users currentLoginUsers, ReturnVisitAuditRequest policyReturnVisitAuditRequest) {
        ResultObject resultObject = new ResultObject();
        AssertUtils.isNotEmpty(this.getLogger(), policyReturnVisitAuditRequest.getReturnVisitChangeId(), PartyErrorConfigEnum.PARTY_RETURN_VISIT_CHANGE_ID_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), policyReturnVisitAuditRequest.getAuditRemark(), PartyErrorConfigEnum.PARTY_SAVE_POLICY_RETURN_VISIT_AUDIT_REMARK_IS_NOT_NULL_ERROR);
        AssertUtils.isNotEmpty(this.getLogger(), policyReturnVisitAuditRequest.getAuditResult(), PartyErrorConfigEnum.PARTY_SAVE_POLICY_RETURN_VISIT_AUDIT_RESULT_IS_NOT_NULL_ERROR);

        ReturnVisitChangePo returnVisitChangePo = returnVisitChangeDao.fetchOneByReturnVisitChangeId(policyReturnVisitAuditRequest.getReturnVisitChangeId());

        AssertUtils.isNotNull(this.getLogger(), returnVisitChangePo, PartyErrorConfigEnum.PARTY_RETURN_VISIT_CHANGE_NOT_EXIST);
        returnVisitChangePo.setUpdatedDate(DateUtils.getCurrentTime());
        returnVisitChangePo.setUpdatedUserId(currentLoginUsers.getUserId());
        returnVisitChangePo.setAuditDate(DateUtils.getCurrentTime());
        returnVisitChangePo.setAuditUserId(currentLoginUsers.getUserId());
        returnVisitChangePo.setAuditRemark(policyReturnVisitAuditRequest.getAuditRemark());
        returnVisitChangePo.setAuditResult(policyReturnVisitAuditRequest.getAuditResult());
        returnVisitChangeDao.update(returnVisitChangePo);


        if (!PartyTermEnum.OPERATIONAL_RESULT.SUBMITTED.name().equals(policyReturnVisitAuditRequest.getAuditResult())) {
            return resultObject;
        }

        //修改原始文件失效

        returnVisitAttachmentBaseDao.updateInvalidById(returnVisitChangePo.getReturnVisitId());

        //修改附件

        List<ReturnVisitChangeAttachmentPo> attachmentPoList = returnVisitChangeAttachmentBaseDao.getAttachmentByReturnVisitChangeId(policyReturnVisitAuditRequest.getReturnVisitChangeId());
        if (AssertUtils.isNotEmpty(attachmentPoList)) {
            List<ReturnVisitAttachmentPo> policyReturnVisitAttachmentPoList = new ArrayList<>();
            attachmentPoList.forEach(attachment -> {
                ReturnVisitAttachmentPo policyReturnVisitAttachmentPo = new ReturnVisitAttachmentPo();
                policyReturnVisitAttachmentPo.setReturnVisitAttachmentId(UUIDUtils.getUUIDShort());
                policyReturnVisitAttachmentPo.setAttachmentId(attachment.getAttachmentId());
                policyReturnVisitAttachmentPo.setReturnVisitId(returnVisitChangePo.getReturnVisitId());
                policyReturnVisitAttachmentPo.setCreatedDate(DateUtils.getCurrentTime());
                policyReturnVisitAttachmentPo.setCreatedUserId(currentLoginUsers.getUserId());
                policyReturnVisitAttachmentPo.setValidFlag(PartyTermEnum.VALID_FLAG.effective.name());
                policyReturnVisitAttachmentPoList.add(policyReturnVisitAttachmentPo);
            });
            returnVisitAttachmentDao.insert(policyReturnVisitAttachmentPoList);
        }

        return resultObject;
    }

    @Override
    public ResultObject<ReturnVisitTypeResponse> getReturnVisitTypeEnum() {
        ResultObject<ReturnVisitTypeResponse> resultObject = ResultObject.success();
        ReturnVisitTypeResponse returnVisitDetailResponse = new ReturnVisitTypeResponse();
        //枚举类型
        ResultObject<List<SyscodeRespFc>> returnVisitChannelList = platformBaseInternationServiceApi.queryInternational(PartyTermEnum.POLICY_RETURN_VISIT_ENUM.RETURN_VISIT_CHANNEL.name(), null);
        AssertUtils.isResultObjectError(this.getLogger(), returnVisitChannelList);
        returnVisitDetailResponse.setReturnVisitChannelList(returnVisitChannelList.getData());

        ResultObject<List<SyscodeRespFc>> returnVisitResultList = platformBaseInternationServiceApi.queryInternational(PartyTermEnum.POLICY_RETURN_VISIT_ENUM.RETURN_VISIT_RESULT.name(), null);
        AssertUtils.isResultObjectError(this.getLogger(), returnVisitResultList);
        returnVisitDetailResponse.setReturnVisitResultList(returnVisitResultList.getData());

        ResultObject<List<SyscodeRespFc>> mustReturnFlag = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.WHETHER.name(), null);
        AssertUtils.isResultObjectError(this.getLogger(), mustReturnFlag);
        returnVisitDetailResponse.setMustReturnFlag(mustReturnFlag.getData());

        ResultObject<List<SyscodeRespFc>> returnVisitTypeResultList = platformBaseInternationServiceApi.queryInternational(PartyTermEnum.POLICY_RETURN_VISIT_ENUM.RETURN_VISIT_TYPE.name(), null);
        AssertUtils.isResultObjectError(this.getLogger(), returnVisitTypeResultList);
        /*List<SyscodeRespFc> list = returnVisitTypeResultList.getData().stream().filter(syscodeRespFc -> !(syscodeRespFc.getCodeKey().equals(PartyTermEnum.RETURN_VISIT_TYPE.NEW_POLICY.name())||
                syscodeRespFc.getCodeKey().equals(PartyTermEnum.RETURN_VISIT_TYPE.GROUP_ADD_INSURED.name())))
                .collect(Collectors.toList());*/
        returnVisitDetailResponse.setReturnVisitTypeList(returnVisitTypeResultList.getData());

        ResultObject<List<SyscodeRespFc>> source_type = platformBaseInternationServiceApi.queryInternational("SOURCE_TYPE", null);
        AssertUtils.isResultObjectError(this.getLogger(), source_type);
        returnVisitDetailResponse.setSourceTypeList(source_type.getData());

        List<SyscodeRespFc> businessTypeTerm = new ArrayList<>();
        for (PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE each : PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.values()) {
            SyscodeRespFc code = new SyscodeRespFc();
            code.setCodeKey(each.name());
            code.setCodeName(each.desc());
            businessTypeTerm.add(code);
        }
        returnVisitDetailResponse.setBusinessTypeList(businessTypeTerm);

        //保单状态
        List<SyscodeRespFc> returnVisitStatusTerm = new ArrayList<>();
        SyscodeRespFc yesReturnVisit = new SyscodeRespFc();
        yesReturnVisit.setCodeKey(PartyTermEnum.RETURN_VISIT_STATUS.YES_RETURN_VISIT.name());
        yesReturnVisit.setCodeName(PartyTermEnum.RETURN_VISIT_STATUS.YES_RETURN_VISIT.desc());
        returnVisitStatusTerm.add(yesReturnVisit);
        SyscodeRespFc noReturnVisit = new SyscodeRespFc();
        noReturnVisit.setCodeKey(PartyTermEnum.RETURN_VISIT_STATUS.NO_RETURN_VISIT.name());
        noReturnVisit.setCodeName(PartyTermEnum.RETURN_VISIT_STATUS.NO_RETURN_VISIT.desc());
        returnVisitStatusTerm.add(noReturnVisit);
        returnVisitDetailResponse.setReturnVisitStatusList(returnVisitStatusTerm);

        resultObject.setData(returnVisitDetailResponse);
        return resultObject;
    }

    @Override
    public ResultObject<List<ReturnVisitContentResponse>> getReturnVisitList(String businessId, String businessType) {
        ResultObject<List<ReturnVisitContentResponse>> resultObject = new ResultObject<>();
        AssertUtils.isNotNull(this.getLogger(), businessId, PartyErrorConfigEnum.PARTY_PARAMETER_BUSINESS_ID_IS_NOT_NULL);
        List<ReturnVisitPo> returnVisitPoList = returnVisitBaseService.getReturnVisitPoList(businessId, businessType);
        AssertUtils.isNotEmpty(this.getLogger(), returnVisitPoList, PartyErrorConfigEnum.PARTY_RETURN_VISIT_IS_NOT_EXIST);
        List<ReturnVisitContentResponse> returnVisitContentResponses = (List<ReturnVisitContentResponse>) this.converterList(returnVisitPoList, new TypeToken<List<ReturnVisitContentResponse>>() {
        }.getType());
        resultObject.setData(returnVisitContentResponses);
        return resultObject;
    }

    private String getPolicyIdByReturnVisit(ReturnVisitPo returnVisitPo) {
        String policyId = "";
        if (PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.ENDORSE.name().equalsIgnoreCase(returnVisitPo.getBusinessType())) {
            ResultObject<AcceptApplyResponse> acceptApplyResponseResultObject = endorseAcceptApi.queryApplyInfo(returnVisitPo.getBusinessId());
            if (!AssertUtils.isResultObjectDataNull(acceptApplyResponseResultObject)) {
                policyId = acceptApplyResponseResultObject.getData().getApplyId();
            }
        } else if (PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.POLICY.name().equalsIgnoreCase(returnVisitPo.getBusinessType())) {
            if (GROUP_RENEWAL.name().equalsIgnoreCase(returnVisitPo.getReturnVisitType())) {
                ResultObject<GroupRenewalReturnVisistResponse> groupReturnVisitResultObject = groupRenewalApi.getGroupRenewalReturnVisitInfo(returnVisitPo.getBusinessId());
                policyId = groupReturnVisitResultObject.getData().getPolicyId();
            } else {
                policyId = returnVisitPo.getBusinessId();
            }

        }
        if (!ONLINE_POLICY.name().equals(returnVisitPo.getBusinessType())) {
            AssertUtils.isNotEmpty(this.getLogger(), policyId, PartyErrorConfigEnum.PARTY_RETURN_VISIT_POLICY_IS_NOT_DOUND);
        }
        return policyId;
    }

    @Override
    public ResultObject<ReturnVisitStatisticsListResponse> getPolicyReturnVisitStatistics(ReturnVisitPageRequest pageRequest, Users currentLoginUsers) {
        ResultObject<ReturnVisitStatisticsListResponse> resultObject = new ResultObject<>();
        List<ReturnVisitBo> returnVisitBoList = returnVisitExtDao.getPolicyReturnVisitStatistics(pageRequest);
        //获取国际化数据
        ResultObject<List<SyscodeResponse>> syscodetResultObject = platformInternationalBaseApi.queryInternational(RETURN_VISIT_STATISTICS_TYPE.name(), currentLoginUsers.getLanguage());
        AssertUtils.isResultObjectError(this.getLogger(), syscodetResultObject);
        List<SyscodeResponse> syscodeResponseList = syscodetResultObject.getData();
        ReturnVisitStatisticsListResponse returnVisitStatisticsListResponse = new ReturnVisitStatisticsListResponse();
        List<ReturnVisitStatisticsResponse> firstReturnVisitStatisticsList = new ArrayList<>();

        ReturnVisitStatisticsResponse returnVisitStatisticsResponse = new ReturnVisitStatisticsResponse();
        returnVisitStatisticsResponse.setReturnVisitTypeName(LanguageUtils.getCodeName(syscodeResponseList,TOTAL_RETURN_VISIT.name()));
        returnVisitStatisticsResponse.setReturnVisitType(TOTAL_RETURN_VISIT.name());
        returnVisitStatisticsResponse.setReturnVisitSum(returnVisitBoList.stream().mapToInt(ReturnVisitBo::getReturnVisitSum).sum());
        returnVisitStatisticsResponse.setYesReturnVisitSum(returnVisitBoList.stream().mapToInt(ReturnVisitBo::getYesReturnVisitSum).sum());
        returnVisitStatisticsResponse.setReturnVisitRate(getRate(returnVisitStatisticsResponse.getYesReturnVisitSum(),returnVisitStatisticsResponse.getReturnVisitSum()));
        firstReturnVisitStatisticsList.add(returnVisitStatisticsResponse);

        ReturnVisitStatisticsResponse mustReturnVisitStatisticsResponse = new ReturnVisitStatisticsResponse();
        mustReturnVisitStatisticsResponse.setReturnVisitTypeName(LanguageUtils.getCodeName(syscodeResponseList,MUST_RETURN_VISIT.name()));
        mustReturnVisitStatisticsResponse.setReturnVisitType(MUST_RETURN_VISIT.name());
        mustReturnVisitStatisticsResponse.setReturnVisitSum(returnVisitBoList.stream().mapToInt(ReturnVisitBo::getMustReturnVisitSum).sum());
        mustReturnVisitStatisticsResponse.setYesReturnVisitSum(returnVisitBoList.stream().mapToInt(ReturnVisitBo::getYesMustReturnVisitSum).sum());
        mustReturnVisitStatisticsResponse.setReturnVisitRate(getRate(mustReturnVisitStatisticsResponse.getYesReturnVisitSum(),mustReturnVisitStatisticsResponse.getReturnVisitSum()));
        firstReturnVisitStatisticsList.add(mustReturnVisitStatisticsResponse);

        returnVisitStatisticsListResponse.setFirstReturnVisitStatisticsList(firstReturnVisitStatisticsList);
        List<String> firstReturnVisit = Arrays.asList(TOTAL_RETURN_VISIT.name(), MUST_RETURN_VISIT.name());
        List<ReturnVisitStatisticsResponse> lastReturnVisitStatisticsList = new ArrayList<>();
        List<String> lastReturnVisit = Arrays.asList(NEW_POLICY.name(), GROUP_RENEWAL.name(), PREMIUM_RENEWAL_POLICY_RENEWAL.name());


        syscodeResponseList.forEach(syscodeResponse -> {
            if(firstReturnVisit.contains(syscodeResponse.getCodeKey())){
                return;
            }
            Optional<ReturnVisitBo> optionalReturnVisitBo = returnVisitBoList.stream().filter(returnVisitBo -> returnVisitBo.getReturnVisitType().equals(syscodeResponse.getCodeKey())).findFirst();
            if(!lastReturnVisit.contains(syscodeResponse.getCodeKey())){
                return;
            }
            ReturnVisitBo returnVisitBo = null;
            if(optionalReturnVisitBo.isPresent()){
                returnVisitBo = optionalReturnVisitBo.get();
            }else {
                returnVisitBo = new ReturnVisitBo();
            }
            ReturnVisitStatisticsResponse returnVisitResponse = new ReturnVisitStatisticsResponse();
            returnVisitResponse.setReturnVisitTypeName(syscodeResponse.getCodeName());
            returnVisitResponse.setReturnVisitType(syscodeResponse.getCodeKey());
            returnVisitResponse.setReturnVisitSum(returnVisitBo.getReturnVisitSum());
            returnVisitResponse.setYesReturnVisitSum(returnVisitBo.getYesReturnVisitSum());
            returnVisitResponse.setReturnVisitRate(getRate(returnVisitResponse.getYesReturnVisitSum(),returnVisitResponse.getReturnVisitSum()));
            lastReturnVisitStatisticsList.add(returnVisitResponse);
        });
        returnVisitStatisticsListResponse.setLastReturnVisitStatisticsList(lastReturnVisitStatisticsList);
        resultObject.setData(returnVisitStatisticsListResponse);
        return resultObject;
    }

    protected BigDecimal getRate(int yesSum, int totalSum) {
        if (totalSum != 0 && yesSum != 0) {
            return new BigDecimal(yesSum).divide(new BigDecimal(totalSum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
        }
        return new BigDecimal(0);
    }

}

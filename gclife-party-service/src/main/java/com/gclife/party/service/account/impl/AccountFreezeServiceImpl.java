package com.gclife.party.service.account.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.party.core.jooq.tables.pojos.UserAccountOperateReviewPo;
import com.gclife.party.model.bo.AccountOperateListBo;
import com.gclife.party.model.bo.UserAccountBo;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.account.AccountOperateListRequest;
import com.gclife.party.model.request.account.AccountOperateRequest;
import com.gclife.party.model.request.account.AccountOperateReviewRequest;
import com.gclife.party.model.response.account.AccountOperateListResponse;
import com.gclife.party.model.response.account.AccountOperateReviewListResponse;
import com.gclife.party.model.vo.AccountOperateListVo;
import com.gclife.party.service.account.AccountFreezeService;
import com.gclife.party.service.base.UserAccountBaseService;
import com.gclife.party.validate.transfer.UserAccountTransfer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static com.gclife.party.model.config.PartyErrorConfigEnum.*;

/**
 * <AUTHOR>
 * create 19-7-12
 * description:
 */
@Service
public class AccountFreezeServiceImpl extends BaseBusinessServiceImpl implements AccountFreezeService {

    @Autowired
    private UserAccountBaseService userAccountBaseService;
    @Autowired
    private UserAccountTransfer userAccountTransfer;

    /**
     * 账户冻结列表
     *
     * @param accountOperateListRequest 列表参数
     * @param users                     用户
     * @return AccountOperateListResponse
     */
    @Override
    public ResultObject<BasePageResponse<AccountOperateListResponse>> getAccountFreezeList(AccountOperateListRequest accountOperateListRequest, Users users) {
        ResultObject<BasePageResponse<AccountOperateListResponse>> resultObject = new ResultObject<>();

        AccountOperateListVo accountOperateListVo = (AccountOperateListVo) this.converterObject(accountOperateListRequest, AccountOperateListVo.class);
        accountOperateListVo.setAccountStatus(PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name());
        List<AccountOperateListBo> accountOperateListBos = userAccountBaseService.getAccountOperateList(accountOperateListVo);
        List<AccountOperateListResponse> accountOperateListResponses = userAccountTransfer.transAccountOperateList(accountOperateListBos, users);
        Integer totalLine = AssertUtils.isNotNull(accountOperateListBos) ? accountOperateListBos.get(0).getTotalLine() : null;
        BasePageResponse basePageResponse = BasePageResponse.getData(accountOperateListRequest.getCurrentPage(), accountOperateListRequest.getPageSize(), totalLine, accountOperateListResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    /**
     * 申请账户冻结
     *
     * @param accountOperateRequest 申请参数
     * @param users                 用户
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postAccountFreeze(AccountOperateRequest accountOperateRequest, Users users) {
        ResultObject resultObject = new ResultObject();
        this.getLogger().info("accountOperateRequest:{}", JSON.toJSONString(accountOperateRequest));

        String userAccountId = accountOperateRequest.getUserAccountId();
        AssertUtils.isNotEmpty(this.getLogger(), userAccountId, PARTY_USER_ACCOUNT_ID_IS_NOT_NULL);

        UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBoById(userAccountId);
        AssertUtils.isNotNull(this.getLogger(), userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);
        if (PartyTermEnum.ACCOUNT_STATUS.FROZEN.name().equals(userAccountBo.getAccountStatus())) {
            throw new RequestException(PARTY_THE_ACCOUNT_IS_FROZEN_AND_CANNOT_BE_OPERATED);
        }

        //录入备注信息，点击提交按钮，该账户信息提交到冻结审批岗；点击取消关闭弹窗。
        //校验信息：若该账户已发起冻结申请，流程在冻结审批未确认之前，再次发起冻结时须提示“该账户已发起冻结申请，不允许再次提交”
        UserAccountOperateReviewPo userAccountOperateReview = userAccountBaseService.getUserAccountOperateReview(userAccountId, PartyTermEnum.ACCOUNT_STATUS.FROZEN.name());
        if (AssertUtils.isNotNull(userAccountOperateReview) && userAccountOperateReview.getReviewStatus().equals(PartyTermEnum.ACCOUNT_REVIEW_STATUS.INITIAL.name())) {
            throw new RequestException(PARTY_THE_ACCOUNT_HAS_INITIATED_A_FREEZE_REQUEST_ERROR);
        }

        UserAccountOperateReviewPo userAccountOperateReviewPo = new UserAccountOperateReviewPo();
        userAccountOperateReviewPo.setUserAccountId(userAccountId);
        userAccountOperateReviewPo.setOperateType(PartyTermEnum.ACCOUNT_STATUS.FROZEN.name());
        userAccountOperateReviewPo.setApplyRemark(accountOperateRequest.getRemark());
        userAccountOperateReviewPo.setApplyDate(DateUtils.getCurrentTime());
        userAccountOperateReviewPo.setApplyUserId(users.getUserId());
        userAccountOperateReviewPo.setReviewStatus(PartyTermEnum.ACCOUNT_REVIEW_STATUS.INITIAL.name());
        userAccountBaseService.saveUserAccountOperateReview(userAccountOperateReviewPo, users.getUserId());

        // 保存冻结原因
        userAccountBo.setFreezeReasonCode(accountOperateRequest.getFreezeReasonCode());
        userAccountBaseService.saveUserAccount(userAccountBo, users.getUserId());

        //发送申请冻结消息
        userAccountTransfer.sendAccountReviewMessage(PartyTermEnum.MSG_BUSINESS_TYPE.ACCOUNT_FREEZE_AUDIT_MESSAGE_REMINDER.name(), userAccountBo.getUserId(), users);
        return resultObject;
    }

    /**
     * 冻结审批列表
     *
     * @param accountOperateListRequest 列表参数
     * @param users                     用户
     * @return AccountOperateReviewListResponse
     */
    @Override
    public ResultObject<BasePageResponse<AccountOperateReviewListResponse>> getAccountFreezeReviewList(AccountOperateListRequest accountOperateListRequest, Users users) {
        ResultObject<BasePageResponse<AccountOperateReviewListResponse>> resultObject = new ResultObject<>();

        AccountOperateListVo accountOperateListVo = (AccountOperateListVo) this.converterObject(accountOperateListRequest, AccountOperateListVo.class);
        accountOperateListVo.setAccountStatus(PartyTermEnum.ACCOUNT_STATUS.FROZEN.name());
        List<AccountOperateListBo> accountOperateListBos = userAccountBaseService.getAccountOperateReviewList(accountOperateListVo);

        List<AccountOperateReviewListResponse> accountOperateListResponses = userAccountTransfer.transAccountOperateReviewList(accountOperateListBos, users);
        Integer totalLine = AssertUtils.isNotNull(accountOperateListBos) ? accountOperateListBos.get(0).getTotalLine() : null;
        BasePageResponse basePageResponse = BasePageResponse.getData(accountOperateListRequest.getCurrentPage(), accountOperateListRequest.getPageSize(), totalLine, accountOperateListResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    /**
     * 操作账户冻结审批
     *
     * @param accountOperateReviewRequest 审批参数
     * @param users                       用户
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postAccountFreezeReview(AccountOperateReviewRequest accountOperateReviewRequest, Users users) {
        ResultObject resultObject = new ResultObject();
        this.getLogger().info("accountOperateReviewRequest:{}", JSON.toJSONString(accountOperateReviewRequest));

        List<String> userAccountIds = accountOperateReviewRequest.getUserAccountIds();
        AssertUtils.isNotEmpty(this.getLogger(), userAccountIds, PARTY_USER_ACCOUNT_ID_IS_NOT_NULL);
        String reviewResult = accountOperateReviewRequest.getReviewResult();
        AssertUtils.isNotEmpty(this.getLogger(), reviewResult, PARTY_REVIEW_RESULT_IS_NOT_NULL);
        if (!Arrays.toString(TerminologyConfigEnum.WHETHER.values()).contains(reviewResult)) {
            throw new RequestException(PARTY_REVIEW_RESULT_FORMAT_ERROR);
        }

        userAccountIds.forEach(userAccountId -> {
            UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBoById(userAccountId);
            AssertUtils.isNotNull(this.getLogger(), userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);
            if (PartyTermEnum.ACCOUNT_STATUS.FROZEN.name().equals(userAccountBo.getAccountStatus())) {
                throw new RequestException(PARTY_THE_ACCOUNT_IS_FROZEN_AND_CANNOT_BE_OPERATED);
            }

            UserAccountOperateReviewPo userAccountOperateReviewPo = userAccountBaseService.getUserAccountOperateReview(userAccountId, PartyTermEnum.ACCOUNT_STATUS.FROZEN.name());
            if (!AssertUtils.isNotNull(userAccountOperateReviewPo) || !userAccountOperateReviewPo.getReviewStatus().equals(PartyTermEnum.ACCOUNT_REVIEW_STATUS.INITIAL.name())) {
                throw new RequestException(PARTY_ACCOUNT_APPLICATION_FREEZE_IS_NOT_FOUND_OBJECT);
            }

            userAccountOperateReviewPo.setReviewRemark(accountOperateReviewRequest.getRemark());
            userAccountOperateReviewPo.setReviewDate(DateUtils.getCurrentTime());
            userAccountOperateReviewPo.setReviewUserId(users.getUserId());
            if (TerminologyConfigEnum.WHETHER.NO.name().equals(reviewResult)) {
                userAccountOperateReviewPo.setReviewStatus(PartyTermEnum.ACCOUNT_REVIEW_STATUS.AUDIT_NO_PASS.name());
            }
            if (TerminologyConfigEnum.WHETHER.YES.name().equals(reviewResult)) {
                userAccountOperateReviewPo.setReviewStatus(PartyTermEnum.ACCOUNT_REVIEW_STATUS.AUDIT_PASS.name());
                //审核通过更改账户状态
                userAccountBo.setAccountStatus(PartyTermEnum.ACCOUNT_STATUS.FROZEN.name());
                userAccountBo.setFreezeDate(DateUtils.getCurrentTime());
                userAccountBo.setUnfreezeDate(null);
                userAccountBaseService.saveUserAccount(userAccountBo, users.getUserId());
            }
            userAccountBaseService.saveUserAccountOperateReview(userAccountOperateReviewPo, users.getUserId());

        });
        return resultObject;
    }

}

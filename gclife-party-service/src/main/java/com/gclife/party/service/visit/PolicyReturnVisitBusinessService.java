package com.gclife.party.service.visit;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.party.model.request.visit.*;
import com.gclife.party.model.response.visit.ReturnVisitStatisticsListResponse;
import com.gclife.party.model.response.visit.ReturnVisitContentResponse;
import com.gclife.party.model.response.visit.ReturnVisitTypeResponse;

import java.util.List;

/**
 * @Auther: chenjinrong
 * @Date: 19-10-21 11:01
 * @Description:
 */
public interface PolicyReturnVisitBusinessService extends BaseBusinessService {

    /**
     * 新增保单回访
     *
     * @param users
     * @param returnVisitSubmitRequest
     * @return
     */
    ResultObject addPolicyReturnVisit(Users users, PolicyReturnVisitAddRequest returnVisitSubmitRequest);

    /**
     * 保单回访列表分页查询
     *
     * @param pageRequest
     * @return
     */
    ResultObject getPolicyReturnVisitList(ReturnVisitPageRequest pageRequest);

    ResultObject getPolicyReturnVisitDetail(String returnVisitId, String returnVisitChangeId);

    /**
     * 提交保全回访
     *
     * @param policyReceiptSubmitRequest
     * @return
     */
    ResultObject postPolicyReturnVisitSubmit(Users users, PolicyReturnVisitSubmitRequest policyReceiptSubmitRequest);

    /**
     * 查询保全变更记录
     *
     * @param pageRequest
     * @return
     */
    ResultObject getReturnVisitChangeList(ReturnVisitPageRequest pageRequest);

    ResultObject postPolicyReturnVisitChangeSubmit(Users currentLoginUsers, PolicyReturnVisitChangeRequest policyReceiptSubmitRequest);

    /**
     * 修改审核列表
     *
     * @param pageRequest
     * @return
     */
    ResultObject getPolicyReturnVisitAudit(ReturnVisitChangePageRequest pageRequest);

    /**
     * 审核提交结论
     *
     * @param currentLoginUsers
     * @param policyReturnVisitAuditRequest
     * @return
     */
    ResultObject postReturnVisitAuditSubmit(Users currentLoginUsers, ReturnVisitAuditRequest policyReturnVisitAuditRequest);

    /**
     * 回访字典查询
     *
     * @return
     */
    ResultObject<ReturnVisitTypeResponse> getReturnVisitTypeEnum();

    /**
     * 回访信息查询
     */
    ResultObject<List<ReturnVisitContentResponse>> getReturnVisitList(String businessId, String businessType);

    /**
     * 回访统计数据
     *
     * @param pageRequest
     * @param currentLoginUsers
     * @return
     */
    ResultObject<ReturnVisitStatisticsListResponse> getPolicyReturnVisitStatistics(ReturnVisitPageRequest pageRequest, Users currentLoginUsers);
}

package com.gclife.party.service;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.party.model.request.CustomerMessagesRequest;
import com.gclife.party.model.request.EndorseCustomerRequest;
import com.gclife.party.model.response.CustomerAgentResponse;
import com.gclife.party.model.response.CustomerAgentsResponse;
import com.gclife.party.model.response.CustomerHistoryResponse;
import com.gclife.party.model.response.CustomerMessageResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-9-7
 * description:
 */
public interface CustomerBaseBusinessService extends BaseBusinessService {

    /**
     * 根据客户ID获取客户信息
     *
     * @return
     */
    ResultObject<CustomerMessageResponse> getBaseCustomer(String customerId, String versionNo);

    /**
     * 查询历史客户
     *
     * @param customerId 客户id
     * @param versionNo  版本号
     * @return
     */
    ResultObject<CustomerHistoryResponse> getBaseCustomerHistory(String customerId, String versionNo);


    /**
     * 模糊搜索客户
     *
     * @param appRequestHandler
     * @param customerMessagesRequest
     * @return
     */
    ResultObject<BasePageResponse<CustomerMessageResponse>> queryCustomerList(AppRequestHeads appRequestHandler, CustomerMessagesRequest customerMessagesRequest);

    /**
     * 同步客户信息
     *
     * @param endorseCustomerRequest 客户信息
     * @return ResultObject
     */
    ResultObject synchronizeBaseCustomer(EndorseCustomerRequest endorseCustomerRequest, Users users);

    /**
     * 查询客户关联的代理人客户id集合
     *
     * @param customerId 客户ID
     * @return
     */
    ResultObject<List<CustomerAgentsResponse>> getAgentCustomerRelation(String customerId);

    /**
     * 回滚客户信息
     *
     * @param customerId   客户ID
     * @param oldVersionNo 旧版本号
     * @return
     */
    ResultObject rollbackCustomer(String customerId, String oldVersionNo);

    /**
     * 获取客户信息
     *
     * @param customerIds
     * @return
     */
    ResultObject<List<CustomerMessageResponse>> getBaseCustomerList(String... customerIds);

    /**
     * 查询客户app客户信息根据
     *
     * @param type
     * @return
     */
    ResultObject<List<CustomerAgentResponse>> getClientCustomerAgentList(String type);

    /**
     * 查询疑似客户列表
     * @param users
     * @param customerAgentId
     * @return
     */
    ResultObject<List<CustomerMessageResponse>> querySuspectedCustomer(Users users, String customerAgentId);

    ResultObject<CustomerMessageResponse> queryRealCustomerByCustomerAgentId(Users users, String customerAgentId);

    ResultObject<List<CustomerAgentsResponse>> getCustomerRelationByCustomerAgentId(String customerAgentId);

    /**
     * 获取代理人客户关联的家人的所有代理人客户
     * @param customerAgentId 代理人客户ID
     * @param relationship 关系
     * @return
     */
    ResultObject<List<CustomerAgentsResponse>> getCustomerByRelationship(String customerAgentId, String relationship);
}

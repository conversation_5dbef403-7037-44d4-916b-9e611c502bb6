package com.gclife.party.service.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.GB2Alpha;
import com.gclife.party.core.jooq.tables.daos.CustomerRelationshipDao;
import com.gclife.party.core.jooq.tables.pojos.*;
import com.gclife.party.dao.CustomerManageBaseDao;
import com.gclife.party.model.bo.*;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.UserCustomerBusinessRequest;
import com.gclife.party.model.request.UserCustomerRequest;
import com.gclife.party.model.request.customer.ContactRecordRequest;
import com.gclife.party.model.request.customer.CustomerListRequest;
import com.gclife.party.model.response.customer.CustomerListResponse;
import com.gclife.party.model.response.CustomerMessageResponse;
import com.gclife.party.model.response.UserCustomerResponse;
import com.gclife.party.model.response.customer.ContactRecordResponse;
import com.gclife.party.model.response.customer.CustomerChooseResponse;
import com.gclife.party.model.response.customer.MemberResponse;
import com.gclife.party.service.CustomerManageService;
import com.gclife.party.service.base.CustomerBaseService;
import com.gclife.party.validate.parameter.CustomerParameterValidate;
import com.gclife.party.validate.transfer.CustomerBaseTransfer;
import com.gclife.platform.api.PlatformAreaApi;
import com.gclife.platform.model.response.AreaTreeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.api.PolicyMiddleApi;
import com.gclife.policy.model.request.ClientPolicyRequest;
import com.gclife.policy.model.response.ClientPolicyResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyTermEnum.CUSTOMER_TYPE.POLICY;
import static com.gclife.party.model.config.PartyTermEnum.CUSTOMER_TYPE.valueOf;


/**
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
@Service
public class CustomerManageServiceImpl extends BaseBusinessServiceImpl implements CustomerManageService {

    @Autowired
    private CustomerManageBaseDao customerManageBaseDao;
    @Autowired
    private CustomerParameterValidate customerParameterValidate;
    @Autowired
    private PolicyMiddleApi policyMiddleApi;

    @Autowired
    private CustomerBaseService customerBaseService;
    @Autowired
    private CustomerRelationshipDao customerRelationshipDao;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private CustomerBaseTransfer customerBaseTransfer;
    @Autowired
    private AttachmentApi attachmentApi;
    @Autowired
    private PlatformAreaApi platformAreaApi;

    /**
     * 客户列表
     * @param customerListRequest
     * @param users 用户
     * @return
     */
    @Override
    public ResultObject<BasePageResponse<CustomerListResponse>> listCustomer(CustomerListRequest customerListRequest, Users users) {
        ResultObject<BasePageResponse<CustomerListResponse>> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(customerListRequest.getKeyword())) {
            // 查询保障中客户
            List<String> customerIds = policyMiddleApi.listEffectiveCustomer(users.getUserId()).getData();
            if (PartyTermEnum.VALID_FLAG.effective.name().equalsIgnoreCase(customerListRequest.getPolicyStatus()) && !AssertUtils.isNotEmpty(customerIds)) {
                return resultObject;
            }
            customerListRequest.setCustomerIds(customerIds);
        }
        // 查询客户
        List<CustomerMessagesBo> customerMessagesBos = customerManageBaseDao.listCustomer(customerListRequest, users.getUserId());
        // 查询头像地址
        List<String> avatars = customerMessagesBos.stream()
                .filter(customerMessagesBo -> AssertUtils.isNotEmpty(customerMessagesBo.getAvatar()))
                .map(CustomerMessagesBo::getAvatar).collect(Collectors.toList());
        List<AttachmentResponse> attachmentResponses = new ArrayList<>();
        if (AssertUtils.isNotEmpty(avatars)) {
            ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(avatars);
            if (!AssertUtils.isResultObjectDataNull(listResultObject)) {
                attachmentResponses = listResultObject.getData();
            }
        }
        // 获取家庭成员
        List<String> customerIds = customerMessagesBos.stream().map(CustomerMessagesBo::getCustomerId).distinct().collect(Collectors.toList());
        Map<String, List<CustomerRelationshipBo>> listMemberMap = customerManageBaseDao.listMember(customerIds, null);
        // 获取所有家庭保单
        listMemberMap.values().forEach(relationshipBos -> {
            List<String> relationshipCustomerIds = relationshipBos.stream().map(CustomerRelationshipBo::getCustomerId).distinct().collect(Collectors.toList());
            customerIds.addAll(relationshipCustomerIds);
        });
        List<CustomerPolicyListBo> allPolicyBo = customerBaseService.listPolicy(customerIds);
        // 查询关联客户数据
        List<CustomerAgentBo> allCustomerAgentBos = customerBaseService.getCustomerRelationByCustomerAgentId(customerIds);
        List<String> allCustomerIds = allCustomerAgentBos.stream()
                .map(CustomerAgentBo::getCustomerAgentId).distinct().collect(Collectors.toList());
        ClientPolicyRequest clientPolicyRequest = new ClientPolicyRequest();
        clientPolicyRequest.setCustomerIds(allCustomerIds);
        clientPolicyRequest.setPolicyStatusType(PartyTermEnum.CLIENT_POLICY_STATUS.EFFECTIVE.name());
        // 查询核心系统保单
        ResultObject<List<ClientPolicyResponse>> listResultObject = policyApi.listClientPolicy(clientPolicyRequest);

        Long currentTime = DateUtils.getCurrentTime();
        for (CustomerMessagesBo customerMessagesBo : customerMessagesBos) {
            // 设置头像地址
            if (AssertUtils.isNotEmpty(attachmentResponses) && AssertUtils.isNotEmpty(customerMessagesBo.getAvatar())) {
                attachmentResponses.stream()
                        .filter(attachmentResponse -> attachmentResponse.getMediaId().equals(customerMessagesBo.getAvatar()))
                        .findFirst().ifPresent(attachmentResponse -> customerMessagesBo.setAvatarUrl(attachmentResponse.getUrl()));
            }
            // 设置年龄
            if (AssertUtils.isNotNull(customerMessagesBo.getBirthday())) {
                customerMessagesBo.setAge(DateUtils.intervalYear(customerMessagesBo.getBirthday(), currentTime));
            }
            // 设置客户标识
            if (AssertUtils.isNotNull(customerMessagesBo.getMedalNum()) && customerMessagesBo.getMedalNum() > 0) {
                customerMessagesBo.setCustomerFlag(PartyTermEnum.MedalEnum.CUSTOMER.name());
            } else {
                customerMessagesBo.setCustomerFlag(PartyTermEnum.MedalEnum.QUASI_CUSTOMER.name());
            }
            // 家庭成员数量
            List<CustomerRelationshipBo> customerRelationshipBos = listMemberMap.get(customerMessagesBo.getCustomerId());
            List<String> familyCustomerIds = customerRelationshipBos.stream()
                    .map(CustomerRelationshipBo::getRelationCustomerId).distinct().collect(Collectors.toList());
            customerMessagesBo.setMemberNum(familyCustomerIds.size() + 1);
            // 家庭保单
            familyCustomerIds.add(customerMessagesBo.getCustomerId());
            List<CustomerPolicyListBo> familyPolicyPos = allPolicyBo.stream()
                    .filter(policyBo -> familyCustomerIds.contains(policyBo.getCustomerId())).collect(Collectors.toList());
            customerMessagesBo.setFamilyPolicyNum(familyPolicyPos.size());
            if (!AssertUtils.isResultObjectDataNull(listResultObject)) {
                List<String> relationCustomerIds = allCustomerAgentBos.stream()
                        .filter(customerAgentBo -> familyCustomerIds.contains(customerAgentBo.getCustomerId()))
                        .map(CustomerAgentBo::getCustomerAgentId).distinct().collect(Collectors.toList());
                List<ClientPolicyResponse> clientPolicyResponses = listResultObject.getData().stream()
                        .filter(clientPolicyResponse -> relationCustomerIds.contains(clientPolicyResponse.getCustomerId()))
                        .collect(Collectors.toList());
                customerMessagesBo.setFamilyPolicyNum(customerMessagesBo.getFamilyPolicyNum() + clientPolicyResponses.size());
            }
        }

        // 数据转换
        List<CustomerListResponse> careerResponses = (List<CustomerListResponse>) this.converterList(customerMessagesBos, new TypeToken<List<CustomerListResponse>>() {
        }.getType());

        // 获取总页数
        Integer totalLine = AssertUtils.isNotNull(customerMessagesBos) ? customerMessagesBos.get(0).getTotalLine() : null;
        BasePageResponse basePageResponse = BasePageResponse.getData(customerListRequest.getCurrentPage(), customerListRequest.getPageSize(), totalLine, careerResponses);
        resultObject.setData(basePageResponse);

        return resultObject;
    }

    /**
     * 选择客户列表
     * @param customerListRequest
     * @param users 用户
     * @return
     */
    @Override
    public ResultObject<BasePageResponse<CustomerChooseResponse>> listChooseCustomer(CustomerListRequest customerListRequest, Users users) {
        ResultObject<BasePageResponse<CustomerChooseResponse>> resultObject = new ResultObject<>();
        // 查询本人
        CustomerAgentPo oneselfCustomerAgentPo = customerBaseService.queryOneCustomerAgent(customerListRequest.getCustomerId());
        AssertUtils.isNotNull(getLogger(), oneselfCustomerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
        // 查询家庭成员数量
        List<CustomerRelationshipBo> customerRelationshipBos = customerManageBaseDao.listMember(customerListRequest.getCustomerId(), null);
        List<String> customerIds = customerRelationshipBos.stream()
                .map(CustomerRelationshipBo::getRelationCustomerId).distinct().collect(Collectors.toList());
        customerIds.add(customerListRequest.getCustomerId());
        // 家庭成员客户ID
        customerListRequest.setCustomerIds(customerIds);
        // 查询客户
        List<CustomerAgentPo> customerAgentPos = customerManageBaseDao.listChooseCustomer(customerListRequest, users.getUserId());
        // 数据转换
        List<CustomerChooseResponse> customerChooseResponses = new ArrayList<>();
        customerAgentPos.forEach(customerAgentPo -> {
            CustomerChooseResponse customerChooseResponse = (CustomerChooseResponse) this.converterObject(customerAgentPo, CustomerChooseResponse.class);
            customerChooseResponse.setCustomerId(customerAgentPo.getCustomerAgentId());
            customerChooseResponses.add(customerChooseResponse);
        });
        // 查询勋章
        List<String> customerAgentIds = customerAgentPos.stream().map(CustomerAgentPo::getCustomerAgentId).collect(Collectors.toList());
        List<CustomerAgentMedalPo> medalPos = customerBaseService.listCustomerAgentMedal(customerAgentIds);

        // 查询地区
        List<String> areaCodes = customerChooseResponses.stream().map(CustomerChooseResponse::getHomeAreaCode).distinct().collect(Collectors.toList());
        Map<String, List<AreaTreeResponse>> areaTreeMap = new HashMap<>();
        if (AssertUtils.isNotEmpty(areaCodes)) {
            ResultObject<Map<String, List<AreaTreeResponse>>> mapResultObject = platformAreaApi.areaTreeGetBatch(areaCodes);
            if (!AssertUtils.isResultObjectDataNull(mapResultObject)) {
                areaTreeMap = mapResultObject.getData();
            }
        }

        // 查询头像地址
        List<String> avatars = customerAgentPos.stream()
                .filter(customerAgentPo -> AssertUtils.isNotEmpty(customerAgentPo.getAvatar()))
                .map(CustomerAgentPo::getAvatar).collect(Collectors.toList());
        List<AttachmentResponse> attachmentResponses = new ArrayList<>();
        if (AssertUtils.isNotEmpty(avatars)) {
            ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(avatars);
            if (!AssertUtils.isResultObjectDataNull(listResultObject)) {
                attachmentResponses = listResultObject.getData();
            }
        }
        Long currentTime = DateUtils.getCurrentTime();
        for (CustomerChooseResponse customerResponse : customerChooseResponses) {
            // 设置客户标识
            customerResponse.setCustomerFlag(PartyTermEnum.MedalEnum.QUASI_CUSTOMER.name());
            medalPos.stream()
                    .filter(medalPo -> medalPo.getCustomerId().equals(customerResponse.getCustomerId()))
                    .findFirst().ifPresent(medalPo -> customerResponse.setCustomerFlag(PartyTermEnum.MedalEnum.CUSTOMER.name()));
            // 设置头像地址
            if (AssertUtils.isNotEmpty(attachmentResponses) && AssertUtils.isNotEmpty(customerResponse.getAvatar())) {
                attachmentResponses.stream()
                        .filter(attachmentResponse -> attachmentResponse.getMediaId().equals(customerResponse.getAvatar()))
                        .findFirst().ifPresent(attachmentResponse -> customerResponse.setAvatarUrl(attachmentResponse.getUrl()));
            }
            // 设置年龄
            if (AssertUtils.isNotNull(customerResponse.getBirthday())) {
                customerResponse.setAge(DateUtils.intervalYear(customerResponse.getBirthday(), currentTime));
            }
            // 设置地区
            if (AssertUtils.isNotEmpty(areaTreeMap.get(customerResponse.getHomeAreaCode()))) {
                customerResponse.setListHomeAreaName(areaTreeMap.get(customerResponse.getHomeAreaCode()));
                customerResponse.setHomeAreaCodeName(customerBaseTransfer.areaNameGet(areaTreeMap.get(customerResponse.getHomeAreaCode())));
            }
            customerResponse.setOneselfSex(oneselfCustomerAgentPo.getSex());
        }

        // 获取总页数
        Integer totalLine = AssertUtils.isNotNull(customerAgentPos) ? customerAgentPos.get(0).getTotalLine() : null;
        BasePageResponse basePageResponse = BasePageResponse.getData(customerListRequest.getCurrentPage(), customerListRequest.getPageSize(), totalLine, customerChooseResponses);
        resultObject.setData(basePageResponse);

        return resultObject;
    }


    /**
     * 家庭成员列表
     * @param customerId 客户ID
     * @param keyword 模糊搜索关键字
     * @return
     */
    @Override
    public ResultObject<List<MemberResponse>> listMember(String customerId, String keyword) {
        // 查询家庭成员关系
        List<CustomerRelationshipBo> customerRelationshipBos = customerManageBaseDao.listMember(customerId, null);
        List<String> customerIds = customerRelationshipBos.stream()
                .map(CustomerRelationshipBo::getRelationCustomerId).distinct().collect(Collectors.toList());
        customerIds.add(customerId);
        // 查询客户信息
        List<CustomerAgentPo> customerAgentPos = customerBaseService.listCustomerAgent(customerIds);
        // 查询头像地址
        List<String> avatars = customerAgentPos.stream()
                .filter(customerMessagesBo -> AssertUtils.isNotEmpty(customerMessagesBo.getAvatar()))
                .map(CustomerAgentPo::getAvatar).collect(Collectors.toList());
        List<AttachmentResponse> tempAttachmentResponses = new ArrayList<>();
        if (AssertUtils.isNotEmpty(avatars)) {
            ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(avatars);
            if (!AssertUtils.isResultObjectDataNull(listResultObject)) {
                tempAttachmentResponses = listResultObject.getData();
            }
        }
        List<AttachmentResponse> attachmentResponses = tempAttachmentResponses;
        // 查询勋章
        List<CustomerAgentMedalPo> medalPos = customerBaseService.listCustomerAgentMedal(customerIds);
        List<MemberResponse> memberResponses = new ArrayList<>();
        // 添加本人
        customerAgentPos.stream()
                .filter(customerAgentPo -> customerId.equals(customerAgentPo.getCustomerAgentId()))
                .findFirst().ifPresent(customerAgentPo -> {
            MemberResponse memberResponse = new MemberResponse();
            ClazzUtils.copyPropertiesIgnoreNull(customerAgentPo, memberResponse);
            memberResponse.setCustomerId(customerAgentPo.getCustomerAgentId());
            memberResponse.setRelationship(PartyTermEnum.RELATIONSHIP.ONESELF.name());
            // 设置头像地址
            if (AssertUtils.isNotEmpty(attachmentResponses) && AssertUtils.isNotEmpty(memberResponse.getAvatar())) {
                attachmentResponses.stream()
                        .filter(attachmentResponse -> attachmentResponse.getMediaId().equals(memberResponse.getAvatar()))
                        .findFirst().ifPresent(attachmentResponse -> memberResponse.setAvatarUrl(attachmentResponse.getUrl()));
            }
            // 设置客户标识
            memberResponse.setCustomerFlag(PartyTermEnum.MedalEnum.QUASI_CUSTOMER.name());
            medalPos.stream()
                    .filter(medalPo -> medalPo.getCustomerId().equals(customerAgentPo.getCustomerAgentId()))
                    .findFirst().ifPresent(medalPo -> memberResponse.setCustomerFlag(PartyTermEnum.MedalEnum.CUSTOMER.name()));
            // 设置年龄
            if (AssertUtils.isNotNull(memberResponse.getBirthday())) {
                memberResponse.setAge(DateUtils.intervalYear(memberResponse.getBirthday(), DateUtils.getCurrentTime()));
            }
            memberResponses.add(memberResponse);
        });
        // 添加其他家庭成员
        customerAgentPos.forEach(customerAgentPo -> {
            customerRelationshipBos.stream()
                    .filter(customerRelationshipBo -> customerAgentPo.getCustomerAgentId().equals(customerRelationshipBo.getRelationCustomerId()))
                    .findFirst().ifPresent(customerRelationshipBo -> {
                MemberResponse memberResponse = new MemberResponse();
                ClazzUtils.copyPropertiesIgnoreNull(customerAgentPo, memberResponse);
                memberResponse.setCustomerId(customerAgentPo.getCustomerAgentId());
                if (customerRelationshipBo.getDepth() == 1) {
                    // 直接关系
                    memberResponse.setRelationship(customerRelationshipBo.getRelationship());
                } else {
                    memberResponse.setRelationship(PartyTermEnum.RELATIONSHIP.OTHER.name());
                }
                // 设置头像地址
                if (AssertUtils.isNotEmpty(attachmentResponses) && AssertUtils.isNotEmpty(memberResponse.getAvatar())) {
                    attachmentResponses.stream()
                            .filter(attachmentResponse -> attachmentResponse.getMediaId().equals(memberResponse.getAvatar()))
                            .findFirst().ifPresent(attachmentResponse -> memberResponse.setAvatarUrl(attachmentResponse.getUrl()));
                }
                // 设置客户标识
                memberResponse.setCustomerFlag(PartyTermEnum.MedalEnum.QUASI_CUSTOMER.name());
                medalPos.stream()
                        .filter(medalPo -> medalPo.getCustomerId().equals(customerAgentPo.getCustomerAgentId()))
                        .findFirst().ifPresent(medalPo -> memberResponse.setCustomerFlag(PartyTermEnum.MedalEnum.CUSTOMER.name()));
                // 设置年龄
                if (AssertUtils.isNotNull(memberResponse.getBirthday())) {
                    memberResponse.setAge(DateUtils.intervalYear(memberResponse.getBirthday(), DateUtils.getCurrentTime()));
                }
                memberResponses.add(memberResponse);
            });
        });
        if (AssertUtils.isNotEmpty(keyword)) {
            memberResponses.removeIf(memberResponse -> !(memberResponse.getName().contains(keyword) || memberResponse.getIdNo().contains(keyword)));
        }

        ResultObject<List<MemberResponse>> resultObject = new ResultObject<>();
        resultObject.setData(memberResponses);
        return resultObject;
    }

    /**
     * 移除家庭成员
     * @param oneselfCustomerId 本人客户ID
     * @param customerId 待移除成员客户ID
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject removeMember(String oneselfCustomerId, String customerId) {
        if (oneselfCustomerId.equals(customerId)) {
            throwsException(this.getLogger(), PartyErrorConfigEnum.PARTY_BUSINESS_REMOVE_ONESELF_ERROR);
        }
        // 查询家庭成员关系
        List<CustomerRelationshipPo> customerRelationshipPos = customerManageBaseDao.listCustomerRelationship(oneselfCustomerId, customerId);
        AssertUtils.isNotEmpty(getLogger(), customerRelationshipPos, PartyErrorConfigEnum.PARTY_BUSINESS_REMOVE_ERROR);
        customerRelationshipDao.delete(customerRelationshipPos);
        return ResultObject.success();
    }

    /**
     * 删除客户
     * @param customerId 客户ID
     * @return
     */
    @Override
    public ResultObject deleteCustomer(String customerId) {
        List<MedalBo> medalList = customerManageBaseDao.getMedalList(customerId);
        if (AssertUtils.isNotEmpty(medalList)) {
            throw new RequestException(PartyErrorConfigEnum.PARTY_DELETE_CUSTOMER_ERROR);
        }
        // 查询所有保单
        List<PolicyPo> policyPos = customerBaseService.listPolicy(customerId);
        if (AssertUtils.isNotEmpty(policyPos)) {
            throwsException(this.getLogger(), PartyErrorConfigEnum.PARTY_DELETE_CUSTOMER_ERROR);
        }
        // 查询图片保单
        List<PictureBatchBo> pictureListBos = customerBaseService.listPictureBatch(customerId);
        if (AssertUtils.isNotEmpty(pictureListBos)) {
            throwsException(this.getLogger(), PartyErrorConfigEnum.PARTY_DELETE_CUSTOMER_ERROR);
        }
        // 查询客户信息
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(customerId);
        customerBaseService.deleteCustomerAgent(customerAgentPo);
        // 查询家庭成员关系
        List<CustomerRelationshipPo> customerRelationshipPos = customerManageBaseDao.listCustomerRelationship(customerId, null);
        customerRelationshipDao.delete(customerRelationshipPos);
        return ResultObject.success();
    }

    @Override
    @Transactional
    public ResultObject<UserCustomerResponse> saveCustomerMessage(UserCustomerRequest userCustomerRequest, Users users) {
        ResultObject<UserCustomerResponse> resultObject = new ResultObject<>();
        try {
            customerParameterValidate.validParameterCustomer(userCustomerRequest);

            String customerId = userCustomerRequest.getCustomerId();
            CustomerAgentPo customerAgentPo = new CustomerAgentPo();
            if (AssertUtils.isNotEmpty(customerId)) {
                customerAgentPo = customerBaseService.queryOneCustomerAgent(customerId);
                AssertUtils.isNotNull(getLogger(), customerAgentPo, PartyErrorConfigEnum.PARTY_UPDATE_CUSTOMER_IS_NULL);
            }
            // 校验并保存客户
            customerBaseTransfer.checkAndSaveCustomer(userCustomerRequest, customerAgentPo, users.getUserId());
            // 保存家庭成员关系
            customerBaseTransfer.saveCustomerRelationship(customerId, userCustomerRequest.getOneselfCustomerId(),
                    customerAgentPo.getCustomerAgentId(), userCustomerRequest.getRelationship(), users.getUserId());

            //返回添加客户ID
            UserCustomerResponse userCustomerResponse = new UserCustomerResponse();
            userCustomerResponse.setCustomerId(customerAgentPo.getCustomerAgentId());
            resultObject.setData(userCustomerResponse);
        } catch (Exception e) {
            e.printStackTrace();
            setTransactionalResultObjectException(this.getLogger(), resultObject, e, PartyErrorConfigEnum.PARTY_SAVE_CUSTOMER_AGENT_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<CustomerMessageResponse> getCustomerDetail(String customerId, String oneselfCustomerId, Users users) {
        ResultObject<CustomerMessageResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), customerId, PartyErrorConfigEnum.PARTY_CUSTOMER_ID_IS_NOT_NULL);
            CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(customerId);
            AssertUtils.isNotNull(getLogger(), customerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
            if (AssertUtils.isNotNull(customerAgentPo) && AssertUtils.isNotNull(customerAgentPo.getUserId())) {
                if (!users.getUserId().equals(customerAgentPo.getUserId())) {
                    throw new RequestException(PartyErrorConfigEnum.PARTY_QUERY_CUSTOMER_MESSAGE_ERROR);
                }
            }
            CustomerMessageResponse customerMessageResponse = (CustomerMessageResponse) this.converterObject(customerAgentPo, CustomerMessageResponse.class);
            customerMessageResponse.setCustomerId(customerId);
            // 查询地区
            if (AssertUtils.isNotEmpty(customerMessageResponse.getHomeAreaCode())) {
                ResultObject<List<AreaTreeResponse>> areaResultObject = platformAreaApi.areaTreeGet(customerMessageResponse.getHomeAreaCode());
                if (!AssertUtils.isResultObjectDataNull(areaResultObject)) {
                    customerMessageResponse.setListHomeAreaName(areaResultObject.getData());
                    customerMessageResponse.setHomeAreaCodeName(customerBaseTransfer.areaNameGet(areaResultObject.getData()));
                }
            }
            // 设置年龄
            if (AssertUtils.isNotNull(customerMessageResponse.getBirthday())) {
                customerMessageResponse.setAge(DateUtils.intervalYear(customerMessageResponse.getBirthday(), DateUtils.getCurrentTime()));
            }
            // 设置头像地址
            if (AssertUtils.isNotEmpty(customerMessageResponse.getAvatar())) {
                AttachmentResponse attachmentResponse = attachmentApi.attachmentGet(customerMessageResponse.getAvatar()).getData();
                if (AssertUtils.isNotNull(attachmentResponse)) {
                    customerMessageResponse.setAvatarUrl(attachmentResponse.getUrl());
                }
            }
            // 查询勋章
            List<CustomerAgentMedalPo> customerAgentMedalPos = customerBaseService.listCustomerAgentMedal(customerId);
            if (AssertUtils.isNotEmpty(customerAgentMedalPos)) {
                customerMessageResponse.setCustomerFlag(PartyTermEnum.MedalEnum.CUSTOMER.name());
            } else {
                customerMessageResponse.setCustomerFlag(PartyTermEnum.MedalEnum.QUASI_CUSTOMER.name());
            }
            if (!AssertUtils.isNotEmpty(oneselfCustomerId)) {
                resultObject.setData(customerMessageResponse);
                return resultObject;
            }
            // 查询本人
            CustomerAgentPo oneselfCustomerAgentPo = customerBaseService.queryOneCustomerAgent(oneselfCustomerId);
            AssertUtils.isNotNull(getLogger(), oneselfCustomerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
            customerMessageResponse.setOneselfSex(oneselfCustomerAgentPo.getSex());
            // 查询家庭成员数量
            List<CustomerRelationshipBo> customerRelationshipBos = customerManageBaseDao.listMember(oneselfCustomerId, null);
            List<String> customerIds = customerRelationshipBos.stream()
                    .map(CustomerRelationshipBo::getRelationCustomerId).distinct().collect(Collectors.toList());
            customerMessageResponse.setMemberNum(customerIds.size()+1);
            // 成员关系
            if (oneselfCustomerId.equals(customerId)) {
                customerMessageResponse.setRelationship(PartyTermEnum.RELATIONSHIP.ONESELF.name());
            } else {
                customerRelationshipBos.stream()
                        .filter(relationshipBo -> relationshipBo.getRelationCustomerId().equals(customerId))
                        .findFirst().ifPresent(relationshipBo -> {
                    if (relationshipBo.getDepth() == 1) {
                        customerMessageResponse.setRelationship(relationshipBo.getRelationship());
                    } else {
                        customerMessageResponse.setRelationship(PartyTermEnum.RELATIONSHIP.OTHER.name());
                    }
                });
            }
            // 查询沟通次数
            List<CustomerContactRecordPo> contactRecordPos = customerBaseService.listContactRecord(customerId, null);
            customerMessageResponse.setContactNum(contactRecordPos.size());

            // 查询家庭保单
            customerIds.add(customerId);
            List<CustomerPolicyListBo> familyPolicyPos = customerBaseService.listPolicy(customerIds);
            customerMessageResponse.setFamilyPolicyNum(familyPolicyPos.size());
            // 个人保单
            List<String> policyIds = familyPolicyPos.stream()
                    .filter(customerPolicyListBo -> customerPolicyListBo.getCustomerId().equals(customerId))
                    .map(CustomerPolicyListBo::getPolicyId).distinct().collect(Collectors.toList());
            customerMessageResponse.setPolicyNum(policyIds.size());
            customerMessageResponse.setTotalAmount(BigDecimal.ZERO);
            if (AssertUtils.isNotEmpty(policyIds)) {
                // 查询保单总保额
                List<PolicyCoveragePo> policyCoveragePos = customerBaseService.listPolicyCoverage(policyIds);
                policyCoveragePos.forEach(policyCoveragePo -> {
                    if (AssertUtils.isNotNull(policyCoveragePo.getAmount()) && AssertUtils.isNotEmpty(policyCoveragePo.getMult())) {
                        customerMessageResponse.setTotalAmount(customerMessageResponse.getTotalAmount().add(policyCoveragePo.getAmount().multiply(new BigDecimal(policyCoveragePo.getMult()))));
                    }
                });
            }

            List<CustomerAgentBo> allCustomerAgentBos = customerBaseService.getCustomerRelationByCustomerAgentId(customerIds);
            List<String> allCustomerIds = allCustomerAgentBos.stream()
                    .map(CustomerAgentBo::getCustomerAgentId).distinct().collect(Collectors.toList());
            ClientPolicyRequest clientPolicyRequest = new ClientPolicyRequest();
            clientPolicyRequest.setCustomerIds(allCustomerIds);
            clientPolicyRequest.setPolicyStatusType(PartyTermEnum.CLIENT_POLICY_STATUS.EFFECTIVE.name());
            // 查询核心系统保单
            ResultObject<List<ClientPolicyResponse>> listResultObject = policyApi.listClientPolicy(clientPolicyRequest);
            if (!AssertUtils.isResultObjectDataNull(listResultObject)) {
                customerMessageResponse.setFamilyPolicyNum(customerMessageResponse.getFamilyPolicyNum() + listResultObject.getData().size());
                // 个人保单
                List<String> relationCustomerIds = allCustomerAgentBos.stream()
                        .filter(customerAgentBo -> customerAgentBo.getCustomerId().equals(customerId))
                        .map(CustomerAgentPo::getCustomerAgentId).distinct().collect(Collectors.toList());
                List<ClientPolicyResponse> clientPolicyResponses = listResultObject.getData().stream()
                        .filter(policyResponse -> relationCustomerIds.contains(policyResponse.getCustomerId())).collect(Collectors.toList());
                customerMessageResponse.setPolicyNum(customerMessageResponse.getPolicyNum() + clientPolicyResponses.size());
                // 总保额
                BigDecimal totalAmount = clientPolicyResponses.stream()
                        .filter(policyResponse -> AssertUtils.isNotNull(policyResponse.getTotalAmount()))
                        .map(ClientPolicyResponse::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                customerMessageResponse.setTotalAmount(customerMessageResponse.getTotalAmount().add(totalAmount));
            }
            resultObject.setData(customerMessageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, PartyErrorConfigEnum.PARTY_QUERY_CUSTOMER_MESSAGE_ERROR);
        }
        return resultObject;
    }


    @Override
    @Transactional
    public ResultObject<UserCustomerResponse> saveCustomerBusiness(UserCustomerBusinessRequest userCustomerBusiness, Users users) {
        ResultObject<UserCustomerResponse> resultObject = new ResultObject<>();
        try {
            System.out.println("=======================保存用户信息:===========================");
            System.out.println("userCustomerBusiness:" + JSON.toJSONString(userCustomerBusiness));
            System.out.println("=======================保存用户信息:===========================");
            //验证数据
            customerParameterValidate.validateCustomerBusiness(userCustomerBusiness);

            System.out.println("=======================保存用户信息1:===========================");
            // 替换掉特殊的空格
            if (AssertUtils.isNotEmpty(userCustomerBusiness.getName()) && userCustomerBusiness.getName().contains(" ")) {
                System.out.println("替换掉特殊的空格");
                userCustomerBusiness.setName(userCustomerBusiness.getName().replaceAll(" "," "));
            }
            List<CustomerAgentPo> customerPoList = customerManageBaseDao.findByIdOrFourElements(userCustomerBusiness.getCustomerId(), userCustomerBusiness.getName(),
                    userCustomerBusiness.getIdNo(), userCustomerBusiness.getBirthday(), userCustomerBusiness.getSex(), userCustomerBusiness.getUserId());
            CustomerAgentPo customerAgentPo = new CustomerAgentPo();
            if (AssertUtils.isNotEmpty(userCustomerBusiness.getOriginCustomerAgentId())) {
                CustomerAgentPo clientCustomerAgentPo = customerBaseService.queryOneCustomerAgent(userCustomerBusiness.getOriginCustomerAgentId());
                if (PartyTermEnum.CUSTOMER_TYPE.CLIENT.name().equals(clientCustomerAgentPo.getCustomerType())) {
                    customerAgentPo.setCustomerNo(clientCustomerAgentPo.getCustomerNo());
                }
            }
            if (AssertUtils.isNotEmpty(customerPoList)) {
                //若是客户APP用户，则关联其客户号
                if (PartyTermEnum.CUSTOMER_TYPE.CLIENT.name().equals(customerPoList.get(0).getCustomerType())) {
                    customerAgentPo.setCustomerNo(customerPoList.get(0).getCustomerNo());
                } else {
                    customerAgentPo = customerPoList.get(0);
                }
            }
            System.out.println("=======================保存用户信息2:===========================");
            ClazzUtils.copy(userCustomerBusiness, customerAgentPo);
            System.out.println("=======================保存用户信息3:===========================");
            System.out.println("customerAgentPo:" + JSON.toJSONString(customerAgentPo));
            System.out.println("=======================保存用户信息3:===========================");
            String groupNo = GB2Alpha.String2AlphaFirst(customerAgentPo.getName());
            System.out.println("=======================保存用户信息4:===========================");
            customerAgentPo.setGroupCode(groupNo);

            //四要素
            CustomerPo customerPo = customerBaseService.queryOneCustomerByFourElements(customerAgentPo.getName(), customerAgentPo.getIdNo(), customerAgentPo.getBirthday(), customerAgentPo.getSex());
            if (AssertUtils.isNotNull(customerPo)) {
                customerAgentPo.setCustomerNo(customerPo.getCustomerNo());
            } else {
                //四要素一致的准客户数据也要保持一致的客户号
                CustomerAgentPo customerAgentPo1 = customerBaseService.queryOneCustomerAgentByFourElements(customerAgentPo.getName(),
                        customerAgentPo.getIdNo(), customerAgentPo.getBirthday(), customerAgentPo.getSex(), null);
                if (AssertUtils.isNotNull(customerAgentPo1) && !customerAgentPo1.getCustomerAgentId().equals(customerAgentPo.getCustomerAgentId())) {
                    customerAgentPo.setCustomerNo(customerAgentPo1.getCustomerNo());
                }
            }

            //设置客户类型为policy
            customerAgentPo.setCustomerType(POLICY.name());

            customerBaseService.saveCustomerAgent(customerAgentPo);
            System.out.println("=======================保存用户信息6:===========================");
            //添加  用户 勋章
            this.saveMedal(userCustomerBusiness.getMedalNo(), customerAgentPo.getCustomerAgentId(), userCustomerBusiness.getUserId(), customerAgentPo);
            System.out.println("=======================保存用户信息7:===========================");
            UserCustomerResponse userCustomerResponse = new UserCustomerResponse();
            userCustomerResponse.setCustomerId(customerAgentPo.getCustomerAgentId());
            userCustomerResponse.setIdType(customerAgentPo.getIdType());
            userCustomerResponse.setIdNo(customerAgentPo.getIdNo());
            resultObject.setData(userCustomerResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PartyErrorConfigEnum.PARTY_SAVE_CUSTOMER_AGENT_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<List<UserCustomerResponse>> saveCustomerBusinessList(List<UserCustomerBusinessRequest> userCustomerBusinessList, Users users) {
        ResultObject<List<UserCustomerResponse>> resultObject = new ResultObject<>();
        List<UserCustomerResponse> customerResponses = new ArrayList<>();
        userCustomerBusinessList.forEach(userCustomerBusinessRequest -> {
            ResultObject<UserCustomerResponse> responseResultObject = saveCustomerBusiness(userCustomerBusinessRequest, users);
            customerResponses.add(responseResultObject.getData());
        });
        resultObject.setData(customerResponses);
        return resultObject;
    }

    @Override
    public ResultObject<List<UserCustomerResponse>> updateCustomerBusinessList(List<UserCustomerBusinessRequest> userCustomerBusinessList, Users currentLoginUsers) {
        ResultObject<List<UserCustomerResponse>> resultObject = new ResultObject<>();
        List<UserCustomerResponse> customerResponses = new ArrayList<>();
        userCustomerBusinessList.forEach(userCustomerBusinessRequest -> {
            ResultObject<UserCustomerResponse> responseResultObject = updateCustomerBusiness(userCustomerBusinessRequest, currentLoginUsers);
            customerResponses.add(responseResultObject.getData());
        });
        resultObject.setData(customerResponses);
        return resultObject;
    }

    @Override
    public ResultObject<UserCustomerResponse> updateCustomerBusiness(UserCustomerBusinessRequest userCustomerBusinessRequest, Users currentLoginUsers) {
        ResultObject<UserCustomerResponse> resultObject = new ResultObject<>();
        //验证数据
        customerParameterValidate.validateCustomerBusinessSingle(userCustomerBusinessRequest);
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(userCustomerBusinessRequest.getCustomerId());
        AssertUtils.isNotNull(getLogger(), customerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
        //保存代理人客户
        ClazzUtils.copyPropertiesIgnoreNull(userCustomerBusinessRequest, customerAgentPo);
        String groupNo = GB2Alpha.String2AlphaFirst(customerAgentPo.getName());
        customerAgentPo.setGroupCode(groupNo);
        customerBaseService.saveCustomerAgent(customerAgentPo);
        //保存客户
        CustomerPo customerPo = customerBaseService.queryOneCustomerByCustomerNo(customerAgentPo.getCustomerNo());
        if (AssertUtils.isNotNull(customerPo)) {
//            AssertUtils.isNotNull(getLogger(),customerPo,PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
            ClazzUtils.copyPropertiesIgnoreNull(customerAgentPo, customerPo);
            customerBaseService.saveCustomerPo(customerPo);
        }
        UserCustomerResponse userCustomerResponse = new UserCustomerResponse();
        userCustomerResponse.setCustomerId(customerAgentPo.getCustomerAgentId());
        userCustomerResponse.setIdType(customerAgentPo.getIdType());
        userCustomerResponse.setIdNo(customerAgentPo.getIdNo());
        resultObject.setData(userCustomerResponse);
        return resultObject;
    }

    /**
     * 根据主键修改Customer客户(不包含CustomerAgent)
     *
     * @param userCustomerBusiness
     * @param users
     * @return
     */
    @Override
    @Transactional
    public ResultObject<UserCustomerResponse> updateRealCustomerBusinessSingle(UserCustomerBusinessRequest userCustomerBusiness, Users users) {
        ResultObject<UserCustomerResponse> resultObject = new ResultObject<>();
        try {
            //验证数据
            customerParameterValidate.validateCustomerBusinessSingle(userCustomerBusiness);
            //保存客户
            CustomerPo customerPo = customerBaseService.queryOneCustomer(userCustomerBusiness.getCustomerId(), null);
            if (AssertUtils.isNotNull(customerPo)) {
                ClazzUtils.copy(userCustomerBusiness, customerPo);
                String groupNo = GB2Alpha.String2AlphaFirst(customerPo.getName());
                customerPo.setGroupCode(groupNo);
                customerBaseService.saveCustomerPo(customerPo);

                CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(userCustomerBusiness.getOriginCustomerAgentId());
                if (AssertUtils.isNotNull(customerAgentPo)) {
                    customerAgentPo.setCustomerNo(customerPo.getCustomerNo());
                    customerBaseService.saveCustomerAgent(customerAgentPo);
                }
                UserCustomerResponse userCustomerResponse = new UserCustomerResponse();
                userCustomerResponse.setCustomerId(customerPo.getCustomerId());
                userCustomerResponse.setIdType(customerPo.getIdType());
                userCustomerResponse.setIdNo(customerPo.getIdNo());
                resultObject.setData(userCustomerResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PartyErrorConfigEnum.PARTY_SAVE_CUSTOMER_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    /**
     * 用所选择的CustomerId数据覆盖CustomerAgent数据
     *
     * @param customerId
     * @param customerAgentId
     * @param users
     * @return
     */
    @Override
    @Transactional
    public ResultObject<UserCustomerResponse> updateCustomerAgentDataByCustomer(String customerId, String customerAgentId, Users users) {
        CustomerPo customerPo = customerBaseService.queryOneCustomer(customerId, null);
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(customerAgentId);
        if (AssertUtils.isNotNull(customerPo) && AssertUtils.isNotNull(customerAgentPo)) {
            customerAgentPo.setCustomerNo(customerPo.getCustomerNo());
            customerBaseService.saveCustomerAgent(customerAgentPo);
        }
        return ResultObject.success();
    }

    /**
     * 保存沟通记录
     * @param contactRecordRequest
     * @param users 用户
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject saveContact(ContactRecordRequest contactRecordRequest, Users users) {
        AssertUtils.isNotEmpty(getLogger(), contactRecordRequest.getCustomerId(), PartyErrorConfigEnum.PARTY_CUSTOMER_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), contactRecordRequest.getContactTimeFormat(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_CONTACT_TIME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), contactRecordRequest.getContactEvent(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_CONTACT_EVENT_IS_NOT_NULL);

        CustomerContactRecordPo contactRecordPo = new CustomerContactRecordPo();
        if (AssertUtils.isNotEmpty(contactRecordRequest.getContactRecordId())) {
            // 查询沟通记录
            contactRecordPo = customerBaseService.queryContactRecord(contactRecordRequest.getContactRecordId());
            AssertUtils.isNotNull(getLogger(), contactRecordPo, PartyErrorConfigEnum.PARTY_BUSINESS_CONTACT_RECORD_IS_NOT_FOUND);
            // 查询沟通记录附件
            List<CustomerContactAttachmentPo> contactAttachmentPos = customerBaseService.listContactAttachment(contactRecordRequest.getContactRecordId());
            if (AssertUtils.isNotEmpty(contactAttachmentPos)) {
                customerBaseService.deleteContactAttachment(contactAttachmentPos);
            }
        }
        ClazzUtils.copyPropertiesIgnoreNull(contactRecordRequest, contactRecordPo);
        contactRecordPo.setContactTime(DateUtils.stringToTime(contactRecordRequest.getContactTimeFormat(), DateUtils.FORMATE20));
        if (!PartyTermEnum.CONTACT_EVENT.RENEWAL.name().equals(contactRecordPo.getContactEvent())) {
            contactRecordPo.setBusinessId(null);
        }
        // 保存沟通记录
        customerBaseService.saveCustomerRecord(contactRecordPo, users.getUserId());
        String contactRecordId = contactRecordPo.getContactRecordId();
        if (AssertUtils.isNotEmpty(contactRecordRequest.getAttachmentIds())) {
            List<CustomerContactAttachmentPo> customerContactAttachmentPos = new ArrayList<>();
            contactRecordRequest.getAttachmentIds().forEach(attachmentId -> {
                CustomerContactAttachmentPo contactAttachmentPo = new CustomerContactAttachmentPo();
                contactAttachmentPo.setContactRecordId(contactRecordId);
                contactAttachmentPo.setAttachmentId(attachmentId);
                customerContactAttachmentPos.add(contactAttachmentPo);
            });
            // 保存沟通附件
            customerBaseService.addContactAttachment(customerContactAttachmentPos, users.getUserId());
        }

        return ResultObject.success();
    }

    /**
     * 查询沟通记录列表
     * @param customerId 客户ID
     * @param contactEvent 沟通事件
     * @return
     */
    @Override
    public ResultObject<List<ContactRecordResponse>> listContactRecord(String customerId, String contactEvent) {
        ResultObject<List<ContactRecordResponse>> resultObject = ResultObject.success();
        // 查询沟通记录
        List<CustomerContactRecordPo> contactRecordPos = customerBaseService.listContactRecord(customerId, contactEvent);
        if (AssertUtils.isNotEmpty(contactRecordPos)) {
            List<ContactRecordResponse> contactRecordResponses = (List<ContactRecordResponse>) this.converterList(
                    contactRecordPos, new TypeToken<List<ContactRecordResponse>>() {}.getType()
            );
            // 查询地区
            List<String> contactAreaCodes = contactRecordPos.stream()
                    .filter(contactRecordPo -> AssertUtils.isNotEmpty(contactRecordPo.getContactAreaCode()))
                    .map(CustomerContactRecordPo::getContactAreaCode).collect(Collectors.toList());
            Map<String, List<AreaTreeResponse>> listMap = platformAreaApi.areaTreeGetBatch(contactAreaCodes).getData();
            contactRecordResponses.forEach(contactRecordResponse -> {
                if (AssertUtils.isNotEmpty(contactRecordResponse.getContactAreaCode())) {
                    contactRecordResponse.setListAreaName(listMap.get(contactRecordResponse.getContactAreaCode()));
                    contactRecordResponse.setContactAreaName(customerBaseTransfer.areaNameGet(listMap.get(contactRecordResponse.getContactAreaCode())));
                }
                if (AssertUtils.isNotNull(contactRecordResponse.getContactTime())) {
                    contactRecordResponse.setContactTimeFormat(DateUtils.timeStrToString(contactRecordResponse.getContactTime(), DateUtils.FORMATE20));
                }
            });
            resultObject.setData(contactRecordResponses);
        }
        return resultObject;
    }

    /**
     * 查询沟通记录
     * @param contactRecordId 沟通记录ID
     * @return
     */
    @Override
    public ResultObject<ContactRecordResponse> queryContactRecord(String contactRecordId) {
        // 查询沟通记录
        CustomerContactRecordPo contactRecordPo = customerBaseService.queryContactRecord(contactRecordId);
        AssertUtils.isNotNull(getLogger(), contactRecordPo, PartyErrorConfigEnum.PARTY_BUSINESS_CONTACT_RECORD_IS_NOT_FOUND);
        ContactRecordResponse contactRecordResponse = (ContactRecordResponse) this.converterObject(contactRecordPo, ContactRecordResponse.class);
        if (AssertUtils.isNotNull(contactRecordResponse.getContactTime())) {
            contactRecordResponse.setContactTimeFormat(DateUtils.timeStrToString(contactRecordResponse.getContactTime(), DateUtils.FORMATE20));
        }
        // 查询客户信息
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(contactRecordPo.getCustomerId());
        AssertUtils.isNotNull(getLogger(), customerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
        contactRecordResponse.setName(customerAgentPo.getName());
        // 查询附件
        List<CustomerContactAttachmentPo> contactAttachmentPos = customerBaseService.listContactAttachment(contactRecordPo.getContactRecordId());
        List<String> attachmentIds = contactAttachmentPos.stream().map(CustomerContactAttachmentPo::getAttachmentId).collect(Collectors.toList());
        contactRecordResponse.setAttachmentIds(attachmentIds);
        // 查询地区
        if (AssertUtils.isNotEmpty(contactRecordPo.getContactAreaCode())) {
            ResultObject<List<AreaTreeResponse>> areaResultObject = platformAreaApi.areaTreeGet(contactRecordPo.getContactAreaCode());
            if (!AssertUtils.isResultObjectDataNull(areaResultObject)) {
                contactRecordResponse.setListAreaName(areaResultObject.getData());
                contactRecordResponse.setContactAreaName(customerBaseTransfer.areaNameGet(areaResultObject.getData()));
            }
        }
        ResultObject<ContactRecordResponse> resultObject = ResultObject.success();
        resultObject.setData(contactRecordResponse);
        return resultObject;
    }

    /**
     * 删除沟通记录
     * @param contactRecordId 沟通记录ID
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject deleteContactRecord(String contactRecordId) {
        // 查询沟通记录
        CustomerContactRecordPo contactRecordPo = customerBaseService.queryContactRecord(contactRecordId);
        AssertUtils.isNotNull(getLogger(), contactRecordPo, PartyErrorConfigEnum.PARTY_BUSINESS_CONTACT_RECORD_IS_NOT_FOUND);
        customerBaseService.deleteContactRecord(contactRecordPo);
        // 查询沟通记录附件
        List<CustomerContactAttachmentPo> contactAttachmentPos = customerBaseService.listContactAttachment(contactRecordId);
        if (AssertUtils.isNotEmpty(contactAttachmentPos)) {
            customerBaseService.deleteContactAttachment(contactAttachmentPos);
        }
        return ResultObject.success();
    }

    /**
     * 生日客户列表
     * @return
     * @param users
     */
    @Override
    public ResultObject<List<CustomerListResponse>> listBirthdayCustomer(Users users) {
        ResultObject<List<CustomerListResponse>> resultObject = new ResultObject<>();
        List<CustomerMessagesBo> customerMessagesBos = customerManageBaseDao.listBirthdayCustomer(users.getUserId());
        if (!AssertUtils.isNotEmpty(customerMessagesBos)) {
            return resultObject;
        }
        // 数据转换
        List<CustomerListResponse> customerListResponses = (List<CustomerListResponse>) this.converterList(
                customerMessagesBos, new TypeToken<List<CustomerListResponse>>() {}.getType()
        );
        // 查询头像地址
        List<String> avatars = customerMessagesBos.stream()
                .filter(customerMessagesBo -> AssertUtils.isNotEmpty(customerMessagesBo.getAvatar()))
                .map(CustomerMessagesBo::getAvatar).collect(Collectors.toList());
        List<AttachmentResponse> attachmentResponses = new ArrayList<>();
        if (AssertUtils.isNotEmpty(avatars)) {
            ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(avatars);
            if (!AssertUtils.isResultObjectDataNull(listResultObject)) {
                attachmentResponses = listResultObject.getData();
            }
        }
        Long currentTime = DateUtils.getCurrentTime();
        for (CustomerListResponse customerListResponse : customerListResponses) {
            // 设置头像地址
            if (AssertUtils.isNotEmpty(attachmentResponses) && AssertUtils.isNotEmpty(customerListResponse.getAvatar())) {
                attachmentResponses.stream()
                        .filter(attachmentResponse -> attachmentResponse.getMediaId().equals(customerListResponse.getAvatar()))
                        .findFirst().ifPresent(attachmentResponse -> customerListResponse.setAvatarUrl(attachmentResponse.getUrl()));
            }
            String pattern = "MM/dd";
            // 设置生日
            customerListResponse.setBirthdayStr(DateUtils.timeStrToString(customerListResponse.getBirthday(), pattern));
            // 设置距离生日天数
            long intervalDay = DateUtils.intervalDay(DateUtils.stringToTime(DateUtils.timeStrToString(currentTime, pattern), pattern),
                    DateUtils.stringToTime(customerListResponse.getBirthdayStr(), pattern));
            customerListResponse.setBeforeBirthday(intervalDay);
            // 设置年龄
            if (AssertUtils.isNotNull(customerListResponse.getBirthday())) {
                customerListResponse.setAge(DateUtils.intervalYear(customerListResponse.getBirthday(), currentTime));
                if (intervalDay != 0) {
                    customerListResponse.setAge(customerListResponse.getAge() + 1);
                }
            }
            // 是否已送祝福
            CustomerContactRecordPo contactRecordPo = customerBaseService.queryOneContactRecord(customerListResponse.getCustomerId(), PartyTermEnum.CONTACT_EVENT.BIRTHDAY_WISHES.name());
            if (AssertUtils.isNotNull(contactRecordPo) && contactRecordPo.getContactTime() > DateUtils.addStringMonthRT(currentTime, -2)) {
                customerListResponse.setBlessFlag(TerminologyConfigEnum.WHETHER.YES.name());
            } else {
                customerListResponse.setBlessFlag(TerminologyConfigEnum.WHETHER.NO.name());
            }
        }

        resultObject.setData(customerListResponses);
        return resultObject;
    }

    /**
     * 保存客户手机号
     * @param customerId 客户ID
     * @param mobile 手机号
     * @return
     */
    @Override
    public ResultObject saveCustomerMobile(String customerId, String mobile) {
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(customerId);
        AssertUtils.isNotNull(getLogger(), customerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
        customerAgentPo.setMobile(mobile);
        customerBaseService.saveCustomerAgent(customerAgentPo);
        CustomerPo customerPo = customerBaseService.queryOneCustomerByCustomerNo(customerAgentPo.getCustomerNo());
        if (AssertUtils.isNotNull(customerPo)) {
            customerPo.setMobile(mobile);
            customerBaseService.saveCustomerPo(customerPo);
        }
        return ResultObject.success();
    }


    /**
     * 添加 勋章
     *
     * @param medalNo
     * @param customerId
     */
    public void saveMedal(String medalNo, String customerId, String userId, CustomerAgentPo customerAgentPo) {
        if (AssertUtils.isNotNull(medalNo)) {
            CustomerMessageBo customerMessageBo = customerManageBaseDao.getCustomerMessage(medalNo, userId, customerId);
            MedalBo medalBo = customerManageBaseDao.getMedalIdByOn(medalNo);

            if (!AssertUtils.isNotNull(customerMessageBo) && AssertUtils.isNotNull(medalBo)) {

                CustomerAgentMedalPo customerAgentMedalPo = new CustomerAgentMedalPo();
                customerAgentMedalPo.setCustomerId(customerId);
                customerAgentMedalPo.setMedalId(medalBo.getMedalId());
                customerAgentMedalPo.setCreatedUserId(userId);
                customerBaseService.saveCustomerAgentMedal(customerAgentMedalPo);
            }
//            //查询是否有疑似客户
//            List<CustomerPo> customerPoList=customerBaseService.queryCustomerPoByIdNo(customerAgentPo.getIdType(),customerAgentPo.getIdNo());
//            if(AssertUtils.isNotEmpty(customerPoList)){
//                //是疑似客户
//                customerPoList.forEach(customerPo -> {
//                    CustomerResemblePo customerResemblePo=customerBaseTransfer.transformCustomerResemble(customerPo,customerAgentPo);
//                    customerBaseService.saveCustomerResemble(customerResemblePo);
//                });
//            }else{
//                //非疑似客户，成为客户
//                CustomerPo customerPo= (CustomerPo) converterObject(customerAgentPo,CustomerPo.class);
//                customerBaseService.saveCustomerPo(customerPo);
//            }

            CustomerPo customerPo = customerBaseService.queryOneCustomerByCustomerNo(customerAgentPo.getCustomerNo());
            if (!AssertUtils.isNotNull(customerPo)) {
                customerPo = new CustomerPo();
                ClazzUtils.copyPropertiesIgnoreNull(customerAgentPo, customerPo);
                customerBaseService.saveCustomerPo(customerPo);
            }
        }
    }

}
package com.gclife.party.service.account.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentBaseResponse;
import com.gclife.agent.model.response.AgentKeyWordResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.party.core.jooq.tables.pojos.UserAccountOperateReviewPo;
import com.gclife.party.model.bo.AccountInputRecordBo;
import com.gclife.party.model.bo.AccountOutputRecordBo;
import com.gclife.party.model.bo.UserAccountBo;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.account.AccountInputRecordRequest;
import com.gclife.party.model.request.account.AccountRequest;
import com.gclife.party.model.response.account.AccountDetailResponse;
import com.gclife.party.model.response.account.AccountInputRecordResponse;
import com.gclife.party.model.response.account.AccountOutputRecordResponse;
import com.gclife.party.model.response.account.AccountResponse;
import com.gclife.party.model.vo.AccountInputRecordVo;
import com.gclife.party.model.vo.AccountVo;
import com.gclife.party.service.account.AccountService;
import com.gclife.party.service.base.UserAccountBaseService;
import com.gclife.party.service.base.UserAccountTransactionRecordBaseService;
import com.google.common.reflect.TypeToken;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyErrorConfigEnum.*;

/**
 * <AUTHOR>
 * @description
 * @date 2019-07-12 13:03
 */
@Service
public class AccountServiceImpl extends BaseBusinessServiceImpl implements AccountService {
    @Autowired
    private UserAccountBaseService userAccountBaseService;
    @Autowired
    private AttachmentApi attachmentApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private UserAccountTransactionRecordBaseService userAccountTransactionRecordBaseService;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;

    @Override
    public ResultObject<BasePageResponse<AccountResponse>> queryAccount(AccountRequest accountRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<AccountResponse>> resultObject = new ResultObject<>();
        AccountVo accountVo = (AccountVo) this.converterObject(accountRequest, AccountVo.class);
        if (AssertUtils.isNotEmpty(accountRequest.getKeyword())) {
            List<AgentKeyWordResponse> agentKeyWordResponseList = agentApi.loadAllAgentsByKeyword(accountRequest.getKeyword()).getData();
            if (!AssertUtils.isNotEmpty(agentKeyWordResponseList)) {
                resultObject.setData(new BasePageResponse<>());
                return resultObject;
            }
            List<String> agentIdList = agentKeyWordResponseList.stream().map(AgentKeyWordResponse::getAgentId).distinct().collect(Collectors.toList());
            accountVo.setAgentIdList(agentIdList);
        }
        //只查询激活和冻结的账户
        List<String> accountStatusList = new ArrayList<>();
        accountStatusList.add(PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name());
        accountStatusList.add(PartyTermEnum.ACCOUNT_STATUS.FROZEN.name());
        //过滤当前选中的状态
        if (AssertUtils.isNotEmpty(accountVo.getAccountStatus())) {
            accountStatusList = accountStatusList.stream().filter(accountStatus -> accountStatus.equals(accountVo.getAccountStatus())).collect(Collectors.toList());
        }
        accountVo.setAccountStatusLisat(accountStatusList);
        List<UserAccountBo> userAccountBoList = userAccountBaseService.queryAccount(accountVo);
        if (!AssertUtils.isNotEmpty(userAccountBoList)) {
            resultObject.setData(new BasePageResponse<>());
            return resultObject;
        }
        List<AccountResponse> accountResponseList = (List<AccountResponse>) this.converterList(userAccountBoList, new TypeToken<List<AccountResponse>>() {
        }.getType());

        List<String> userIds = userAccountBoList.stream().map(UserAccountBo::getUserId).distinct().collect(Collectors.toList());

        AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
        applyAgentRequest.setListAgentId(userIds);
        List<AgentResponse> agentResponses = agentApi.agentsGet(applyAgentRequest).getData();
        if (AssertUtils.isNotEmpty(agentResponses)) {
            accountResponseList.forEach(accountResponse -> agentResponses.stream().filter(agentResponse -> accountResponse.getUserId().equals(agentResponse.getUserId()))
                    .findFirst().ifPresent(agentResponse -> accountResponse.setAgentStatus(agentResponse.getAgentStatus())));
        }
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(userAccountBoList) ? userAccountBoList.get(0).getTotalLine() : null;

        BasePageResponse basePageResponse = BasePageResponse.getData(accountRequest.getCurrentPage(), accountRequest.getPageSize(), totalLine, accountResponseList);

        resultObject.setData(basePageResponse);
        return resultObject;
    }


    @Override
    public ResultObject<BasePageResponse<AccountInputRecordResponse>> inputRecord(AccountInputRecordRequest accountInputRecordRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<AccountInputRecordResponse>> resultObject = new ResultObject<>();

//        AssertUtils.isNotEmpty(this.getLogger(), accountInputRecordRequest.getUserAccountId(), PARTY_USER_ACCOUNT_ID_IS_NOT_NULL);

        AccountInputRecordVo accountInputRecordVo = (AccountInputRecordVo) this.converterObject(accountInputRecordRequest, AccountInputRecordVo.class);
        List<AccountInputRecordBo> accountInputRecordBoList = userAccountTransactionRecordBaseService.inputRecord(accountInputRecordVo);
        if (!AssertUtils.isNotEmpty(accountInputRecordBoList)) {
            resultObject.setData(new BasePageResponse<>());
            return resultObject;
        }
        List<AccountInputRecordResponse> accountResponseList = (List<AccountInputRecordResponse>) this.converterList(accountInputRecordBoList, new TypeToken<List<AccountInputRecordResponse>>() {
        }.getType());
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(accountInputRecordBoList) ? accountInputRecordBoList.get(0).getTotalLine() : null;
        BasePageResponse basePageResponse = BasePageResponse.getData(accountInputRecordRequest.getCurrentPage(), accountInputRecordRequest.getPageSize(), totalLine, accountResponseList);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<AccountOutputRecordResponse>> outputRecord(AccountInputRecordRequest accountInputRecordRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<AccountOutputRecordResponse>> resultObject = new ResultObject<>();

//        AssertUtils.isNotEmpty(this.getLogger(), accountInputRecordRequest.getUserAccountId(), PARTY_USER_ACCOUNT_ID_IS_NOT_NULL);

        AccountInputRecordVo accountInputRecordVo = (AccountInputRecordVo) this.converterObject(accountInputRecordRequest, AccountInputRecordVo.class);

        List<AccountOutputRecordBo> accountInputRecordBoList = userAccountTransactionRecordBaseService.outputRecord(accountInputRecordVo);

        if (!AssertUtils.isNotEmpty(accountInputRecordBoList)) {
            resultObject.setData(new BasePageResponse<>());
            return resultObject;
        }
        List<AccountOutputRecordResponse> accountResponseList = (List<AccountOutputRecordResponse>) this.converterList(accountInputRecordBoList, new TypeToken<List<AccountOutputRecordResponse>>() {
        }.getType());
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(accountInputRecordBoList) ? accountInputRecordBoList.get(0).getTotalLine() : null;
        BasePageResponse basePageResponse = BasePageResponse.getData(accountInputRecordRequest.getCurrentPage(), accountInputRecordRequest.getPageSize(), totalLine, accountResponseList);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    @Override
    public ResultObject<AccountDetailResponse> queryAccountDetail(String userAccountId, Users currentLoginUsers) {
        ResultObject<AccountDetailResponse> resultObject = new ResultObject<>();

        UserAccountBo userAccountBo = userAccountBaseService.queryAccountById(userAccountId);
        AssertUtils.isNotNull(this.getLogger(), userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);
        AgentBaseResponse agentBaseResponse = agentBaseAgentApi.queryOneAgentById(userAccountBo.getUserId()).getData();
        if (AssertUtils.isNotNull(agentBaseResponse)) {
            userAccountBo.setAgentCode(agentBaseResponse.getAgentCode());
        }
        AccountDetailResponse accountDetailResponse = (AccountDetailResponse) this.converterObject(userAccountBo, AccountDetailResponse.class);
        UserAccountOperateReviewPo userAccountOperateReview = userAccountBaseService.getUserAccountOperateReview(userAccountId, null);
        if (AssertUtils.isNotNull(userAccountOperateReview)) {
            accountDetailResponse.setApplyRemark(userAccountOperateReview.getApplyRemark());
        }
        accountDetailResponse.setFreezeReasonCode(userAccountBo.getFreezeReasonCode());
        accountDetailResponse.setAgentStatus(agentBaseResponse.getAgentStatus());
        resultObject.setData(accountDetailResponse);
        return resultObject;
    }

    @Override
    public void exportAccount(HttpServletResponse httpServletResponse, AccountRequest accountRequest, Users currentLoginUsers) {
        //由输入流得到工作簿
        ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentApi.templateGet("EXPORT_ACCOUNT_TEMPLATE");
        AssertUtils.isResultObjectDataNull(this.getLogger(), attachmentRespFcResultObject);
        AttachmentResponse attachmentRespFc = attachmentRespFcResultObject.getData();
        accountRequest.setPageSize(1000);
        accountRequest.setKeyword(accountRequest.getAccountName());
        try {
            URL url = new URL(attachmentRespFc.getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            boolean booleanWhile = true;
            XSSFSheet sheet = workbook.getSheetAt(0);
            while (booleanWhile) {
                ResultObject<BasePageResponse<AccountResponse>> responseResultObject = this.queryAccount(accountRequest, currentLoginUsers);
                AssertUtils.isResultObjectError(this.getLogger(), responseResultObject);
                BasePageResponse<AccountResponse> basePageResponse = responseResultObject.getData();
                List<AccountResponse> accountResponseList = basePageResponse.getData();
                if (!AssertUtils.isNotEmpty(accountResponseList)) {
                    break;
                }
                String json = JackSonUtils.toJson(accountResponseList);
                accountResponseList = JSON.parseArray(json, AccountResponse.class);
                int i = (accountRequest.getCurrentPage() - 1) * accountRequest.getPageSize() + 2;
                int number = (accountRequest.getCurrentPage() - 1) * accountRequest.getPageSize() + 1;
                if (AssertUtils.isNotEmpty(accountResponseList)) {
                    for (AccountResponse accountResponse : accountResponseList) {
                        Row writeRow = sheet.getRow(i);
                        if (!AssertUtils.isNotNull(writeRow)) {
                            Row sheetRow = sheet.getRow(i - 1);
                            writeRow = sheet.createRow(i);
                            copyRow(workbook, sheet, sheetRow, writeRow, false);
                        }
                        writeRow.getCell(0).setCellValue(number);
                        String userAccountNo = accountResponse.getUserAccountNo();
                        writeRow.getCell(1).setCellValue(userAccountNo);
                        String accountName = accountResponse.getAccountName();
                        writeRow.getCell(2).setCellValue(accountName);
                        String agentCode = accountResponse.getAgentCode();
                        writeRow.getCell(3).setCellValue(agentCode);
                        BigDecimal revenueAmount = accountResponse.getRevenueAmount();
                        writeRow.getCell(4).setCellValue(revenueAmount.toString());
                        BigDecimal expendedAmount = accountResponse.getExpendedAmount();
                        writeRow.getCell(5).setCellValue(expendedAmount.toString());
                        BigDecimal residueAmount = accountResponse.getResidueAmount();
                        writeRow.getCell(6).setCellValue(residueAmount.toString());
                        BigDecimal disableAmount = accountResponse.getDisableAmount();
                        writeRow.getCell(7).setCellValue(disableAmount.toString());
                        BigDecimal withholdingTax = accountResponse.getWithholdingTax();
                        writeRow.getCell(8).setCellValue(withholdingTax.toString());
                        BigDecimal availableAmount = accountResponse.getAvailableAmount();
                        writeRow.getCell(9).setCellValue(availableAmount.toString());
                        String accountStatusName = accountResponse.getAccountStatusName();
                        writeRow.getCell(10).setCellValue(accountStatusName);
                        i++;
                        number++;
                    }
                }
                if (accountRequest.getCurrentPage().intValue() >= basePageResponse.getTotalPage().intValue()) {
                    booleanWhile = false;
                } else {
                    accountRequest.setCurrentPage(accountRequest.getCurrentPage() + 1);
                }
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(attachmentRespFc.getTemplateName() + ".xlsx", "UTF-8"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(PARTY_EXPORT_USER_ACCOUNT_ERROR);
        }
    }

    public static void copyRow(Workbook wb, Sheet sheet, Row fromRow, Row toRow, boolean copyValueFlag) {
        toRow.setHeight(fromRow.getHeight());
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress cellRangeAddress = sheet.getMergedRegion(i);
            if (cellRangeAddress.getFirstRow() == fromRow.getRowNum()) {
                CellRangeAddress newCellRangeAddress = new CellRangeAddress(toRow.getRowNum(), (toRow.getRowNum() + (cellRangeAddress.getLastRow() - cellRangeAddress.getFirstRow())), cellRangeAddress.getFirstColumn(), cellRangeAddress.getLastColumn());
                sheet.addMergedRegion(newCellRangeAddress);
            }
        }
        for (Iterator cellIt = fromRow.cellIterator(); cellIt.hasNext(); ) {
            Cell tmpCell = (Cell) cellIt.next();
            Cell newCell = toRow.createCell(tmpCell.getColumnIndex());
            copyCell(wb, tmpCell, newCell, copyValueFlag);
        }
    }

    public static void copyCell(Workbook wb, Cell srcCell, Cell distCell, boolean copyValueFlag) {
        CellStyle newstyle = wb.createCellStyle();
        newstyle.cloneStyleFrom(srcCell.getCellStyle());
        distCell.setCellStyle(newstyle);
        if (srcCell.getCellComment() != null) {
            distCell.setCellComment(srcCell.getCellComment());
        }
        int srcCellType = srcCell.getCellType();
        distCell.setCellType(srcCellType);
        if (copyValueFlag) {
            if (srcCellType == HSSFCell.CELL_TYPE_NUMERIC) {
                if (HSSFDateUtil.isCellDateFormatted(srcCell)) {
                    distCell.setCellValue(srcCell.getDateCellValue());
                } else {
                    distCell.setCellValue(srcCell.getNumericCellValue());
                }
            } else if (srcCellType == HSSFCell.CELL_TYPE_STRING) {
                distCell.setCellValue(srcCell.getRichStringCellValue());
            } else if (srcCellType == HSSFCell.CELL_TYPE_BLANK) {
            } else if (srcCellType == HSSFCell.CELL_TYPE_BOOLEAN) {
                distCell.setCellValue(srcCell.getBooleanCellValue());
            } else if (srcCellType == HSSFCell.CELL_TYPE_ERROR) {
                distCell.setCellErrorValue(srcCell.getErrorCellValue());
            } else if (srcCellType == HSSFCell.CELL_TYPE_FORMULA) {
                distCell.setCellFormula(srcCell.getCellFormula());
            } else {
            }
        }
    }


    @Override
    public void exportAccountInput(HttpServletResponse httpServletResponse, AccountInputRecordRequest accountInputRecordRequest, Users currentLoginUsers) {
        //由输入流得到工作簿
        ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentApi.templateGet("EXPORT_ACCOUNT_INPUT_TEMPLATE");
        AssertUtils.isResultObjectDataNull(this.getLogger(), attachmentRespFcResultObject);
        AttachmentResponse attachmentRespFc = attachmentRespFcResultObject.getData();
        accountInputRecordRequest.setPageSize(1000);
        try {
            URL url = new URL(attachmentRespFc.getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            boolean booleanWhile = true;
            XSSFSheet sheet = workbook.getSheetAt(0);
            while (booleanWhile) {
                ResultObject<BasePageResponse<AccountInputRecordResponse>> responseResultObject = this.inputRecord(accountInputRecordRequest, currentLoginUsers);
                AssertUtils.isResultObjectError(this.getLogger(), responseResultObject);
                BasePageResponse<AccountInputRecordResponse> basePageResponse = responseResultObject.getData();
                List<AccountInputRecordResponse> accountResponseList = basePageResponse.getData();
                if (!AssertUtils.isNotEmpty(accountResponseList)) {
                    break;
                }
                String json = JackSonUtils.toJson(accountResponseList);
                accountResponseList = JSON.parseArray(json, AccountInputRecordResponse.class);
                int i = (accountInputRecordRequest.getCurrentPage() - 1) * accountInputRecordRequest.getPageSize() + 2;
                int number = (accountInputRecordRequest.getCurrentPage() - 1) * accountInputRecordRequest.getPageSize() + 1;
                if (AssertUtils.isNotEmpty(accountResponseList)) {
                    for (AccountInputRecordResponse accountResponse : accountResponseList) {
                        Row writeRow = sheet.getRow(i);
                        if (!AssertUtils.isNotNull(writeRow)) {
                            Row sheetRow = sheet.getRow(i - 1);
                            writeRow = sheet.createRow(i);
                            copyRow(workbook, sheet, sheetRow, writeRow, false);
                        }
                        writeRow.getCell(0).setCellValue(number);
                        String userAccountNo = accountResponse.getUserAccountNo();
                        writeRow.getCell(1).setCellValue(userAccountNo);
                        String accountName = accountResponse.getAccountName();
                        writeRow.getCell(2).setCellValue(accountName);
                        String agentCode = accountResponse.getAgentCode();
                        writeRow.getCell(3).setCellValue(agentCode);
                        String serialNo = accountResponse.getSerialNo();
                        writeRow.getCell(4).setCellValue(serialNo);
                        String createdDateFormat = accountResponse.getCreatedDateFormat();
                        writeRow.getCell(5).setCellValue(createdDateFormat);
                        String businessTypeName = accountResponse.getBusinessTypeName();
                        writeRow.getCell(6).setCellValue(businessTypeName);
                        BigDecimal amount = accountResponse.getAmount();
                        writeRow.getCell(7).setCellValue(amount.toString());
                        BigDecimal freezeAmount = accountResponse.getFreezeAmount();
                        writeRow.getCell(8).setCellValue(freezeAmount.toString());
                        BigDecimal advanceTaxAmount = accountResponse.getAdvanceTaxAmount();
                        writeRow.getCell(9).setCellValue(advanceTaxAmount.toString());
                        BigDecimal actualAmount = accountResponse.getActualAmount();
                        writeRow.getCell(10).setCellValue(actualAmount.toString());
                        Boolean freezeBoolean = accountResponse.getFreezeBoolean();
                        if (freezeBoolean) {
                            writeRow.getCell(11).setCellValue("是");
                        } else {
                            writeRow.getCell(11).setCellValue("否");
                        }
                        i++;
                        number++;
                    }
                }
                if (accountInputRecordRequest.getCurrentPage().intValue() >= basePageResponse.getTotalPage().intValue()) {
                    booleanWhile = false;
                } else {
                    accountInputRecordRequest.setCurrentPage(accountInputRecordRequest.getCurrentPage() + 1);
                }
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(attachmentRespFc.getTemplateName() + ".xlsx", "UTF-8"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(PARTY_EXPORT_USER_ACCOUNT_ERROR);
        }
    }

    @Override
    public void exportAccountOutput(HttpServletResponse httpServletResponse, AccountInputRecordRequest accountInputRecordRequest, Users currentLoginUsers) {
        //由输入流得到工作簿
        ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentApi.templateGet("EXPORT_ACCOUNT_OUTPUT_TEMPLATE");
        AssertUtils.isResultObjectDataNull(this.getLogger(), attachmentRespFcResultObject);
        AttachmentResponse attachmentRespFc = attachmentRespFcResultObject.getData();
        accountInputRecordRequest.setPageSize(1000);
        try {
            URL url = new URL(attachmentRespFc.getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            boolean booleanWhile = true;
            XSSFSheet sheet = workbook.getSheetAt(0);
            while (booleanWhile) {
                ResultObject<BasePageResponse<AccountOutputRecordResponse>> responseResultObject = this.outputRecord(accountInputRecordRequest, currentLoginUsers);
                AssertUtils.isResultObjectError(this.getLogger(), responseResultObject);
                BasePageResponse<AccountOutputRecordResponse> basePageResponse = responseResultObject.getData();
                List<AccountOutputRecordResponse> accountResponseList = basePageResponse.getData();
                if (!AssertUtils.isNotEmpty(accountResponseList)) {
                    break;
                }
                String json = JackSonUtils.toJson(accountResponseList);
                accountResponseList = JSON.parseArray(json, AccountOutputRecordResponse.class);
                int i = (accountInputRecordRequest.getCurrentPage() - 1) * accountInputRecordRequest.getPageSize() + 2;
                int number = (accountInputRecordRequest.getCurrentPage() - 1) * accountInputRecordRequest.getPageSize() + 1;
                if (AssertUtils.isNotEmpty(accountResponseList)) {
                    for (AccountOutputRecordResponse accountResponse : accountResponseList) {
                        Row writeRow = sheet.getRow(i);
                        if (!AssertUtils.isNotNull(writeRow)) {
                            Row sheetRow = sheet.getRow(i - 1);
                            writeRow = sheet.createRow(i);
                            copyRow(workbook, sheet, sheetRow, writeRow, false);
                        }
                        writeRow.getCell(0).setCellValue(number);
                        String userAccountNo = accountResponse.getUserAccountNo();
                        writeRow.getCell(1).setCellValue(userAccountNo);
                        String accountName = accountResponse.getAccountName();
                        writeRow.getCell(2).setCellValue(accountName);
                        String agentCode = accountResponse.getAgentCode();
                        writeRow.getCell(3).setCellValue(agentCode);
                        String serialNo = accountResponse.getSerialNo();
                        writeRow.getCell(4).setCellValue(serialNo);
                        String createdDateFormat = accountResponse.getCreatedDateFormat();
                        writeRow.getCell(5).setCellValue(createdDateFormat);
                        String businessTypeName = accountResponse.getBusinessTypeName();
                        writeRow.getCell(6).setCellValue(businessTypeName);
                        BigDecimal amount = accountResponse.getAmount();
                        writeRow.getCell(7).setCellValue(amount.toString());
                        String subbranch = accountResponse.getSubbranch();
                        writeRow.getCell(8).setCellValue(subbranch);
                        String accountNo = accountResponse.getAccountNo();
                        writeRow.getCell(9).setCellValue(accountNo);
                        String statusName = accountResponse.getStatusName();
                        writeRow.getCell(10).setCellValue(statusName);
                        i++;
                        number++;
                    }
                }
                if (accountInputRecordRequest.getCurrentPage().intValue() >= basePageResponse.getTotalPage().intValue()) {
                    booleanWhile = false;
                } else {
                    accountInputRecordRequest.setCurrentPage(accountInputRecordRequest.getCurrentPage() + 1);
                }
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(attachmentRespFc.getTemplateName() + ".xlsx", "UTF-8"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(PARTY_EXPORT_USER_ACCOUNT_ERROR);
        }
    }

    @Override
    public ResultObject lockUserAccount(String userId, String typeCode, String reasonCode) {
        ResultObject resultObject = new ResultObject();
        AssertUtils.isNotEmpty(this.getLogger(), userId, PARTY_USER_ID_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), typeCode, PARTY_USER_ACCOUNT_TYPE_CODE_IS_NOT_NULL);
        UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBo(userId, PartyTermEnum.USER_ACCOUNT_TYPE.CASH_ACCOUNT.name());
        if (AssertUtils.isNotNull(userAccountBo)) {
            if (PartyTermEnum.ACCOUNT_STATUS.FROZEN.name().equals(typeCode) &&
                    PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name().equals(userAccountBo.getAccountStatus())) {
                // 冻结
                userAccountBo.setAccountStatus(typeCode);
                userAccountBo.setFreezeDate(DateUtils.getCurrentTime());
                userAccountBo.setFreezeReasonCode(reasonCode);
                userAccountBaseService.saveUserAccount(userAccountBo, userId);
            } else if (PartyTermEnum.ACCOUNT_STATUS.ACTIVITY.name().equals(typeCode) &&
                    PartyTermEnum.ACCOUNT_STATUS.FROZEN.name().equals(userAccountBo.getAccountStatus()) &&
                    PartyTermEnum.ACCOUNT_FREEZE_REASON_CODE.ID_EXPIRED.name().equals(userAccountBo.getFreezeReasonCode())) {
                // 解冻
                userAccountBo.setAccountStatus(typeCode);
                userAccountBo.setUnfreezeDate(DateUtils.getCurrentTime());
                userAccountBaseService.saveUserAccount(userAccountBo, userId);
            }
        }
        return resultObject;
    }
}
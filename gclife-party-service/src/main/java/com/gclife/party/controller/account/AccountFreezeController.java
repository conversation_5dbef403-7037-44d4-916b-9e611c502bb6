package com.gclife.party.controller.account;

import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.account.AccountOperateListRequest;
import com.gclife.party.model.request.account.AccountOperateRequest;
import com.gclife.party.model.request.account.AccountOperateReviewRequest;
import com.gclife.party.model.response.account.AccountOperateListResponse;
import com.gclife.party.model.response.account.AccountOperateReviewListResponse;
import com.gclife.party.service.account.AccountFreezeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * create 19-7-12
 * description:
 */
@Api(tags = "账户冻结操作", description = "账户冻结操作")
@RefreshScope
@RestController
@RequestMapping("v1/account")
public class AccountFreezeController extends BaseController {
    @Autowired
    private AccountFreezeService accountFreezeService;

    @ApiOperation(value = "账户冻结列表", notes = "账户冻结列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @GetMapping(value = "freeze/list")
    public ResultObject<BasePageResponse<AccountOperateListResponse>> getAccountFreezeList(AccountOperateListRequest accountOperateListRequest) {
        return accountFreezeService.getAccountFreezeList(accountOperateListRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "申请账户冻结", notes = "申请账户冻结")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @PostMapping(value = "freeze/deal")
    public ResultObject postAccountFreeze(@RequestBody AccountOperateRequest accountOperateRequest) {
        return accountFreezeService.postAccountFreeze(accountOperateRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "冻结审批列表", notes = "冻结审批列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @GetMapping(value = "freeze/review/list")
    public ResultObject<BasePageResponse<AccountOperateReviewListResponse>> getAccountFreezeReviewList(AccountOperateListRequest accountOperateListRequest) {
        return accountFreezeService.getAccountFreezeReviewList(accountOperateListRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "操作账户冻结审批", notes = "操作账户冻结审批")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @PostMapping(value = "freeze/review/deal")
    public ResultObject postAccountFreeze(@RequestBody AccountOperateReviewRequest accountOperateReviewRequest) {
        return accountFreezeService.postAccountFreezeReview(accountOperateReviewRequest, this.getCurrentLoginUsers());
    }
}

package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.PositionSignInRequest;
import com.gclife.party.model.response.topic.PositionSignInDetailResponse;
import com.gclife.party.model.response.topic.PositionSignInResponse;
import com.gclife.party.service.PositionSignInService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 11:17 2019/1/16
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Api(tags = "PositionSignIn", description = "定位签到")
@RefreshScope
@RestController
@RequestMapping(value = "/v1/position/sign/in")
public class PositionSignInController extends BaseController {

    @Autowired
    private PositionSignInService positionSignInService;

    @ApiOperation(value = "位置签到列表", notes = "位置签到列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "list")
    public ResultObject<BasePageResponse<PositionSignInResponse>> queryPositionSignIn(PositionSignInRequest positionSignInRequest) {
        return positionSignInService.queryPositionSignIn(positionSignInRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "位置签到列表导出", notes = "位置签到列表导出")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "export")
    public void exportPositionSignIn(HttpServletResponse response,@RequestBody PositionSignInRequest positionSignInRequest) {
        positionSignInService.exportPositionSignIn(response,positionSignInRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "位置签到详情", notes = "位置签到详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "detail")
    public ResultObject<PositionSignInDetailResponse> queryPositionSignInDetail(@RequestParam(name = "topicId")String topicId) {
        return positionSignInService.queryPositionSignInDetail(topicId, this.getCurrentLoginUsers());
    }


}

package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.client.ClientManageListRequest;
import com.gclife.party.model.request.client.ClientPolicyEntryRequest;
import com.gclife.party.model.request.client.ClientPolicyErrorRequest;
import com.gclife.party.model.request.policy.DutyClassResponse;
import com.gclife.party.model.response.client.ClientCustomerListResponse;
import com.gclife.party.model.response.client.ClientManagePolicyDetailResponse;
import com.gclife.party.model.response.client.PolicyManageListResponse;
import com.gclife.party.model.response.client.PolicyOptionRecordResponse;
import com.gclife.party.service.ClientAppPolicyService;
import com.gclife.product.model.response.ProviderResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 2022/8/17 8:31
 * description:客户APP保单录入
 */
@Api(tags = "客户APP保单录入")
@RefreshScope
@RestController
@RequestMapping("v1/client/manage/policy")
public class ClientAppPolicyController extends BaseController {
    @Autowired
    private ClientAppPolicyService clientAppPolicyService;

    @ApiOperation(value = "客户管理保单录入列表", notes = "客户管理保单录入列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "inputs")
    public ResultObject<BasePageResponse<PolicyManageListResponse>> queryClientPolicyInputList(ClientManageListRequest clientPolicyListRequest) {
        clientPolicyListRequest.setListType("INPUT");
        return clientAppPolicyService.queryClientPolicyList(clientPolicyListRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "客户管理保单查询列表", notes = "客户管理保单查询列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "list")
    public ResultObject<BasePageResponse<PolicyManageListResponse>> queryClientPolicyList(ClientManageListRequest clientPolicyListRequest) {
        return clientAppPolicyService.queryClientPolicyList(clientPolicyListRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "签收/取消保单", notes = "签收/取消保单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PutMapping(value = "sign")
    public ResultObject<Void> signPolicy(String policyId, String cancelFlag) {
        return clientAppPolicyService.signPolicy(policyId, cancelFlag, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "录入保单异常处理", notes = "录入保单异常处理")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "error")
    public ResultObject<Void> errorPolicy(@RequestBody ClientPolicyErrorRequest clientPolicyErrorRequest) {
        return clientAppPolicyService.errorPolicy(clientPolicyErrorRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "保单流程记录", notes = "保单流程记录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "record")
    public ResultObject<List<PolicyOptionRecordResponse>> getOptionRecordList(@RequestParam("policyId") String policyId) {
        return clientAppPolicyService.getOptionRecordList(policyId);
    }

    @ApiOperation(value = "保单详情", notes = "保单详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "detail")
    public ResultObject<ClientManagePolicyDetailResponse> detail(@RequestParam("policyId") String policyId) {
        return clientAppPolicyService.detail(policyId, this.getAppRequestHandler());
    }

    @ApiOperation(value = "保单提交", notes = "保单提交")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "save")
    public ResultObject<Void> savePolicy(@RequestBody ClientPolicyEntryRequest clientPolicyEntryRequest) {
        return clientAppPolicyService.savePolicy(clientPolicyEntryRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "查询保险公司", notes = "查询保险公司")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "providers")
    public ResultObject<List<ProviderResponse>> listProvider() {
        return clientAppPolicyService.listProvider();
    }

    @ApiOperation(value = "查询保障类别库", notes = "查询保障类别库")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "duty/class")
    public ResultObject<List<DutyClassResponse>> listDutyClass() {
        return clientAppPolicyService.listDutyClass();
    }

    @ApiOperation(value = "该保单下的家庭成员列表", notes = "该保单下的家庭成员列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "members")
    public ResultObject<List<ClientCustomerListResponse>> listMember(@RequestParam(name = "policyId") String policyId,
                                                                     @RequestParam(name = "keyword", required = false) String keyword) {
        return clientAppPolicyService.listMember(policyId, keyword);
    }
}

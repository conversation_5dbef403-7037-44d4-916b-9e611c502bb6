package com.gclife.party.controller.visit;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.visit.ReturnVisitPageRequest;
import com.gclife.party.service.visit.EndorseReturnVisitBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Auther: chenjinrong
 * @Date: 19-10-18 15:31
 * @Description:
 */
@Api(tags = "保全回访", description = "保全回访")
@Controller
@RequestMapping(value = "/v1/endorse/")
public class EndorseReturnVisitController {

    @Autowired
    EndorseReturnVisitBusinessService endorseReturnVisitBusinessService;

    @ApiOperation(value = "保全回访列表", notes = "保全回访列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "return/visit/list")
    public ResultObject getEndorseReturnVisitList(ReturnVisitPageRequest pageRequest){
        pageRequest.setBusinessType(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.ENDORSE.name());
        pageRequest.setReturnVisitStatus(PartyTermEnum.RETURN_VISIT_STATUS.NO_RETURN_VISIT.name());
        return endorseReturnVisitBusinessService.getEndorseReturnVisitList(pageRequest);
    }


}

package com.gclife.party.controller.visit;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.visit.*;
import com.gclife.party.model.response.visit.*;
import com.gclife.party.service.visit.PolicyReturnVisitBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Auther: chenjinrong
 * @Date: 19-10-18 14:41
 * @Description: 保单回访
 */
@Api(tags = "保单回访", description = "保单回访")
@Controller
@RequestMapping(value = "/v1/policy/")
public class PolicyReturnVisitController extends BaseController {

    @Autowired
    PolicyReturnVisitBusinessService policyReturnVisitBusinessService;

    @ApiOperation(value = "保单回访统计查询", notes = "保单回访统计查询")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "return/visit/statistics")
    public ResultObject<ReturnVisitStatisticsListResponse> getPolicyReturnVisitStatistics(ReturnVisitPageRequest pageRequest){
        return policyReturnVisitBusinessService.getPolicyReturnVisitStatistics(pageRequest,this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "保单回访列表", notes = "保单回访列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "return/visit/list")
    public ResultObject getPolicyReturnVisitList(ReturnVisitPageRequest pageRequest){
        pageRequest.setReturnVisitStatus(PartyTermEnum.RETURN_VISIT_STATUS.NO_RETURN_VISIT.name());
        pageRequest.setBusinessType(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.POLICY.name());
        return policyReturnVisitBusinessService.getPolicyReturnVisitList(pageRequest);
    }

    @ApiOperation(value = "保单回访详情", notes = "保单回访详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "return/visit/detail")
    public ResultObject getPolicyReturnVisitDetail(String returnVisitId,String returnVisitChangeId){
        //需要分开查询个险保单和团险保单的详情
        return policyReturnVisitBusinessService.getPolicyReturnVisitDetail(returnVisitId,returnVisitChangeId);
    }

    @ApiOperation(value = "新增保单回访信息", notes = "新增保单回访信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "return/visit/add")
    public ResultObject<ReturnVisitAddResponse> addPolicyReturnVisit(@RequestBody PolicyReturnVisitAddRequest returnVisitSubmitRequest) {
        return policyReturnVisitBusinessService.addPolicyReturnVisit(this.getCurrentLoginUsers(),returnVisitSubmitRequest);
    }

    @ApiOperation(value = "提交回访信息", notes = "提交回访信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "return/visit/submit")
    public ResultObject postPolicyReturnVisitSubmit(@RequestBody PolicyReturnVisitSubmitRequest policyReceiptSubmitRequest){
        return policyReturnVisitBusinessService.postPolicyReturnVisitSubmit(this.getCurrentLoginUsers(),policyReceiptSubmitRequest);
    }

    @ApiOperation(value = "保单回访变更查询列表", notes = "保单回访变更查询列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "return/visit/change")
    public ResultObject getPolicyReturnVisitChange(ReturnVisitPageRequest pageRequest){
        pageRequest.setReturnVisitStatus(PartyTermEnum.RETURN_VISIT_STATUS.YES_RETURN_VISIT.name());
        return policyReturnVisitBusinessService.getReturnVisitChangeList(pageRequest);
    }

    @ApiOperation(value = "提交回访变更申请信息", notes = "提交回访变更申请信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "return/visit/change/submit")
    public ResultObject postPolicyReturnVisitChangeSubmit(@RequestBody PolicyReturnVisitChangeRequest policyReceiptSubmitRequest) {
        return policyReturnVisitBusinessService.postPolicyReturnVisitChangeSubmit(this.getCurrentLoginUsers(), policyReceiptSubmitRequest);
    }


    @ApiOperation(value = "回访审核查询列表", notes = "回访审核查询列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "return/visit/audit")
    public ResultObject getPolicyReturnVisitAudit(ReturnVisitChangePageRequest pageRequest){
        pageRequest.setAuditResult(PartyTermEnum.CHANGE_AUDIT_FLAG.wait_audit.name());
        return policyReturnVisitBusinessService.getPolicyReturnVisitAudit(pageRequest);
    }

    @ApiOperation(value = "提交回访审核申请信息", notes = "提交回访审核申请信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "return/visit/audit/submit")
    public ResultObject postPolicyReturnVisitAuditSubmit(@RequestBody ReturnVisitAuditRequest policyReturnVisitAuditRequest) {
        return policyReturnVisitBusinessService.postReturnVisitAuditSubmit(this.getCurrentLoginUsers(), policyReturnVisitAuditRequest);
    }

    @ApiOperation(value = "回访查询列表", notes = "回访查询列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "return/visit/query")
    public ResultObject queryPolicyReturnVisitList(ReturnVisitPageRequest pageRequest){
        return policyReturnVisitBusinessService.getPolicyReturnVisitList(pageRequest);
    }

    @ApiOperation(value = "回访字典", notes = "回访字典")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "return/visit/type")
    public ResultObject<ReturnVisitTypeResponse> getReturnVisitTypeEnum(){
        return policyReturnVisitBusinessService.getReturnVisitTypeEnum();
    }

    @ApiOperation(value = "查询回访记录", notes = "查询回访记录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "return/visit/get")
    public ResultObject<List<ReturnVisitContentResponse>> getReturnVisitTypeEnum(String businessId, String businessType){
        return policyReturnVisitBusinessService.getReturnVisitList(businessId, businessType);
    }
}

package com.gclife.party.controller.account;

import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.account.AccountOperateListRequest;
import com.gclife.party.model.request.account.AccountOperateRequest;
import com.gclife.party.model.request.account.AccountOperateReviewRequest;
import com.gclife.party.model.response.account.AccountOperateListResponse;
import com.gclife.party.model.response.account.AccountOperateReviewListResponse;
import com.gclife.party.service.account.AccountActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * create 19-7-12
 * description:
 */
@Api(tags = "账户解冻操作", description = "账户解冻操作")
@RefreshScope
@RestController
@RequestMapping("v1/account")
public class AccountActivityController extends BaseController {
    @Autowired
    private AccountActivityService accountActivityService;

    @ApiOperation(value = "账户解冻列表", notes = "账户解冻列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @GetMapping(value = "activity/list")
    public ResultObject<BasePageResponse<AccountOperateListResponse>> getAccountActivityList(AccountOperateListRequest accountOperateListRequest) {
        return accountActivityService.getAccountActivityList(accountOperateListRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "申请账户解冻", notes = "申请账户解冻")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @PostMapping(value = "activity/deal")
    public ResultObject postAccountActivity(@RequestBody AccountOperateRequest accountOperateRequest) {
        return accountActivityService.postAccountActivity(accountOperateRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "解冻审批列表", notes = "解冻审批列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @GetMapping(value = "activity/review/list")
    public ResultObject<BasePageResponse<AccountOperateReviewListResponse>> getAccountActivityReviewList(AccountOperateListRequest accountOperateListRequest) {
        return accountActivityService.getAccountActivityReviewList(accountOperateListRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "操作账户解冻审批", notes = "操作账户解冻审批")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @PostMapping(value = "activity/review/deal")
    public ResultObject postAccountActivity(@RequestBody AccountOperateReviewRequest accountOperateReviewRequest) {
        return accountActivityService.postAccountActivityReview(accountOperateReviewRequest, this.getCurrentLoginUsers());
    }

}

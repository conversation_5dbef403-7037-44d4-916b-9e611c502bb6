package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.CustomerMessagesRequest;
import com.gclife.party.model.request.UserCustomerBusinessRequest;
import com.gclife.party.model.request.UserCustomerRequest;
import com.gclife.party.model.request.customer.ContactRecordRequest;
import com.gclife.party.model.request.customer.CustomerListRequest;
import com.gclife.party.model.response.CustomerAgentResponse;
import com.gclife.party.model.response.CustomerMessageResponse;
import com.gclife.party.model.response.UserCustomerResponse;
import com.gclife.party.model.response.customer.ContactRecordResponse;
import com.gclife.party.model.response.customer.CustomerChooseResponse;
import com.gclife.party.model.response.customer.CustomerListResponse;
import com.gclife.party.model.response.customer.MemberResponse;
import com.gclife.party.service.CustomerBaseBusinessService;
import com.gclife.party.service.CustomerManageService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户管理模块
 *
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
@Api(tags = "Customer_Manage", description = "客户管理模块")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class CustomerManageController extends BaseController {

    @Autowired
    private CustomerManageService customerManageService;
    @Autowired
    private CustomerBaseBusinessService customerBaseBusinessService;

    @ApiOperation(value = "添加修改客户信息", notes = "添加修改客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "customer/save")
    public ResultObject<UserCustomerResponse> saveCustomerMessage(@RequestBody UserCustomerRequest userCustomerRequest) {
        return customerManageService.saveCustomerMessage(userCustomerRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "客户列表", notes = "客户列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "customer/list")
    public ResultObject<BasePageResponse<CustomerListResponse>> listCustomer(CustomerListRequest customerListRequest) {
        return customerManageService.listCustomer(customerListRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "选择客户列表", notes = "选择客户列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "customer/choose/list")
    public ResultObject<BasePageResponse<CustomerChooseResponse>> listChooseCustomer(CustomerListRequest customerListRequest) {
        return customerManageService.listChooseCustomer(customerListRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "家庭成员列表", notes = "家庭成员列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "customer/members")
    public ResultObject<List<MemberResponse>> listMember(@RequestParam(name = "customerId") String customerId,
                                                         @RequestParam(name = "keyword", required = false) String keyword) {
        return customerManageService.listMember(customerId, keyword);
    }

    @ApiOperation(value = "移除家庭成员", notes = "移除家庭成员")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @DeleteMapping(value = "customer/member/delete")
    public ResultObject removeMember(@RequestParam(name = "oneselfCustomerId") String oneselfCustomerId,
                                     @RequestParam(name = "customerId") String customerId) {
        return customerManageService.removeMember(oneselfCustomerId, customerId);
    }

    @ApiOperation(value = "删除客户", notes = "删除客户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @DeleteMapping(value = "customer/delete")
    public ResultObject deleteCustomer(@RequestParam(name = "customerId") String customerId) {
        return customerManageService.deleteCustomer(customerId);
    }

    @ApiOperation(value = "获取我的客户信息", notes = "获取我的客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "customerId", value = "客户ID", defaultValue = "客户ID", paramType = "query", required = true)
            }
    )
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "customer/detail")
    public ResultObject<CustomerMessageResponse> getCustomerDetail(@RequestParam(name = "customerId") String customerId,
                                                                   @RequestParam(name = "oneselfCustomerId", required = false) String oneselfCustomerId) {
        return customerManageService.getCustomerDetail(customerId, oneselfCustomerId, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "保存沟通记录", notes = "保存沟通记录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "customer/contact/save")
    public ResultObject saveContact(@RequestBody ContactRecordRequest contactRecordRequest) {
        return customerManageService.saveContact(contactRecordRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "查询沟通记录列表", notes = "查询沟通记录列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "customer/contacts")
    public ResultObject<List<ContactRecordResponse>> listContactRecord(@RequestParam(name = "customerId", required = false) String customerId,
                                                                       @RequestParam(name = "contactEvent", required = false) String contactEvent) {
        return customerManageService.listContactRecord(customerId, contactEvent);
    }

    @ApiOperation(value = "查询沟通记录", notes = "保存沟通记录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "customer/contact")
    public ResultObject<ContactRecordResponse> queryContactRecord(@RequestParam(name = "contactRecordId") String contactRecordId) {
        return customerManageService.queryContactRecord(contactRecordId);
    }

    @ApiOperation(value = "删除沟通记录", notes = "删除沟通记录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @DeleteMapping(value = "customer/contact/delete")
    public ResultObject deleteContactRecord(@RequestParam(name = "contactRecordId") String contactRecordId) {
        return customerManageService.deleteContactRecord(contactRecordId);
    }

    @ApiOperation(value = "生日客户列表", notes = "生日客户列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "customer/birthday/list")
    public ResultObject<List<CustomerListResponse>> listBirthdayCustomer() {
        return customerManageService.listBirthdayCustomer(this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "保存客户手机号", notes = "保存客户手机号")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "customer/mobile/save")
    public ResultObject saveCustomerMobile(@RequestParam(name = "customerId") String customerId,
                                           @RequestParam(name = "mobile") String mobile) {
        return customerManageService.saveCustomerMobile(customerId, mobile);
    }




    @ApiOperation(value = "添加修改客户信息服务层PO数据添加", notes = "添加修改客户信息服务层PO数据添加")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "save/customer/business")
    public ResultObject<UserCustomerResponse> saveCustomerBusiness(@RequestBody UserCustomerBusinessRequest userCustomerBusiness) {
        return customerManageService.saveCustomerBusiness(userCustomerBusiness, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "添加修改客户信息服务层PO数据", notes = "添加修改客户信息服务层PO数据添加")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "save/customer/business/batch")
    public ResultObject<List<UserCustomerResponse>> saveCustomerBusinessList(@RequestBody List<UserCustomerBusinessRequest> userCustomerBusinessList) {
        return customerManageService.saveCustomerBusinessList(userCustomerBusinessList, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "修改单个客户信息服务层PO数据(根据主键修改)", notes = "修改单个客户信息服务层PO数据(根据主键修改)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "update/customer/business")
    public ResultObject<UserCustomerResponse> updateCustomerBusinessSingle(@RequestBody UserCustomerBusinessRequest userCustomerBusiness) {
        return customerManageService.updateCustomerBusiness(userCustomerBusiness, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "批量修改客户信息服务层PO数据(根据主键修改)", notes = "批量修改客户信息服务层PO数据(根据主键修改)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "update/customer/business/batch")
    public ResultObject<List<UserCustomerResponse>> updateCustomerBusinessList(@RequestBody List<UserCustomerBusinessRequest> userCustomerBusinessList) {
        return customerManageService.updateCustomerBusinessList(userCustomerBusinessList, this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "模糊查询客户", notes = "模糊查询客户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "keyword/customer")
    public ResultObject<BasePageResponse<CustomerMessageResponse>> queryCustomer(CustomerMessagesRequest customerMessagesRequest) {
        return customerBaseBusinessService.queryCustomerList(getAppRequestHandler(), customerMessagesRequest);
    }

    @ApiOperation(value = "查询疑似客户列表", notes = "查询疑似客户列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "suspected/customer/list")
    public ResultObject<List<CustomerMessageResponse>> querySuspectedCustomer(String customerAgentId) {
        return customerBaseBusinessService.querySuspectedCustomer(getCurrentLoginUsers(), customerAgentId);
    }

    @ApiOperation(value = "根据代理人客户ID查询真正的客户信息", notes = "根据代理人客户ID查询真正的客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "real/customer/list")
    public ResultObject<CustomerMessageResponse> queryRealCustomerByCustomerAgentId(String customerAgentId) {
        return customerBaseBusinessService.queryRealCustomerByCustomerAgentId(getCurrentLoginUsers(), customerAgentId);
    }

    @ApiOperation(value = "根据主键修改Customer客户(不包含CustomerAgent)", notes = "根据主键修改Customer客户(不包含CustomerAgent)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "update/real/customer/business")
    public ResultObject<UserCustomerResponse> updateRealCustomerBusinessSingle(@RequestBody UserCustomerBusinessRequest userCustomerBusiness) {
        return customerManageService.updateRealCustomerBusinessSingle(userCustomerBusiness, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "用所选择的CustomerId的客户号覆盖CustomerAgent的客户号", notes = "用所选择的CustomerId的客户号覆盖CustomerAgent的客户号")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "update/customer/agent/business")
    public ResultObject<UserCustomerResponse> updateCustomerAgentDataByCustomer(@RequestParam(value = "customerId") String customerId,@RequestParam(value = "customerAgentId") String customerAgentId) {
        return customerManageService.updateCustomerAgentDataByCustomer(customerId,customerAgentId, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "查询客户app客户信息根据", notes = "查询客户app客户信息根据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "customer/client/list")
    public ResultObject<List<CustomerAgentResponse>> getClientCustomerAgentList(@RequestParam(value = "type") String type){
        return customerBaseBusinessService.getClientCustomerAgentList(type);
    }

    }
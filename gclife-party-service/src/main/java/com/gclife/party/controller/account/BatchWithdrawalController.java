package com.gclife.party.controller.account;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.party.api.UserAccountApi;
import com.gclife.party.model.request.*;
import com.gclife.party.model.request.account.AccountRequest;
import com.gclife.party.model.request.visit.BatchWithdrawalRequest;
import com.gclife.party.model.request.visit.WithdrawalResult;
import com.gclife.party.model.response.CashWithdrawalResponse;
import com.gclife.party.model.response.UserAccountDetailResponse;
import com.gclife.party.model.response.UserAccountResponse;
import com.gclife.party.model.response.UserAccountTransactionRecordResponse;
import com.gclife.party.model.response.account.AccountResponse;
import com.gclife.party.service.account.AccountBatchWithdrawalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 2021/4/6 下午6:04
 * description:
 */
@Api(tags = "用户账户提现相关", description = "用户账户提现相关")
@RefreshScope
@RestController
public class BatchWithdrawalController extends BaseController {
    @Autowired
    private AccountBatchWithdrawalService accountBatchWithdrawalService;

    @ApiOperation(value = "Pc账户中心批量提现列表", notes = "Pc账户中心批量提现列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "/v1/account/cash/withdrawal")
    ResultObject<BasePageResponse<AccountResponse>> postCashWithdrawalList(AccountRequest accountRequest) {
        return accountBatchWithdrawalService.postCashWithdrawalBatchList(accountRequest, this.getCurrentLoginUsers());
    }
//    @ApiOperation(value = "Pc账户中心批量提现", notes = "Pc账户中心批量提现")
//    @ApiResponses({
//            @ApiResponse(code = 200, message = "请求成功")
//    })
//    @ApiVersion(1)
//    @AutoCheckPermissions
//    @PostMapping(value = "/v1/account/cash/withdrawal/fullwithdraw")
//     ResultObject<BasePageResponse<AccountResponse>> postCashWithdrawalFullWithdraw(@RequestBody List<String> accountids) {
//        return accountBatchWithdrawalService.postCashWithdrawalFullWithdraw(accountids,this.getCurrentLoginUsers());
//    }
    @ApiOperation(value = "Pc账户中心提现", notes = "Pc账户中心提现")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "/v1/account/cash/withdrawal/extract")
    ResultObject<WithdrawalResult> postCashWithdrawalExtract(@RequestBody BatchWithdrawalRequest batchWithdrawalRequest) {
        return accountBatchWithdrawalService.postCashWithdrawalExtract(batchWithdrawalRequest,this.getCurrentLoginUsers());
    }
}

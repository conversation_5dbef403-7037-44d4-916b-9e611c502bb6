package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.party.api.UserAccountApi;
import com.gclife.party.model.request.*;
import com.gclife.party.model.response.CashWithdrawalResponse;
import com.gclife.party.model.response.UserAccountDetailResponse;
import com.gclife.party.model.response.UserAccountResponse;
import com.gclife.party.model.response.UserAccountTransactionRecordResponse;
import com.gclife.party.service.UserAccountBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * create 19-6-13
 * description: 用户账户相关
 */
@Api(tags = "用户账户相关", description = "用户账户相关")
@RefreshScope
@RestController
public class UserAccountController extends BaseController implements UserAccountApi {
    @Autowired
    private UserAccountBusinessService userAccountBusinessService;

    @Override
    public ResultObject postUserAccountIncomeList(@RequestBody List<UserAccountIncomeRequest> userAccountIncomeRequests) {
        return userAccountBusinessService.postUserAccountIncomeList(userAccountIncomeRequests);
    }

    @Override
    public ResultObject postUserAccountIncome(@RequestBody UserAccountIncomeRequest userAccountIncomeRequest) {
        return userAccountBusinessService.postUserAccountIncome(userAccountIncomeRequest);
    }

    @Override
    public ResultObject<UserAccountTransactionRecordResponse> postUserAccountOutlay(@RequestBody UserAccountOutlayRequest userAccountOutlayRequest) {
        return userAccountBusinessService.postUserAccountOutlay(userAccountOutlayRequest);
    }

    @Override
    public ResultObject<List<UserAccountTransactionRecordResponse>> postUserAccountOutlayBatch(@RequestBody List<UserAccountOutlayRequest> userAccountOutlayRequests) {
        return userAccountBusinessService.postUserAccountOutlayBatch(userAccountOutlayRequests);
    }

    @Override
    public ResultObject initializationAccountByUserId(@RequestParam(name = "userId") String userId) {
        return userAccountBusinessService.initializationAccountByUserId(userId);
    }

    @Override
    public ResultObject activityAccountByUserId(String userId) {
        return userAccountBusinessService.activityAccountByUserId(userId);
    }

    @Override
    public ResultObject updateAccountStatus(@RequestBody UserAccountNotifyRequest userAccountNotifyRequest) {
        return userAccountBusinessService.updateAccountStatus(userAccountNotifyRequest);
    }

    @Override
    public ResultObject updateAccountStatusRollBack(@RequestBody UserAccountRollbackRequest userAccountRollbackRequest) {
        return userAccountBusinessService.updateAccountStatusRollBack(userAccountRollbackRequest);
    }

    @Override
    public ResultObject updateAccountStatusRollBackBatch(@RequestBody List<UserAccountRollbackRequest> userAccountRollbackRequests) {
        return userAccountBusinessService.updateAccountStatusRollBackBatch(userAccountRollbackRequests);
    }

    @Override
    public ResultObject updateAccountIncomeRollBackBatch(@RequestBody List<UserAccountRollbackRequest> userAccountRollbackRequests) {
        return userAccountBusinessService.updateAccountIncomeRollBackBatch(userAccountRollbackRequests);
    }

    @Override
    public ResultObject dealAccountDisable(@RequestBody List<UserAccountDisableRequest> userAccountDisableRequests) {
        return userAccountBusinessService.dealAccountDisable(userAccountDisableRequests);
    }

    @Override
    public ResultObject<UserAccountResponse> userAccountGet(@RequestParam(name = "userId") String userId, @RequestParam(name = "userAccountTypeCode") String userAccountTypeCode) {
        return userAccountBusinessService.userAccountGet(userAccountTypeCode, userId);
    }

    @Override
    public ResultObject<List<UserAccountDetailResponse>> userAccountDetailGet(@RequestParam(name = "userId") String userId, @RequestParam(name = "userAccountTypeCode") String userAccountTypeCode, @RequestParam(required = false, name = "streamCode") String streamCode,
                                                                              @RequestParam(name = "currentPage") Integer currentPage, @RequestParam(name = "pageSize") Integer pageSize) {
        return userAccountBusinessService.userAccountDetailGet(userId, userAccountTypeCode, streamCode, currentPage, pageSize);
    }

    @Override
    public ResultObject<CashWithdrawalResponse> postCashWithdrawal(@RequestBody CashWithdrawalRequest cashWithdrawalRequest) {
        return userAccountBusinessService.postCashWithdrawal(cashWithdrawalRequest);
    }

    @Override
    public ResultObject postCashWithdrawalNotify(@RequestBody UserAccountNotifyRequest userAccountNotifyRequest) {
        return userAccountBusinessService.postCashWithdrawalNotify(userAccountNotifyRequest);
    }

    @Override
    public ResultObject updateAccountWithdrawRollBack(@RequestBody UserAccountRollbackRequest userAccountRollbackRequest) {
        return userAccountBusinessService.updateAccountWithdrawRollBack(userAccountRollbackRequest);
    }

    @ApiOperation(value = "发起批量提现", notes = "发起批量提现")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "/v1/account/cash/withdrawal/batch")
    ResultObject postCashWithdrawalBatch() {
        return userAccountBusinessService.postCashWithdrawalBatch(this.getCurrentLoginUsers());
    }
}

package com.gclife.party.controller.account;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.account.AccountInputRecordRequest;
import com.gclife.party.model.request.account.AccountRequest;
import com.gclife.party.model.response.account.AccountDetailResponse;
import com.gclife.party.model.response.account.AccountInputRecordResponse;
import com.gclife.party.model.response.account.AccountOutputRecordResponse;
import com.gclife.party.model.response.account.AccountResponse;
import com.gclife.party.service.account.AccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description
 * @date 2019-07-12 08:38
 */
@Api(tags = "Account_Center", description = "账户中心，仪表板数据，及国际化术语")
@RefreshScope
@RestController
@RequestMapping("v1/account")
public class AccountController extends BaseController {

    @Autowired
    private AccountService accountService;

    @ApiOperation(value = "账户列表", notes = "账户列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "list")
    public ResultObject<BasePageResponse<AccountResponse>> queryAccount(AccountRequest accountRequest) {
        return accountService.queryAccount(accountRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "账户详情", notes = "账户详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "detail")
    public ResultObject<AccountDetailResponse> queryAccountDetail(@RequestParam String userAccountId) {
        return accountService.queryAccountDetail(userAccountId, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "流入资料明细", notes = "账户列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "input/record")
    public ResultObject<BasePageResponse<AccountInputRecordResponse>> inputRecord(AccountInputRecordRequest accountInputRecordRequest) {
        return accountService.inputRecord(accountInputRecordRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "流出资料明细", notes = "账户列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "output/record")
    public ResultObject<BasePageResponse<AccountOutputRecordResponse>> outputRecord(AccountInputRecordRequest accountInputRecordRequest) {
        return accountService.outputRecord(accountInputRecordRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = " 账户余额表导出", notes = "账户余额表导出")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "export/account")
    public void exportAccount(HttpServletResponse httpServletResponse,AccountRequest accountRequest) {
        accountService.exportAccount(httpServletResponse,accountRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = " 账户流入明细表导出", notes = "账户流入明细表导出")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "export/account/input")
    public void exportAccountInput(HttpServletResponse httpServletResponse,AccountInputRecordRequest accountInputRecordRequest) {
        accountService.exportAccountInput(httpServletResponse,accountInputRecordRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = " 账户流出明细表导出", notes = "账户流出明细表导出")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "export/account/output")
    public void exportAccountOutput(HttpServletResponse httpServletResponse,AccountInputRecordRequest accountInputRecordRequest) {
        accountService.exportAccountOutput(httpServletResponse,accountInputRecordRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "锁定(解锁)用户账户", notes = "锁定用户账户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "lock")
    public ResultObject lockUserAccount(@RequestParam(name = "userId") String userId,
                                        @RequestParam(name = "typeCode") String typeCode,
                                        @RequestParam(name = "reasonCode", required = false) String reasonCode) {
        return accountService.lockUserAccount(userId, typeCode, reasonCode);
    }


}

package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.policy.ClientPolicyQueryRequest;
import com.gclife.party.model.request.policy.DutyClassResponse;
import com.gclife.party.model.request.policy.PictureBatchRequest;
import com.gclife.party.model.request.policy.PolicyRequest;
import com.gclife.party.model.response.policy.*;
import com.gclife.party.service.CustomerPolicyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户保单模块
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 22-09-01
 */
@Api(tags = "Customer_Policy", value = "客户保单模块")
@Controller
@RequestMapping("v1/customer/policy")
public class CustomerPolicyController extends BaseController {
    @Autowired
    private CustomerPolicyService customerPolicyService;

    @ApiOperation(value = "保单保存", notes = "保单保存")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "save")
    public ResultObject savePolicy(@RequestBody PolicyRequest policyRequest) {
        return customerPolicyService.savePolicy(policyRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "保单列表", notes = "保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "list")
    public ResultObject<List<CustomerPolicyListResponse>> listPolicy(@RequestParam(name = "customerId") String customerId) {
        return customerPolicyService.listPolicy(customerId);
    }

    @ApiOperation(value = "录入保单详情", notes = "录入保单详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "detail")
    public ResultObject<CustomerPolicyResponse> queryPolicy(@RequestParam("policyId") String policyId) {
        return customerPolicyService.queryPolicy(policyId);
    }

    @ApiOperation(value = "录入保单删除", notes = "录入保单删除")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @DeleteMapping(value = "delete")
    public ResultObject deletePolicy(@RequestParam("policyId") String policyId) {
        return customerPolicyService.deletePolicy(policyId);
    }

    @ApiOperation(value = "图片保单保存", notes = "图片保单保存")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "picture/save")
    public ResultObject savePicturePolicy(@RequestBody PictureBatchRequest pictureBatchRequest) {
        return customerPolicyService.savePicturePolicy(pictureBatchRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "图片保单列表", notes = "图片保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "picture/list")
    public ResultObject<List<PictureListResponse>> listPicturePolicy(@RequestParam(name = "customerId") String customerId) {
        return customerPolicyService.listPicturePolicy(customerId);
    }

    @ApiOperation(value = "图片保单信息", notes = "图片保单信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "picture/detail")
    public ResultObject<PictureDetailResponse> picturePolicyDetail(@RequestParam("pictureBatchId") String pictureBatchId,
                                                                   @RequestParam(name = "oneselfCustomerId") String oneselfCustomerId) {
        return customerPolicyService.picturePolicyDetail(pictureBatchId, oneselfCustomerId);
    }

    @ApiOperation(value = "图片保单取消申请", notes = "图片保单取消申请")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "picture/cancel")
    public ResultObject picturePolicyCancel(@RequestParam("pictureBatchId") String pictureBatchId) {
        return customerPolicyService.picturePolicyCancel(pictureBatchId, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "图片保单删除", notes = "图片保单删除")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @DeleteMapping(value = "picture/delete")
    public ResultObject deletePicturePolicy(@RequestParam("pictureBatchId") String pictureBatchId) {
        return customerPolicyService.deletePicturePolicy(pictureBatchId);
    }

    @ApiOperation(value = "查询保险公司", notes = "查询保险公司")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "providers")
    public ResultObject listProvider() {
        return customerPolicyService.listProvider();
    }

    @ApiOperation(value = "字典数据", notes = "保单字典数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "dic")
    public ResultObject<PolicyDicResponse> getPolicyDic() {
        return customerPolicyService.getPolicyDic(this.getAppRequestHandler());
    }

    @ApiOperation(value = "查询保障类别库", notes = "查询保障类别库")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "duty/class/list")
    public ResultObject<List<DutyClassResponse>> listDutyClass() {
        return customerPolicyService.listDutyClass();
    }


    @ApiOperation(value = "client保单列表", notes = "client保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "client/list")
    public ResultObject<List<ClientPolicyListResponse>> listClientPolicy(ClientPolicyQueryRequest queryRequest) {
        return customerPolicyService.listClientPolicy(queryRequest);
    }

}
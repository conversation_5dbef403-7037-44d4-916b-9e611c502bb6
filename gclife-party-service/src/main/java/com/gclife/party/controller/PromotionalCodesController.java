package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.model.ResultObject;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.party.core.jooq.tables.pojos.PromotionalCodesPo;
import com.gclife.party.model.response.PromotionalCodesResponse;
import com.gclife.party.service.PromotionalCodesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.InetAddress;

import static com.gclife.party.model.config.PartyErrorConfigEnum.PROMOTIONAL_CODES_CODE_IS_NOT_NULL;
import static com.gclife.party.model.config.PartyErrorConfigEnum.PROMOTIONAL_CODES_CODE_IS_NOT_RIGHT;


/**
 * <AUTHOR>
 * create 2022-11-22
 * description: 优惠码
 */
@Api(tags = "Promotional_Codes", description = "优惠码")
@RefreshScope
@RestController
@RequestMapping("v1/promotional/codes/")
public class PromotionalCodesController {

    @Autowired
    private PromotionalCodesService promotionalCodesService;


    @ApiOperation(value = "优惠码查询", notes = "优惠码查询")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "query")
    public ResultObject<PromotionalCodesResponse> queryPromotionalCodes(@RequestParam(value = "code") String code, String flag, String channelType) {
        return promotionalCodesService.queryPromotionalCodes(code, flag, channelType);
    }

    @ApiOperation(value = "优惠码重定向查询", notes = "优惠码重定向查询")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "redirect")
    public void promotionalCodesRedirect(HttpServletResponse response, String url,String code) throws IOException {
        String path = promotionalCodesService.verifyPromotionalCodes(url, code);
        response.sendRedirect(path);
    }
}

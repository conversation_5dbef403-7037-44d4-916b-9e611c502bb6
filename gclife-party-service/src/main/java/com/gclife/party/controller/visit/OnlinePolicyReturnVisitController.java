package com.gclife.party.controller.visit;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.visit.PolicyReturnVisitAddRequest;
import com.gclife.party.model.request.visit.PolicyReturnVisitSubmitRequest;
import com.gclife.party.model.request.visit.ReturnVisitPageRequest;
import com.gclife.party.model.response.visit.ReturnVisitAddResponse;
import com.gclife.party.service.visit.OnlinePolicyReturnVisitBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import static com.gclife.party.model.config.PartyTermEnum.RETURN_VISIT_TYPE.ONLINE_OTHER_AMOUNT;

/**
 * @Auther: lichongfu
 * @Date: 22-10-26 14:41
 * @Description: 网销保单回访
 */
@Api(tags = "网销保单回访", description = "网销保单回访")
@Controller
@RequestMapping(value = "/v1/online/policy/")
public class OnlinePolicyReturnVisitController  extends BaseController {

    @Autowired
    OnlinePolicyReturnVisitBusinessService onlinePolicyReturnVisitBusinessService;

    @ApiOperation(value = "新增网销保单回访信息", notes = "新增网销保单回访信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "return/visit/add")
    public ResultObject addOnlinePolicyReturnVisit(@RequestBody PolicyReturnVisitAddRequest returnVisitSubmitRequest) {
        returnVisitSubmitRequest.setBusinessType(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.ONLINE_POLICY.name());
        //returnVisitSubmitRequest.setReturnVisitType(ONLINE_OTHER_AMOUNT.name());
        return onlinePolicyReturnVisitBusinessService.addOnlinePolicyReturnVisit(this.getCurrentLoginUsers(),returnVisitSubmitRequest);
    }

    @ApiOperation(value = "网销提交回访信息", notes = "网销提交回访信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "return/visit/submit")
    public ResultObject postOnlinePolicyReturnVisitSubmit(@RequestBody PolicyReturnVisitSubmitRequest policyReceiptSubmitRequest){
        return onlinePolicyReturnVisitBusinessService.postOnlinePolicyReturnVisitSubmit(this.getCurrentLoginUsers(),policyReceiptSubmitRequest);
    }

    @ApiOperation(value = "保单回访详情", notes = "保单回访详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "return/visit/detail")
    public ResultObject getOnlinePolicyReturnVisitDetail(String returnVisitId, String returnVisitChangeId){
        return onlinePolicyReturnVisitBusinessService.getOnlinePolicyReturnVisitDetail(returnVisitId,returnVisitChangeId);
    }

    @ApiOperation(value = "网销保单回访列表分页查询", notes = "网销保单回访列表分页查询")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "return/visit/list")
    public ResultObject getOnlinePolicyReturnVisitList(ReturnVisitPageRequest pageRequest){
        pageRequest.setReturnVisitStatus(PartyTermEnum.RETURN_VISIT_STATUS.NO_RETURN_VISIT.name());
        pageRequest.setBusinessType(PartyTermEnum.RETURN_VISIT_BUSINESS_TYPE.ONLINE_POLICY.name());
        return onlinePolicyReturnVisitBusinessService.getOnlinePolicyReturnVisitList(pageRequest);
    }
}
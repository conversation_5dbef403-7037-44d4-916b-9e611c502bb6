package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.CustomerMessagesRequest;
import com.gclife.party.model.request.EndorseCustomerRequest;
import com.gclife.party.model.response.CustomerAgentsResponse;
import com.gclife.party.model.response.CustomerHistoryResponse;
import com.gclife.party.model.response.CustomerMessageResponse;
import com.gclife.party.service.CustomerBaseBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户管理模块
 *
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
@Api(tags = "Customer_Manage", description = "客户管理模块")
@RefreshScope
@RestController
@RequestMapping("v1/base/")
public class CustomerBaseController extends BaseController {

    @Autowired
    private CustomerBaseBusinessService customerBaseBusinessService;

    @ApiOperation(value = "模糊查询客户", notes = "模糊查询客户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "keyword/customer")
    public ResultObject<BasePageResponse<CustomerMessageResponse>> queryCustomer(CustomerMessagesRequest customerMessagesRequest){
        return customerBaseBusinessService.queryCustomerList(getAppRequestHandler(),customerMessagesRequest);
    }

    @ApiOperation(value = "获取客户信息", notes = "根据客户ID获取客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "customerId", value = "客户ID", defaultValue = "客户ID", paramType = "query", required = true),
                    @ApiImplicitParam(name = "versionNo", value = "版本号", defaultValue = "版本号", paramType = "query", required = true)
            }
    )
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "customer")
    public ResultObject<CustomerMessageResponse> getBaseCustomer(@RequestParam(value = "customerId") String customerId,@RequestParam(value = "versionNo") String versionNo){
        return customerBaseBusinessService.getBaseCustomer(customerId,versionNo);
    }


    @ApiOperation(value = "获取客户历史信息", notes = "根据客户ID和版本号获取客户历史信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "customerId", value = "客户ID", defaultValue = "客户ID", paramType = "query", required = true),
                    @ApiImplicitParam(name = "versionNo", value = "版本号", defaultValue = "版本号", paramType = "query", required = true)
            }
    )
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "history/customer")
    public ResultObject<CustomerHistoryResponse> getBaseHistoryCustomer(String customerId, String versionNo){
        return customerBaseBusinessService.getBaseCustomerHistory(customerId,versionNo);
    }

    @ApiOperation(value = "同步客户信息", notes = "根据客户ID同步客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PutMapping(value = "endorse/customer")
    public ResultObject synchronizeBaseCustomer(@RequestBody EndorseCustomerRequest endorseCustomerRequest){
        return customerBaseBusinessService.synchronizeBaseCustomer(endorseCustomerRequest,getCurrentLoginUsers());
    }

    @ApiOperation(value = "回滚客户信息", notes = "回滚客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PutMapping(value = "endorse/customer/rollback")
    public ResultObject rollbackCustomer(@RequestParam("customerId") String customerId,
                                         @RequestParam("oldVersionNo") String oldVersionNo){
        return customerBaseBusinessService.rollbackCustomer(customerId, oldVersionNo);
    }

    @ApiOperation(value = "获取客户关联的代理人客户", notes = "获取客户关联的代理人客户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "customerId", value = "客户ID", defaultValue = "客户ID", paramType = "query", required = true)
            }
    )
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "agent/customer")
    public ResultObject<List<CustomerAgentsResponse>> getAgentCustomerRelation(@RequestParam(value = "customerId") String customerId){
        return customerBaseBusinessService.getAgentCustomerRelation(customerId);
    }

    @ApiOperation(value = "获取代理人客户关联的所有代理人客户", notes = "获取代理人客户关联的所有代理人客户")
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "relation/agent/customer")
    public ResultObject<List<CustomerAgentsResponse>> getCustomerRelationByCustomerAgentId(@RequestParam(value = "customerAgentId") String customerAgentId){
        return customerBaseBusinessService.getCustomerRelationByCustomerAgentId(customerAgentId);
    }

    @ApiOperation(value = "获取代理人客户关联的家人的所有代理人客户", notes = "获取代理人客户关联的家人的所有代理人客户")
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "relation/family/customer")
    public ResultObject<List<CustomerAgentsResponse>> getCustomerByRelationship(String customerAgentId, String relationship) {
        return customerBaseBusinessService.getCustomerByRelationship(customerAgentId, relationship);
    }

    @ApiOperation(value = "查询客户信息", notes = "查询客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "customerId", value = "客户ID", defaultValue = "客户ID", paramType = "query", required = true)
            }
    )
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "customer/list")
    public ResultObject<List<CustomerMessageResponse>> getBaseCustomerList(@RequestParam(value = "customerIds") String... customerIds){
        return customerBaseBusinessService.getBaseCustomerList(customerIds);
    }
}
package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.InvestigateCustomerQueryRequest;
import com.gclife.party.model.response.InvestigateCustomerListResponse;
import com.gclife.party.service.InvestigateCustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 17:15 2018/10/15
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Api(tags = "Investigate", description = "调查title")
@RefreshScope
@RestController
@RequestMapping(value = "/v1/investigate/customer")
public class InvestigateCustomerController  extends BaseController {


    @Autowired
    private InvestigateCustomerService investigateCustomerService;

    @ApiOperation(value = "APP问卷调历史表", notes = "问卷调历史表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "app/list")
    public ResultObject<BasePageResponse<InvestigateCustomerListResponse>> queryAppInvestigateCustomerList(InvestigateCustomerQueryRequest   investigateCustomerRequest) {
        return investigateCustomerService.queryAppInvestigateCustomerList(investigateCustomerRequest,this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "系统 问卷调历史表", notes = "系统 问卷调历史表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "list")
    public ResultObject<BasePageResponse<InvestigateCustomerListResponse>> queryInvestigateCustomerList(InvestigateCustomerQueryRequest   investigateCustomerRequest) {
        return investigateCustomerService.queryInvestigateCustomerList(investigateCustomerRequest,this.getCurrentLoginUsers());
    }

}

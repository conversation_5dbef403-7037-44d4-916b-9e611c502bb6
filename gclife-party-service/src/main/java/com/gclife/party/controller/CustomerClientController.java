package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.*;
import com.gclife.party.model.response.ClientAgentResponse;
import com.gclife.party.model.response.CustomerAgentsResponse;
import com.gclife.party.model.response.InviteCustomerResponse;
import com.gclife.party.model.response.VerifyRelationResponse;
import com.gclife.party.model.response.customer.PartyCustomerResponse;
import com.gclife.party.service.CustomerClientBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * create 2022/5/27 14:07
 * description:
 */
@Api(tags = "客户App用户")
@RefreshScope
@RestController
@RequestMapping("v1/")
public class CustomerClientController extends BaseController {
    @Autowired
    private CustomerClientBusinessService customerClientBusinessService;

    @ApiOperation(value = "客户APP注册", notes = "客户APP注册")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "client/register")
    public ResultObject<CustomerAgentsResponse> registerClient(@RequestBody CustomerClientRequest customerClientRequest) {
        return customerClientBusinessService.registerClient(customerClientRequest, this.getAppRequestHandler());
    }

    @ApiOperation(value = "客户APP注册回滚", notes = "客户APP注册回滚")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @DeleteMapping(value = "client/register/rollback")
    public ResultObject<Void> registerClientRollback(@RequestParam(value = "userId") String userId) {
        return customerClientBusinessService.registerClientRollback(userId, this.getAppRequestHandler());
    }

    @ApiOperation(value = "根据userId获取客户信息", notes = "根据userId获取客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "client/customer/agent/get")
    public ResultObject<ClientAgentResponse> getClientAgentInfo(String userId) {
        return customerClientBusinessService.getClientAgentInfo(userId, this.getAppRequestHandler());
    }

    @ApiOperation(value = "修改clientCustomerAgent客户信息", notes = "修改clientCustomerAgent客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "client/customer/agent/save")
    public ResultObject saveClientAgentInfo(@RequestBody CustomerPersonalCenterRequest customerPersonalCenterRequest) {
        return customerClientBusinessService.saveClientAgentInfo(customerPersonalCenterRequest,this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "无需登录 修改clientCustomerAgent客户信息", notes = "无需登录 修改clientCustomerAgent客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "base/client/customer/agent/save")
    public ResultObject saveBaseClientAgentInfo(@RequestBody CustomerPersonalCenterRequest customerPersonalCenterRequest, @RequestParam String userId) {
        return customerClientBusinessService.saveBaseClientAgentInfo(customerPersonalCenterRequest, userId);
    }

    @ApiOperation(value = "保存注册客户实名验证信息", notes = "保存注册客户实名验证信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "client/customer/save")
    public ResultObject saveClientInfo(@RequestBody CustomerIdentityRequest customerIdentityRequest) {
        return customerClientBusinessService.saveClientInfo(customerIdentityRequest, this.getAppRequestHandler(),this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "客户信息关联验证", notes = "客户信息关联验证")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "client/relation/verify")
    public ResultObject<VerifyRelationResponse> ClientRelationVerify(@RequestBody CustomerIdentityRequest customerIdentityRequest) {
        return customerClientBusinessService.verifyClientRelation(customerIdentityRequest, this.getAppRequestHandler(),this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "客户app身份放开验证", notes = "客户app身份放开验证")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "client/app/open/verify")
    public ResultObject<VerifyRelationResponse> clientAppOpenVerify(@RequestBody CustomerIdentityRequest customerIdentityRequest) {
        return customerClientBusinessService.verifyClientAppOpen(customerIdentityRequest, this.getAppRequestHandler(),this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "业务员邀请客户关系保存", notes = "业务员邀请客户关系保存")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "agent/invite/customer/save")
    public ResultObject agentInviteCustomerSave(@RequestBody AgentInviteCustomerRequest agentInviteCustomerRequest) {
        return customerClientBusinessService.agentInviteCustomerSave(agentInviteCustomerRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "查询邀请客户", notes = "查询邀请客户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "agent/invite/customer/list")
    public ResultObject<InviteCustomerResponse> agentInviteCustomerQuery(@RequestBody AgentInviteCustomerQueryRequest agentInviteCustomerQueryRequest) {
        return customerClientBusinessService.agentInviteCustomerQuery(agentInviteCustomerQueryRequest,this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "四要素查询客户", notes = "四要素查询客户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "get/customer/by/four/elements")
    public ResultObject<PartyCustomerResponse> getCustomerByFourElements(CustomerIdentityRequest customerIdentityRequest) {
        return customerClientBusinessService.getCustomerByFourElements(customerIdentityRequest);
    }
}

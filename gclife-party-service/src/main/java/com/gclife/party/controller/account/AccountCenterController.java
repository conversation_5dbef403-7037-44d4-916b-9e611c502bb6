package com.gclife.party.controller.account;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.response.account.FundAccountOverviewResponse;
import com.gclife.party.model.response.account.TermResponse;
import com.gclife.party.service.account.AccountCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @date 2019-07-12 08:38
 */
@Api(tags = "Account_Center", description = "账户中心，仪表板数据，及国际化术语")
@RefreshScope
@RestController
@RequestMapping("v1/account/center")
public class AccountCenterController extends BaseController {



    @Autowired
    private AccountCenterService accountCenterService;

    @ApiOperation(value = "模糊查询客户", notes = "模糊查询客户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "fund/account/overview")
    public ResultObject<FundAccountOverviewResponse> fundAccountOverview(){
        return accountCenterService.fundAccountOverview(this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "账户中心国际化术语", notes = "账户中心国际化术语")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "term")
    public ResultObject<TermResponse> term(){
        return accountCenterService.term(this.getCurrentLoginUsers());

    }






















}

package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.ProtectionCustomerRequest;
import com.gclife.party.service.ProtectionCustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * create 2021-04-29
 * description: 特别保护表格
 */
@Api(tags = "Protection_Customer", description = "特别保护表格")
@RefreshScope
@RestController
@RequestMapping("v1/protection/customer/interface/")
public class ProtectionCustomerController extends BaseController {

    @Autowired
    private ProtectionCustomerService protectionCustomerService;

    @ApiOperation(value = "导出特别保护表格", notes = "导出特别保护表格")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "export")
    public void exportProtectionCustomer(HttpServletResponse httpServletResponse) {
        protectionCustomerService.exportProtectionCustomer(httpServletResponse);
    }

    @ApiOperation(value = "保存特别保护表格", notes = "保存特别保护表格")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "save")
    public ResultObject saveProtectionCustomer(@RequestBody ProtectionCustomerRequest protectionCustomerRequest) {
        return protectionCustomerService.saveProtectionCustomer(protectionCustomerRequest);
    }
}

package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.ProductEnquiryRequest;
import com.gclife.party.service.ProductEnquiryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * create 2021-05-08
 * description: 产品询盘
 */
@Api(tags = "Product_Enquiry", description = "产品询盘")
@RefreshScope
@RestController
@RequestMapping("v1/product/enquiry/interface/")
public class ProductEnquiryController {

    @Autowired
    private ProductEnquiryService productEnquiryService;

    @ApiOperation(value = "导出产品询盘表格", notes = "导出产品询盘表格")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "export")
    public void exportProductEnquiry(HttpServletResponse httpServletResponse) {
        productEnquiryService.exportProductEnquiry(httpServletResponse);
    }

    @ApiOperation(value = "保存产品询盘信息", notes = "保存产品询盘信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "save")
    public ResultObject saveProductEnquiry(@RequestBody ProductEnquiryRequest productEnquiryRequest) {
        return productEnquiryService.saveProductEnquiry(productEnquiryRequest);
    }

}

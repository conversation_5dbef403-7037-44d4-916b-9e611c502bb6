package com.gclife.party.controller.topic;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.topic.TopicSignRequest;
import com.gclife.party.model.response.topic.TopicSignRecordResponse;
import com.gclife.party.model.response.topic.TopicStatisticsSignResponse;
import com.gclife.party.service.TopicBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 17:03 2018/10/10
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Api(tags = "签到接口(2019)", description = "签到接口(2019)")
@RefreshScope
@RestController
@RequestMapping(value = "/v1/middle/")
public class TopicSignController extends BaseController {

    @Autowired
    private TopicBusinessService topicBusinessService;


    @ApiOperation(value = "查询签到统计信息", notes = "查询签到统计信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "topic/signin/statistics")
    public ResultObject<TopicStatisticsSignResponse> queryOneUserTopicSignStatistics(@RequestParam(value = "lat",required = false) String lat, @RequestParam(value = "lng",required = false)String lng) {
        return topicBusinessService.queryOneUserTopicSignStatistics(this.getCurrentLoginUsers(),lat,lng);
    }

    @ApiOperation(value = "用户签到", notes = "用户签到")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "topic/signin")
    public ResultObject userTopicSign(@RequestBody TopicSignRequest topicSignRequest) {
        return topicBusinessService.userTopicSign(this.getCurrentLoginUsers(), topicSignRequest);
    }


    @ApiOperation(value = "用户签到记录", notes = "用户签到记录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "topic/signin/record")
    public ResultObject<BasePageResponse<TopicSignRecordResponse>> getUserTopicSignRecord(BasePageRequest basePageRequest) {
        return topicBusinessService.getUserTopicSignRecord(this.getCurrentLoginUsers(), basePageRequest);
    }


}

package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.response.CustomerAgentsResponse;
import com.gclife.party.service.CustomerAgentBaseService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/6/1
 */
@Api(tags = "代理人客户", description = "代理人客户")
@RefreshScope
@RestController
@RequestMapping("v1/base/")
public class CustomerAgentBaseController {

    @Autowired
    private CustomerAgentBaseService customerAgentBaseService;

    @ApiOperation(value = "获取代理人客户信息", notes = "获取代理人客户信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "customerAgentId", value = "客户ID", defaultValue = "客户ID", paramType = "query", required = true)
            }
    )
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "get/customerAgent")
    public ResultObject<CustomerAgentsResponse> getBaseCustomerAgent(String customerAgentId) {
        return customerAgentBaseService.getBaseCustomerAgent(customerAgentId);
    }
}

package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.InvestigatePaperRequest;
import com.gclife.party.model.response.InvestigatePaperResponse;
import com.gclife.party.service.InvestigateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 17:03 2018/10/10
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Api(tags = "Investigate", description = "调查title")
@RefreshScope
@RestController
@RequestMapping(value = "/v1/investigate/")
public class InvestigateController  extends BaseController {

    @Autowired
    private InvestigateService investigateService;



    @ApiOperation(value = "查询调查标题列表", notes = "查询调查标题列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "list")
    public ResultObject<BasePageResponse<InvestigatePaperResponse>> queryInvestigateList(InvestigatePaperRequest investigatePaperRequest) {
        return investigateService.queryInvestigateList(investigatePaperRequest,this.getCurrentLoginUsers());
    }








}

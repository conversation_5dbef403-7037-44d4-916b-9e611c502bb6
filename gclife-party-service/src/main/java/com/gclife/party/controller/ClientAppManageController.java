package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.client.ClientChangeServiceRequest;
import com.gclife.party.model.request.client.ClientManageListRequest;
import com.gclife.party.model.response.client.ClientChangeRecordResponse;
import com.gclife.party.model.response.client.ClientDicResponse;
import com.gclife.party.model.response.client.ClientManageDetailResponse;
import com.gclife.party.model.response.client.ClientManageListResponse;
import com.gclife.party.service.ClientAppManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 2022/8/10 9:02
 * description:客户APP管理
 */
@Api(tags = "客户APP管理")
@RefreshScope
@RestController
@RequestMapping("v1/client/")
public class ClientAppManageController extends BaseController {
    @Autowired
    private ClientAppManageService clientAppManageService;

    @ApiOperation(value = "客户管理列表", notes = "客户管理列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "manage/list")
    public ResultObject<BasePageResponse<ClientManageListResponse>> queryClientManageList(ClientManageListRequest ClientManageListRequest) {
        return clientAppManageService.queryClientManageList(ClientManageListRequest);
    }

    @ApiOperation(value = "客户信息详情", notes = "客户信息详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "manage/detail")
    public ResultObject<ClientManageDetailResponse> queryClientInfoDetail(@RequestParam(value = "customerAgentId") String customerAgentId) {
        return clientAppManageService.queryClientInfoDetail(customerAgentId);
    }

    @ApiOperation(value = "变更保障顾问", notes = "变更保障顾问")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "change/service")
    public ResultObject<Void> postChangeService(@RequestBody ClientChangeServiceRequest clientChangeServiceRequest) {
        return clientAppManageService.postChangeService(clientChangeServiceRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "异动列表", notes = "异动列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "change/record")
    public ResultObject<List<ClientChangeRecordResponse>> getChangeRecordList(@RequestParam(value = "customerAgentId") String customerAgentId) {
        return clientAppManageService.getChangeRecordList(customerAgentId, this.getAppRequestHandler());
    }

    @ApiOperation(value = "字典数据", notes = "字典数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "dic")
    public ResultObject<ClientDicResponse> getDic() {
        return clientAppManageService.getDic(this.getAppRequestHandler());
    }
}

package com.gclife.party.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.party.model.request.InvestigateCustomerRequest;
import com.gclife.party.model.response.InvestigateCustomerResponse;
import com.gclife.party.service.InvestigateQuestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 11:10 2018/10/12
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Api(tags = "Investigate", description = "问卷调查表")
@RefreshScope
@RestController
@RequestMapping(value = "/v1/investigate/question")
public class InvestigateQuestionController extends BaseController {

    @Autowired
    private InvestigateQuestionService investigateQuestionService;

    @ApiOperation(value = "问卷调查表", notes = "问卷调查表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "app/investigate")
    public ResultObject<InvestigateCustomerResponse> queryAppInvestigateQuestion(@RequestParam(name = "versionId") String versionId, @RequestParam(name = "investigateCustomerId") String investigateCustomerId) {
        return investigateQuestionService.queryAppInvestigateQuestion(versionId, investigateCustomerId, this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "查询客户调问卷查详情", notes = "查询客户调问卷查详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "investigate")
    public ResultObject<InvestigateCustomerResponse> queryInvestigateQuestion(@RequestParam(name = "versionId") String versionId, @RequestParam(name = "investigateCustomerId") String investigateCustomerId) {
        return investigateQuestionService.queryInvestigateQuestion(versionId, investigateCustomerId, this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "保存问卷调查表", notes = "保存问卷调查表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "save")
    public ResultObject saveInvestigateQuestion(@RequestBody InvestigateCustomerRequest investigateCustomerRequest) {
        return investigateQuestionService.saveInvestigateQuestion(investigateCustomerRequest, this.getCurrentLoginUsers());
    }

}

package com.gclife.party.validate.transfer;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.common.AgentConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.*;
import com.gclife.party.core.jooq.tables.pojos.UserAccountBlackPo;
import com.gclife.party.core.jooq.tables.pojos.UserAccountDisableRecordPo;
import com.gclife.party.core.jooq.tables.pojos.UserAccountTransactionRecordPo;
import com.gclife.party.model.bo.AccountOperateListBo;
import com.gclife.party.model.bo.UserAccountBo;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.UserAccountDisableRequest;
import com.gclife.party.model.request.UserAccountIncomeRequest;
import com.gclife.party.model.request.UserAccountOutlayRequest;
import com.gclife.party.model.request.UserAccountRollbackRequest;
import com.gclife.party.model.response.UserAccountTransactionRecordResponse;
import com.gclife.party.model.response.account.AccountOperateListResponse;
import com.gclife.party.model.response.account.AccountOperateReviewListResponse;
import com.gclife.party.service.MessageBusinessService;
import com.gclife.party.service.base.UserAccountBaseService;
import com.gclife.party.validate.parameter.UserAccountParameterValidate;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.api.PlatformUsersApi;
import com.gclife.platform.api.PlatformUsersBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.platform.model.response.UserResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.party.model.config.PartyErrorConfigEnum.*;

/**
 * <AUTHOR>
 * create 19-6-19
 * description:
 */
@Component
@Slf4j
public class UserAccountTransfer extends BaseBusinessServiceImpl {
    @Autowired
    private UserAccountParameterValidate userAccountParameterValidate;
    @Autowired
    private UserAccountBaseService userAccountBaseService;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private PlatformUsersBaseApi platformUsersBaseApi;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private PlatformUsersApi platformUsersApi;
    @Autowired
    private AgentApi agentApi;

    public void saveAccountIncome(UserAccountIncomeRequest userAccountIncomeRequest) {
        userAccountParameterValidate.validateUserAccountIncomeRequest(userAccountIncomeRequest);
        String userId = userAccountIncomeRequest.getUserId();
        String userAccountTypeCode = userAccountIncomeRequest.getUserAccountTypeCode();
        UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBo(userId, userAccountTypeCode);
        AssertUtils.isNotNull(log, userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);

        //5.2.0 新增黑名单设置，黑名单用户不算账户收入
        UserAccountBlackPo userAccountBlackPo = userAccountBaseService.queryUserAccountBlack(userId, userAccountTypeCode);
        if (AssertUtils.isNotNull(userAccountBlackPo)) {
            log.info("该用户已设置成黑名单，无法操作账户，userId：{}", userId);
            return;
        }
        //用户账户
        userAccountBo.setRevenueAmount(userAccountBo.getRevenueAmount().add(userAccountIncomeRequest.getAmount()));
        userAccountBo.setResidueAmount(userAccountBo.getResidueAmount().add(userAccountIncomeRequest.getAmount()));
        userAccountBaseService.saveUserAccount(userAccountBo, userId);

        //流水记录
        UserAccountTransactionRecordPo userAccountTransactionRecordPo = new UserAccountTransactionRecordPo();
        ClazzUtils.copyPropertiesIgnoreNull(userAccountIncomeRequest, userAccountTransactionRecordPo);
        userAccountTransactionRecordPo.setCurrencyCode(TerminologyConfigEnum.CURRENCY.USD.name());
        userAccountTransactionRecordPo.setUserAccountId(userAccountBo.getUserAccountId());
        userAccountTransactionRecordPo.setStatus(PartyTermEnum.STREAM_STATUS.SUCCESS.name());
        userAccountTransactionRecordPo.setStreamCode(PartyTermEnum.STREAM_CODE.INPUT.name());
        userAccountTransactionRecordPo.setValidString(StringUtil.getFixLenthString(6));
        //流水号
        userAccountTransactionRecordPo.setSerialNo(UUIDUtils.getUUIDShort());
        userAccountBaseService.saveUserAccountTransactionRecord(userAccountTransactionRecordPo, userId);

        //不可用记录
        UserAccountDisableRequest userAccountDisableRequest = userAccountIncomeRequest.getUserAccountDisableRequest();
        if (AssertUtils.isNotNull(userAccountDisableRequest)) {
            this.saveLookAccountDisable(userId, userAccountDisableRequest, userAccountBo);
        }
        try {
            this.sendMessage(userAccountIncomeRequest);
        } catch (Exception e) {
            log.info("发送现金账户收入提醒消息失败,错误原因:{}", e.getMessage());
        }
    }

    private void sendMessage(UserAccountIncomeRequest userAccountIncomeRequest) {
        //发送现金账户收入提醒消息
        if (PartyTermEnum.USER_ACCOUNT_TYPE.CASH_ACCOUNT.name().equals(userAccountIncomeRequest.getUserAccountTypeCode())) {
            Map<String, String> messageParamMap = new HashMap<>();
            messageParamMap.put("amount", userAccountIncomeRequest.getAmount().toString());
            //收入类型国际化
            String userId = userAccountIncomeRequest.getUserId();
            ResultObject<UserResponse> userRespFcResultObject = platformUsersBaseApi.queryOneUsersPoById(userId);
            String language = AssertUtils.isResultObjectDataNull(userRespFcResultObject) ? TerminologyConfigEnum.LANGUAGE.EN_US.name() : userRespFcResultObject.getData().getLanguage();
            ResultObject<SyscodeResponse> accountBusinessTypeResultObject = platformInternationalBaseApi.queryOneInternational(TerminologyTypeEnum.ACCOUNT_BUSINESS_TYPE.name(), userAccountIncomeRequest.getBusinessTypeCode(), language);
            messageParamMap.put("commission", AssertUtils.isResultObjectDataNull(accountBusinessTypeResultObject) ? userAccountIncomeRequest.getDescription() : accountBusinessTypeResultObject.getData().getCodeName());
            log.info("现金账户收入提醒代理人,代理人ID:{},业务类型:COMMISSION_INCOME_NOTICE,参数:{}", userId, JSON.toJSONString(messageParamMap));
            messageBusinessService.pushBusinessMessage(PartyTermEnum.MSG_BUSINESS_TYPE.COMMISSION_INCOME_NOTICE.name(), Collections.singletonList(userId), messageParamMap);
        }
    }

    public UserAccountTransactionRecordResponse getUserAccountTransactionRecordResponse(UserAccountOutlayRequest userAccountOutlayRequest) {
        //非空验证
        userAccountParameterValidate.validateUserAccountOutlayRequest(userAccountOutlayRequest);

        String userId = userAccountOutlayRequest.getUserId();

        UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBo(userId, userAccountOutlayRequest.getUserAccountTypeCode());
        AssertUtils.isNotNull(log, userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);
        //1.如果账户被冻结，不能支出
        if (PartyTermEnum.ACCOUNT_STATUS.FROZEN.name().equals(userAccountBo.getAccountStatus())) {
            throw new RequestException(PARTY_THE_ACCOUNT_IS_FROZEN_AND_CANNOT_BE_OPERATED);
        }
        //不可用金额
        BigDecimal disableAmount = this.getDisableAmount(userAccountBo);
        //可用余额
        BigDecimal ableAmount = userAccountBo.getResidueAmount().subtract(disableAmount);

        //2.可用余额不得少于支出的金额(销奖和税的支出不做校验)
        if (!AgentConfigEnum.BUSINESS_TYPE.TAX.name().equals(userAccountOutlayRequest.getBusinessTypeCode()) &&
                !AgentConfigEnum.BUSINESS_TYPE.BONUS.name().equals(userAccountOutlayRequest.getBusinessTypeCode()) &&
                ableAmount.compareTo(userAccountOutlayRequest.getAmount()) < 0) {
            throw new RequestException(PARTY_INSUFFICIENT_BALANCE_AVAILABLE);
        }

        //用户账户
        userAccountBo.setExpendedAmount(userAccountBo.getExpendedAmount().add(userAccountOutlayRequest.getAmount()));
        userAccountBo.setResidueAmount(userAccountBo.getResidueAmount().subtract(userAccountOutlayRequest.getAmount()));
        userAccountBaseService.saveUserAccount(userAccountBo, userId);

        //流水记录
        UserAccountTransactionRecordPo userAccountTransactionRecordPo = new UserAccountTransactionRecordPo();
        ClazzUtils.copyPropertiesIgnoreNull(userAccountOutlayRequest, userAccountTransactionRecordPo);
        userAccountTransactionRecordPo.setCurrencyCode(TerminologyConfigEnum.CURRENCY.USD.name());
        userAccountTransactionRecordPo.setUserAccountId(userAccountBo.getUserAccountId());
        userAccountTransactionRecordPo.setValidString(StringUtil.getFixLenthString(6));
        if (AgentConfigEnum.BUSINESS_TYPE.TAX.name().equals(userAccountOutlayRequest.getBusinessTypeCode()) ||
                AgentConfigEnum.BUSINESS_TYPE.BONUS.name().equals(userAccountOutlayRequest.getBusinessTypeCode())) {
            userAccountTransactionRecordPo.setStatus(PartyTermEnum.STREAM_STATUS.SUCCESS.name());
        } else {
            userAccountTransactionRecordPo.setStatus(PartyTermEnum.STREAM_STATUS.PROCESSING.name());
        }
        userAccountTransactionRecordPo.setStreamCode(PartyTermEnum.STREAM_CODE.OUTPUT.name());
        //流水号
        userAccountTransactionRecordPo.setSerialNo(UUIDUtils.getUUIDShort());
        userAccountBaseService.saveUserAccountTransactionRecord(userAccountTransactionRecordPo, userId);

        UserAccountTransactionRecordResponse userAccountTransactionRecordResponse = new UserAccountTransactionRecordResponse();
        ClazzUtils.copyPropertiesIgnoreNull(userAccountTransactionRecordPo, userAccountTransactionRecordResponse);
        return userAccountTransactionRecordResponse;
    }


    public void saveAccountRollback(UserAccountRollbackRequest userAccountRollbackRequest) {
        AssertUtils.isNotEmpty(log, userAccountRollbackRequest.getBizId(), PARTY_USER_ACCOUNT_BIZ_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, userAccountRollbackRequest.getUserAccountTypeCode(), PARTY_USER_ACCOUNT_TYPE_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, userAccountRollbackRequest.getUserId(), PARTY_USER_ID_NOT_NULL);

        String userId = userAccountRollbackRequest.getUserId();
        UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBo(userId, userAccountRollbackRequest.getUserAccountTypeCode());
        AssertUtils.isNotNull(log, userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);

        //查询流水详情
        UserAccountTransactionRecordPo accountTransactionRecordPo = userAccountBaseService.getAccountTransactionRecordById(userAccountRollbackRequest.getBizId(), PartyTermEnum.STREAM_CODE.OUTPUT.name());
        if (AssertUtils.isNotNull(accountTransactionRecordPo) && PartyTermEnum.STREAM_STATUS.PROCESSING.name().equals(accountTransactionRecordPo.getStatus())) {
            //支出异常，复原其金额
            userAccountBo.setExpendedAmount(userAccountBo.getExpendedAmount().subtract(accountTransactionRecordPo.getAmount()));
            userAccountBo.setResidueAmount(userAccountBo.getResidueAmount().add(accountTransactionRecordPo.getAmount()));
            userAccountBaseService.saveUserAccount(userAccountBo, userId);

            //设置支付失败状态
            accountTransactionRecordPo.setStatus(PartyTermEnum.STREAM_STATUS.FAIL.name());
            userAccountBaseService.saveUserAccountTransactionRecord(accountTransactionRecordPo, userId);
        }
    }


    public void saveAccountIncomeRollback(UserAccountRollbackRequest userAccountRollbackRequest) {
        AssertUtils.isNotEmpty(log, userAccountRollbackRequest.getBizId(), PARTY_USER_ACCOUNT_BIZ_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, userAccountRollbackRequest.getUserAccountTypeCode(), PARTY_USER_ACCOUNT_TYPE_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, userAccountRollbackRequest.getUserId(), PARTY_USER_ID_NOT_NULL);

        String userId = userAccountRollbackRequest.getUserId();
        UserAccountBo userAccountBo = userAccountBaseService.getUserAccountBo(userId, userAccountRollbackRequest.getUserAccountTypeCode());
        AssertUtils.isNotNull(log, userAccountBo, PARTY_USER_ACCOUNT_IS_NOT_EXIST);

        //查询流水详情
        UserAccountTransactionRecordPo accountTransactionRecordPo = userAccountBaseService.getAccountTransactionRecordById(userAccountRollbackRequest.getBizId(), PartyTermEnum.STREAM_CODE.INPUT.name());
        List<UserAccountDisableRecordPo> accountDisableRecordPos = userAccountBaseService.getAccountDisableRecordById(userAccountRollbackRequest.getBizId());

        if (AssertUtils.isNotNull(accountTransactionRecordPo)) {
            //支出异常，复原其金额
            userAccountBo.setRevenueAmount(userAccountBo.getRevenueAmount().subtract(accountTransactionRecordPo.getAmount()));
            userAccountBo.setResidueAmount(userAccountBo.getResidueAmount().subtract(accountTransactionRecordPo.getAmount()));
            userAccountBaseService.saveUserAccount(userAccountBo, userId);

            //设置支付失败状态
            accountTransactionRecordPo.setStatus(PartyTermEnum.STREAM_STATUS.FAIL.name());
            accountTransactionRecordPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
            userAccountBaseService.saveUserAccountTransactionRecord(accountTransactionRecordPo, userId);

            if (AssertUtils.isNotEmpty(accountDisableRecordPos)) {
                accountDisableRecordPos.forEach(accountDisableRecordPo -> {
                    accountDisableRecordPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                    userAccountBaseService.saveUserAccountDisableRecord(accountDisableRecordPo, null);
                });
            }
        }
    }

    /**
     * 计算不可用金额总和
     *
     * @param userAccountBo 账户
     * @return 不可用金额总和
     */
    public BigDecimal getDisableAmount(UserAccountBo userAccountBo) {
        BigDecimal disableAmount = new BigDecimal(0);
        List<UserAccountDisableRecordPo> accountDisableRecordPos = userAccountBo.getAccountDisableRecordPos();
        if (AssertUtils.isNotEmpty(accountDisableRecordPos)) {
            for (UserAccountDisableRecordPo userAccountDisableRecordPo : accountDisableRecordPos) {
                if (PartyTermEnum.STREAM_STATUS.LOCK.name().equals(userAccountDisableRecordPo.getStatus())) {
                    disableAmount = disableAmount.add(userAccountDisableRecordPo.getAmount());
                }
            }
        }
        return disableAmount;
    }

    /**
     * 处理中的流水记录金额总和
     *
     * @param userAccountBo 账户
     * @return 处理中的流水记录金额总和
     */
    public BigDecimal getExtractAmount(UserAccountBo userAccountBo) {
        BigDecimal extractAmount = new BigDecimal(0);
        List<UserAccountTransactionRecordPo> accountTransactionRecordPos = userAccountBo.getAccountTransactionRecordPos();
        if (AssertUtils.isNotEmpty(accountTransactionRecordPos)) {
            for (UserAccountTransactionRecordPo accountTransactionRecordPo : accountTransactionRecordPos) {
                if (PartyTermEnum.STREAM_STATUS.PROCESSING.name().equals(accountTransactionRecordPo.getStatus())) {
                    extractAmount = extractAmount.add(accountTransactionRecordPo.getAmount());
                }
            }
        }
        return extractAmount;
    }

    public void saveLookAccountDisable(String userId, UserAccountDisableRequest userAccountDisableRequest, UserAccountBo userAccountBo) {
        UserAccountDisableRecordPo userAccountDisableRecordPo =
                userAccountBaseService.getAccountDisableRecordById(userAccountDisableRequest.getBizId(), userAccountDisableRequest.getBusinessTypeCode());
        if (!AssertUtils.isNotNull(userAccountDisableRecordPo)) {
            userAccountDisableRecordPo = new UserAccountDisableRecordPo();
        }
        ClazzUtils.copyPropertiesIgnoreNull(userAccountDisableRequest, userAccountDisableRecordPo);
        userAccountDisableRecordPo.setCurrencyCode(TerminologyConfigEnum.CURRENCY.USD.name());
        userAccountDisableRecordPo.setUserAccountId(userAccountBo.getUserAccountId());
        userAccountDisableRecordPo.setValidString(StringUtil.getFixLenthString(6));
        userAccountDisableRecordPo.setStatus(PartyTermEnum.STREAM_STATUS.LOCK.name());
        userAccountBaseService.saveUserAccountDisableRecord(userAccountDisableRecordPo, userId);
    }

    public List<AccountOperateListResponse> transAccountOperateList(List<AccountOperateListBo> accountOperateListBos, Users users) {
        List<AccountOperateListResponse> accountOperateListResponses = new ArrayList<>();
        if (!AssertUtils.isNotEmpty(accountOperateListBos)) {
            return accountOperateListResponses;
        }

        List<String> userIds = accountOperateListBos.stream().map(AccountOperateListBo::getUserId).distinct().collect(Collectors.toList());
        AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
        agentApplyQueryRequest.setListAgentId(userIds);
        List<AgentResponse> agentResponseList = agentApi.agentsGet(agentApplyQueryRequest).getData();
        accountOperateListBos.forEach(accountOperateListBo -> {
            AccountOperateListResponse accountOperateListResponse = new AccountOperateListResponse();
            ClazzUtils.copyPropertiesIgnoreNull(accountOperateListBo, accountOperateListResponse);
            if (AssertUtils.isNotEmpty(agentResponseList)) {
                agentResponseList.stream().filter(agentResponse -> agentResponse.getUserId().equals(accountOperateListBo.getUserId())).findFirst()
                        .ifPresent(agentResponse -> {
                            accountOperateListResponse.setUserName(agentResponse.getAgentName());
                            accountOperateListResponse.setAgentCode(agentResponse.getAgentCode());
                        });
            }
            accountOperateListResponses.add(accountOperateListResponse);
        });
        return accountOperateListResponses;
    }

    public void sendAccountReviewMessage(String businessCode, String userId, Users users) {
        try {
            Map<String, String> messageParamMap = new HashMap<>();
            ResultObject<UserResponse> userRespFcResultObject = platformUsersBaseApi.queryOneUsersPoById(userId);
            String name = AssertUtils.isResultObjectDataNull(userRespFcResultObject) ? "无" : userRespFcResultObject.getData().getName();
            messageParamMap.put("accountUserName", name);
            messageParamMap.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE6));

            log.info("发送账户冻结解冻操作消息失败,参数:{}", JSON.toJSONString(messageParamMap));

            //找内勤用户管理机构IDs
            ResultObject<BranchResponse> branchBaseRespFcResultObject = platformBranchBaseApi.queryOneUserBranch(users.getUserId());
            List<String> userIds = platformUsersApi.getBusinessUsers(businessCode, branchBaseRespFcResultObject.getData().getManagerBranchId(), "MANAGE").getData();
            if (AssertUtils.isNotEmpty(userIds)) {
                messageBusinessService.pushBusinessMessage(businessCode, userIds, messageParamMap);
            }
        } catch (Exception e) {
            log.info("发送账户冻结解冻操作消息失败,错误原因:{}", e.getMessage());
        }
    }

    public List<AccountOperateReviewListResponse> transAccountOperateReviewList(List<AccountOperateListBo> accountOperateListBos, Users users) {
        List<AccountOperateReviewListResponse> accountOperateReviewListResponses = new ArrayList<>();
        if (!AssertUtils.isNotEmpty(accountOperateListBos)) {
            return accountOperateReviewListResponses;
        }

        List<String> userIds = accountOperateListBos.stream().map(AccountOperateListBo::getUserId).distinct().collect(Collectors.toList());
//        ResultObject<List<UserResponse>> usersPoByIds = platformUsersBaseApi.queryUsersPoByIds(userIds);
        AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
        agentApplyQueryRequest.setListAgentId(userIds);
        List<AgentResponse> agentResponseList = agentApi.agentsGet(agentApplyQueryRequest).getData();
        List<String> applyUserIds = accountOperateListBos.stream().map(AccountOperateListBo::getApplyUserId).distinct().collect(Collectors.toList());
        ResultObject<List<UserResponse>> applyUsersPoByIds = platformUsersBaseApi.queryUsersPoByIds(applyUserIds);

        accountOperateListBos.forEach(accountOperateListBo -> {
            AccountOperateReviewListResponse accountOperateReviewListResponse = new AccountOperateReviewListResponse();
            ClazzUtils.copyPropertiesIgnoreNull(accountOperateListBo, accountOperateReviewListResponse);
            if (AssertUtils.isNotEmpty(agentResponseList)) {
                agentResponseList.stream().filter(agentResponse -> agentResponse.getUserId().equals(accountOperateListBo.getUserId())).findFirst()
                        .ifPresent(agentResponse -> {
                            accountOperateReviewListResponse.setUserName(agentResponse.getAgentName());
                            accountOperateReviewListResponse.setAgentCode(agentResponse.getAgentCode());
                        });
            }
            if (!AssertUtils.isResultObjectListDataNull(applyUsersPoByIds)) {
                applyUsersPoByIds.getData().stream().filter(userRespFc -> userRespFc.getUserId().equals(accountOperateListBo.getApplyUserId())).findFirst()
                        .ifPresent(userRespFc -> accountOperateReviewListResponse.setApplyUserName(userRespFc.getName()));
            }

            accountOperateReviewListResponses.add(accountOperateReviewListResponse);
        });
        return accountOperateReviewListResponses;
    }
}

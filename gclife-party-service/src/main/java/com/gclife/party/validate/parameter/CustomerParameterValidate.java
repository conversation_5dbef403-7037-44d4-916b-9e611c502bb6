package com.gclife.party.validate.parameter;

import com.gclife.common.exception.RequestException;
import com.gclife.common.interfaces.BaseInternationService;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.util.AssertUtils;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.EndorseCustomerRequest;
import com.gclife.party.model.request.policy.PolicyCoverageRequest;
import com.gclife.party.model.request.policy.PolicyRequest;
import com.gclife.party.model.request.UserCustomerBusinessRequest;
import com.gclife.party.model.request.UserCustomerRequest;
import com.gclife.platform.api.PlatformAreaApi;
import com.gclife.platform.model.response.AreaResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * create 17-11-14
 * description:
 */
@Component
public class CustomerParameterValidate{

 @Autowired
 private PlatformAreaApi platformAreaApi;

 @Autowired
 private BaseInternationService baseInternationService;
    /**
     * 日志
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerParameterValidate.class);

    public void validParameterCustomer(UserCustomerRequest userCustomerRequest) {
        AssertUtils.isNotEmpty(LOGGER, userCustomerRequest.getSex(), PartyErrorConfigEnum.PARTY_PARAMETER_SEX_IS_NOT_NULL);
        AssertUtils.isNotNull(LOGGER, userCustomerRequest.getIdType(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotNull(LOGGER, userCustomerRequest.getIdNo(), PartyErrorConfigEnum.PARTY_PARAMETER_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, verifyCodeNameByKey(TerminologyTypeEnum.GENDER.name(), userCustomerRequest.getSex()), PartyErrorConfigEnum.PARTY_PARAMETER_SEX_FORMAT_ERROR);
        if(AssertUtils.isNotEmpty(userCustomerRequest.getMobile())) {
            AssertUtils.isNotPureDigital(LOGGER, userCustomerRequest.getMobile(), PartyErrorConfigEnum.PARTY_PARAMETER_PHONE_FORMAT_ERROR);
        }
        if(AssertUtils.isNotEmpty(userCustomerRequest.getIdType())) {
            AssertUtils.isNotEmpty(LOGGER, verifyCodeNameByKey(TerminologyTypeEnum.ID_TYPE.name(), userCustomerRequest.getIdType()), PartyErrorConfigEnum.PARTY_PARAMETER_ID_TYPE_FORMAT_ERROR);
        }
        if (AssertUtils.isNotEmpty(userCustomerRequest.getEmail())) {
            AssertUtils.isEmail(LOGGER,userCustomerRequest.getEmail(),PartyErrorConfigEnum.PARTY_CUSTOMER_EMAIL_FORMAT_ERROR);
        }
    }

    public String verifyCodeNameByKey(String type, String key) throws RequestException {
        ResultObject<SyscodeRespFc> resultObject= baseInternationService.queryOneTerminology(type,key);
        if (!AssertUtils.isResultObjectDataNull(resultObject)) {
            return resultObject.getData().getCodeName();
        }
        return null;
    }

    public void validCustomerData(EndorseCustomerRequest endorseCustomerRequest,Users users) {
        AssertUtils.isNotEmpty(LOGGER, endorseCustomerRequest.getCustomerId(),PartyErrorConfigEnum.PARTY_CUSTOMER_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, endorseCustomerRequest.getMobile(),PartyErrorConfigEnum.PARTY_CUSTOMER_MOBILE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, endorseCustomerRequest.getHomeAreaCode(),PartyErrorConfigEnum.PARTY_CUSTOMER_HOME_AREA_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, endorseCustomerRequest.getHomeAddress(),PartyErrorConfigEnum.PARTY_CUSTOMER_HOME_ADDRESS_IS_NOT_NULL);
        //AssertUtils.isNotEmpty(LOGGER, endorseCustomerRequest.getEmail(),PartyErrorConfigEnum.PARTY_CUSTOMER_EMAIL_IS_NOT_NULL);

        validCustomerDataFormat(endorseCustomerRequest,users);
    }

    private void validCustomerDataFormat(EndorseCustomerRequest endorseCustomerRequest, Users users) {
        if(AssertUtils.isNotEmpty(endorseCustomerRequest.getHomePhone())) {
            AssertUtils.isNotPureDigital(LOGGER, endorseCustomerRequest.getHomePhone(), PartyErrorConfigEnum.PARTY_CUSTOMER_HOME_PHONE_FORMAT_ERROR);
        }
        AssertUtils.isNotPureDigital(LOGGER,endorseCustomerRequest.getMobile(),PartyErrorConfigEnum.PARTY_CUSTOMER_MOBILE_FORMAT_ERROR);
        ResultObject<AreaResponse> homeAreaResultObject = platformAreaApi.areaInfoGet(endorseCustomerRequest.getHomeAreaCode());
        AssertUtils.isResultObjectDataNull(LOGGER, homeAreaResultObject, PartyErrorConfigEnum.PARTY_CUSTOMER_HOME_AREA_CODE_IS_NOT_EXIST);
        //AssertUtils.isEmail(LOGGER,endorseCustomerRequest.getEmail(), PartyErrorConfigEnum.PARTY_CUSTOMER_EMAIL_FORMAT_ERROR);
    }

    public void validateCustomerBusiness(UserCustomerBusinessRequest userCustomerBusiness) {
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusiness.getIdType(),PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusiness.getIdNo(),PartyErrorConfigEnum.PARTY_PARAMETER_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusiness.getName(),PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusiness.getUserId(),PartyErrorConfigEnum.PARTY_PARAMETER_USER_ID_IS_NOT_NULL);
    }

    public void validateCustomerBusinessSingle(UserCustomerBusinessRequest userCustomerBusinessRequest) {
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusinessRequest.getCustomerId(),PartyErrorConfigEnum.PARTY_CUSTOMER_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusinessRequest.getIdType(),PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusinessRequest.getIdNo(),PartyErrorConfigEnum.PARTY_PARAMETER_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER,userCustomerBusinessRequest.getName(),PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_NAME_IS_NOT_NULL);
    }
    
    /**
     * 校验保单保存数据
     * @param policyRequest
     */
    public void verifyPolicyData(PolicyRequest policyRequest) {
        AssertUtils.isNotEmpty(LOGGER, policyRequest.getCustomerId(), PartyErrorConfigEnum.PARTY_CUSTOMER_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, policyRequest.getProviderId(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PROVIDER_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, policyRequest.getProductName(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, policyRequest.getEffectiveDateFormat(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_EFFECTIVE_DATE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(LOGGER, policyRequest.getPremium(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PREMIUM_IS_NOT_NULL);
        AssertUtils.isNotDigital(LOGGER, policyRequest.getPremium(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PREMIUM_ERROR);
        if (AssertUtils.isNotEmpty(policyRequest.getPolicyCoverages())) {
            policyRequest.getPolicyCoverages().forEach(coverageRequest -> {
                AssertUtils.isNotEmpty(LOGGER, coverageRequest.getPrimaryFlag(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_PRIMARY_FLAG_IS_NOT_NULL);
                AssertUtils.isNotEmpty(LOGGER, coverageRequest.getDutyClassId(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_DUTY_CLASS_IS_NOT_NULL);
                AssertUtils.isNotEmpty(LOGGER, coverageRequest.getAmount(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_AMOUNT_IS_NOT_NULL);
                AssertUtils.isNotDigital(LOGGER, coverageRequest.getAmount(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_AMOUNT_ERROR);
                AssertUtils.isNotEmpty(LOGGER, coverageRequest.getCoveragePeriod(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_COVERAGE_PERIOD_IS_NOT_NULL);
                if (PartyTermEnum.PRIMARY_FLAG.ADDITIONAL.name().equals(coverageRequest.getPrimaryFlag())) {
                    AssertUtils.isNotEmpty(LOGGER, coverageRequest.getProductName(), PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_NAME_IS_NOT_NULL);
                }
            });
            Optional<PolicyCoverageRequest> optional = policyRequest.getPolicyCoverages().stream()
                    .filter(coverageRequest -> PartyTermEnum.PRIMARY_FLAG.MAIN.name().equals(coverageRequest.getPrimaryFlag()))
                    .findFirst();
            if (!optional.isPresent()) {
                throw new RequestException(PartyErrorConfigEnum.PARTY_POLICY_PARAMETER_PRODUCT_MAIN_IS_NOT_NULL);
            }
        }
    }
}
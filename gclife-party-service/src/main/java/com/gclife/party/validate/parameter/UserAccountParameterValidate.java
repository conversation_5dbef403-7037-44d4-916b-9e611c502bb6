package com.gclife.party.validate.parameter;

import com.gclife.common.exception.RequestException;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.party.model.request.*;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

import static com.gclife.party.model.config.PartyErrorConfigEnum.*;

/**
 * <AUTHOR>
 * create 19-6-15
 * description:
 */
@Component
public class UserAccountParameterValidate extends BaseBusinessServiceImpl {

    public void validateUserAccountIncomeRequest(UserAccountIncomeRequest userAccountIncomeRequest) {
        AssertUtils.isNotEmpty(this.getLogger(), userAccountIncomeRequest.getUserId(), PARTY_USER_ID_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userAccountIncomeRequest.getUserAccountTypeCode(), PARTY_USER_ACCOUNT_TYPE_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userAccountIncomeRequest.getBusinessTypeCode(), PARTY_BUSINESS_TYPE_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userAccountIncomeRequest.getBizId(), PARTY_USER_ACCOUNT_BIZ_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), userAccountIncomeRequest.getAmount(), PARTY_USER_ACCOUNT_AMOUNT_IS_NOT_NULL);

//        if (userAccountIncomeRequest.getAmount().compareTo(BigDecimal.ZERO) < 0) {
//            throw new RequestException(PARTY_THE_AMOUNT_CANNOT_BE_NEGATIVE);
//        }
        UserAccountDisableRequest userAccountDisableRequest = userAccountIncomeRequest.getUserAccountDisableRequest();
        if (AssertUtils.isNotNull(userAccountDisableRequest)) {
            userAccountDisableRequest.setUserAccountTypeCode(userAccountIncomeRequest.getUserAccountTypeCode());
            this.validateUserAccountDisableRequest(userAccountDisableRequest);
        }
    }

    public void validateUserAccountDisableRequest(UserAccountDisableRequest userAccountDisableRequest) {
        AssertUtils.isNotEmpty(this.getLogger(), userAccountDisableRequest.getUserId(), PARTY_USER_ID_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userAccountDisableRequest.getUserAccountTypeCode(), PARTY_USER_ACCOUNT_TYPE_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userAccountDisableRequest.getBizId(), PARTY_USER_ACCOUNT_BIZ_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userAccountDisableRequest.getBusinessTypeCode(), PARTY_BUSINESS_TYPE_CODE_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), userAccountDisableRequest.getAmount(), PARTY_USER_ACCOUNT_AMOUNT_IS_NOT_NULL);

        if (userAccountDisableRequest.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new RequestException(PARTY_THE_AMOUNT_CANNOT_BE_NEGATIVE);
        }
    }

    public void validateUserAccountOutlayRequest(UserAccountOutlayRequest userAccountOutlayRequest) {
        AssertUtils.isNotEmpty(this.getLogger(), userAccountOutlayRequest.getUserId(), PARTY_USER_ID_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userAccountOutlayRequest.getUserAccountTypeCode(), PARTY_USER_ACCOUNT_TYPE_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userAccountOutlayRequest.getBusinessTypeCode(), PARTY_BUSINESS_TYPE_CODE_IS_NOT_NULL);
        //支出不用传银行账户ID
//        AssertUtils.isNotEmpty(this.getLogger(), userAccountOutlayRequest.getBankAccountId(), PARTY_BANK_ACCOUNT_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userAccountOutlayRequest.getBizId(), PARTY_USER_ACCOUNT_BIZ_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), userAccountOutlayRequest.getAmount(), PARTY_USER_ACCOUNT_AMOUNT_IS_NOT_NULL);

        if (userAccountOutlayRequest.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new RequestException(PARTY_THE_AMOUNT_CANNOT_BE_NEGATIVE);
        }
    }

    public void validateUserAccountNotifyRequest(UserAccountNotifyRequest userAccountNotifyRequest) {
        AssertUtils.isNotEmpty(this.getLogger(), userAccountNotifyRequest.getUserId(), PARTY_USER_ID_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), userAccountNotifyRequest.getBusinessId(), PARTY_USER_ACCOUNT_BIZ_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), userAccountNotifyRequest.getAmount(), PARTY_USER_ACCOUNT_AMOUNT_IS_NOT_NULL);

        if (userAccountNotifyRequest.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new RequestException(PARTY_THE_AMOUNT_CANNOT_BE_NEGATIVE);
        }
    }

    public void validateCashWithdrawalRequest(CashWithdrawalRequest cashWithdrawalRequest) {
        AssertUtils.isNotEmpty(this.getLogger(), cashWithdrawalRequest.getUserId(), PARTY_USER_ID_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), cashWithdrawalRequest.getAmount(), PARTY_USER_ACCOUNT_AMOUNT_IS_NOT_NULL);
        if (cashWithdrawalRequest.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new RequestException(PARTY_THE_AMOUNT_CANNOT_BE_NEGATIVE);
        }
    }
}

package com.gclife.party.validate.transfer;

import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.GB2Alpha;
import com.gclife.party.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.party.core.jooq.tables.pojos.CustomerPo;
import com.gclife.party.core.jooq.tables.pojos.CustomerRelationshipPo;
import com.gclife.party.core.jooq.tables.pojos.CustomerResemblePo;
import com.gclife.party.dao.CustomerManageBaseDao;
import com.gclife.party.model.bo.CustomerRelationshipBo;
import com.gclife.party.model.bo.MedalBo;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.EndorseCustomerRequest;
import com.gclife.party.model.request.UserCustomerRequest;
import com.gclife.party.service.base.CustomerBaseService;
import com.gclife.platform.model.response.AreaTreeResponse;
import org.jooq.tools.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 18-9-11
 * description:
 */
@Component
public class CustomerBaseTransfer extends BaseBusinessServiceImpl {
    @Autowired
    private CustomerManageBaseDao customerManageBaseDao;
    @Autowired
    private CustomerBaseService customerBaseService;

    /**
     * 校验并保存客户
     * @param userCustomerRequest
     * @param customerAgentPo
     * @param userId
     * @return
     */
    public void checkAndSaveCustomer(UserCustomerRequest userCustomerRequest, CustomerAgentPo customerAgentPo, String userId) {
        String customerId = userCustomerRequest.getCustomerId();

        //查询客户  是否存在 根据 客户  手机号or身份证号
        if (!AssertUtils.isNotEmpty(customerId)) {
            List<CustomerAgentPo> customerPoList = customerManageBaseDao.findByIdNo(userCustomerRequest.getIdType(), userCustomerRequest.getIdNo(), userId);
            if (AssertUtils.isNotEmpty(customerPoList)) {
                throw new RequestException(PartyErrorConfigEnum.PARTY_SAVE_CUSTOMER_REPEAT_ERROR);
            }
        }

        // 查询是否客户，客户禁止修改信息，只能修改关系
        boolean customerFlag = false;
        if (AssertUtils.isNotEmpty(customerId)) {
            List<MedalBo> medalList = customerManageBaseDao.getMedalList(customerId);
            if (AssertUtils.isNotEmpty(medalList)) {
                customerFlag = true;
            }
        }
        if (!customerFlag) {
            // 准客户姓、名、生日必填
            AssertUtils.isNotEmpty(getLogger(), userCustomerRequest.getFamilyName(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_NAME_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), userCustomerRequest.getGivenName(), PartyErrorConfigEnum.PARTY_PARAMETER_CUSTOMER_NAME_IS_NOT_NULL);
            AssertUtils.isNotNull(getLogger(), userCustomerRequest.getBirthdayFormat(), PartyErrorConfigEnum.PARTY_PARAMETER_BIRTHDAY_IS_NOT_NULL);
        }

        if (!customerFlag) {
            // 准客户修改信息
            ClazzUtils.copyPropertiesIgnoreNull(userCustomerRequest, customerAgentPo);
            customerAgentPo.setName(userCustomerRequest.getGivenName() + " " + userCustomerRequest.getFamilyName());
            customerAgentPo.setUserId(userId);
            String groupNo = GB2Alpha.String2AlphaFirst(customerAgentPo.getName());
            if (AssertUtils.isNotEmpty(groupNo)) {
                customerAgentPo.setGroupCode(groupNo);
            }
            customerAgentPo.setBirthday(DateUtils.stringToTime(userCustomerRequest.getBirthdayFormat(), DateUtils.FORMATE18));
            customerBaseService.saveCustomerAgent(customerAgentPo);
        }
    }

    /**
     * 保存家庭成员关系
     * @param customerId 请求数据客户ID(判断新增还是修改)
     * @param oneselfCustomerId 本人客户ID
     * @param relationCustomerId 关系客户ID
     * @param relationship 关系
     * @param userId 用户ID
     */
    public void saveCustomerRelationship(String customerId, String oneselfCustomerId, String relationCustomerId, String relationship, String userId) {
        if (AssertUtils.isNotNull(oneselfCustomerId)) {
            CustomerRelationshipPo customerRelationshipPo = new CustomerRelationshipPo();
            CustomerRelationshipPo oneselfCustomerRelationshipPo = new CustomerRelationshipPo();
            if (AssertUtils.isNotEmpty(customerId)) {
                // 查询待添加成员与本人关系
                List<CustomerRelationshipBo> customerRelationshipBos = customerManageBaseDao.listMember(oneselfCustomerId, 1);
                Optional<CustomerRelationshipBo> relationshipBoOptional = customerRelationshipBos.stream()
                        .filter(relationshipBo -> relationshipBo.getRelationCustomerId().equals(customerId))
                        .findFirst();
                if (relationshipBoOptional.isPresent()) {
                    customerRelationshipPo = relationshipBoOptional.get();
                }
                // 查询本人与待添加成员关系
                List<CustomerRelationshipBo> oneselfCustomerRelationshipBos = customerManageBaseDao.listMember(customerId, 1);
                Optional<CustomerRelationshipBo> oneselfRelationshipBoOptional = oneselfCustomerRelationshipBos.stream()
                        .filter(relationshipBo -> relationshipBo.getRelationCustomerId().equals(oneselfCustomerId))
                        .findFirst();
                if (oneselfRelationshipBoOptional.isPresent()) {
                    oneselfCustomerRelationshipPo = oneselfRelationshipBoOptional.get();
                }
            }
            // 待添加成员与本人关系
            customerRelationshipPo.setCustomerId(oneselfCustomerId);
            customerRelationshipPo.setRelationCustomerId(relationCustomerId);
            customerRelationshipPo.setRelationship(relationship);
            customerBaseService.saveCustomerRelationship(customerRelationshipPo, userId);
            // 本人与待添加成员关系
            oneselfCustomerRelationshipPo.setCustomerId(relationCustomerId);
            oneselfCustomerRelationshipPo.setRelationCustomerId(oneselfCustomerId);
            // 查询本人信息
            CustomerAgentPo oneselfCustomerAgentPo = customerBaseService.queryOneCustomerAgent(oneselfCustomerId);
            String reverseRelation = this.getReverseRelation(relationship, oneselfCustomerAgentPo.getSex());
            if (!AssertUtils.isNotEmpty(reverseRelation)) {
                throwsException(PartyErrorConfigEnum.PARTY_BUSINESS_CUSTOMER_RELATIONSHIP_ERROR);
            }
            oneselfCustomerRelationshipPo.setRelationship(reverseRelation);
            customerBaseService.saveCustomerRelationship(oneselfCustomerRelationshipPo, userId);
        }
    }

    public void transformCustomerBase(CustomerPo customerPo, EndorseCustomerRequest endorseCustomerRequest) {
        customerPo.setHomePhone(endorseCustomerRequest.getHomePhone());
        customerPo.setHomeAddress(endorseCustomerRequest.getHomeAddress());
        customerPo.setHomeZipCode(endorseCustomerRequest.getHomeZipCode());
        customerPo.setEmail(endorseCustomerRequest.getEmail());
        customerPo.setMobile(endorseCustomerRequest.getMobile());
        customerPo.setHomeAreaCode(endorseCustomerRequest.getHomeAreaCode());
    }

    public CustomerResemblePo transformCustomerResemble(CustomerPo customerPo, CustomerAgentPo customerAgentPo) {
        CustomerResemblePo customerResemblePo=new CustomerResemblePo();
        customerResemblePo.setCustomerAgentId(customerAgentPo.getCustomerAgentId());
        customerResemblePo.setCustomerAgentNo(customerAgentPo.getCustomerNo());
        customerResemblePo.setCustomerId(customerPo.getCustomerId());
        customerResemblePo.setCustomerNo(customerPo.getCustomerNo());
        return customerResemblePo;
    }

    /**
     * 计算结束日期
     * @param effectiveDate 生效日期
     * @param coveragePeriod 保障期限
     * @param birthday 生日
     * @return
     */
    public Long calculateEndDate(Long effectiveDate, String coveragePeriod, Long birthday) {
        int periodValue;
        if (PartyTermEnum.PRODUCT_PERIOD.ONE_YEAR.name().equals(coveragePeriod)) {
            periodValue = 1;
        } else if (PartyTermEnum.PRODUCT_PERIOD.LIFELONG.name().equals(coveragePeriod)) {
            return null;
        } else if (coveragePeriod.contains(PartyTermEnum.PRODUCT_PERIOD.YEAR.name())) {
            periodValue = Integer.parseInt(coveragePeriod.replace("YEAR_", ""));
        } else if (AssertUtils.isNotNull(birthday)) {
            int age = Integer.parseInt(coveragePeriod.replace("AGE_", ""));
            long applyAge = DateUtils.intervalYear(birthday, effectiveDate);
            if (applyAge >= age) {
                return null;
            } else {
                periodValue = (int) (age - applyAge);
            }
        } else {
            return null;
        }
        return DateUtils.addStringYearsRT(effectiveDate, periodValue) - 1;
    }

    public String areaNameGet(List<AreaTreeResponse> areaTreeResponses) {
        List<String> areaNames = areaTreeResponses.stream()
                .filter(areaTreeResponse -> AssertUtils.isNotEmpty(areaTreeResponse.getAreaName()))
                .map(AreaTreeResponse::getAreaName).collect(Collectors.toList());
        return StringUtils.join(areaNames.toArray(), " ");
    }

    /**
     * 获得对应关系
     * @param relationship 关系
     * @param sex 目标人员性别
     * @return
     */
    public String getReverseRelation(String relationship, String sex) {
        PartyTermEnum.RELATIONSHIP relationshipObj = PartyTermEnum.RELATIONSHIP.valueOf(relationship);
        if (!AssertUtils.isNotNull(relationshipObj)) {
            return null;
        }
        if (TerminologyConfigEnum.GENDER.MALE.name().equals(sex)) {
            return relationshipObj.maleReverse();
        } else {
            return relationshipObj.feMaleReverse();
        }
    }
}

spring:
  application:
    name: gclife-party-service
    description: '@project.description@'
  profiles:
    # 环境配置
    active: ${RUN_ENV:dev}
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${NACOS_SERVER_ADDRS}
        namespace: gclife-${spring.profiles.active}
        username: ${NACOS_SERVER_USER:nacos}
        password: ${NACOS_SERVER_PASSWORD:gclife}
        metadata:
          # 指定当前服务的环境名称，供流量控制策略进行区分
          environment.name: ${GRAY_TAG:normal}
          # 指定当前服务的环境版本，供流量控制策略进行区分
          environment.version: ${RELEASE_VERSION:1.0}
      config:
        # 配置中心地址
        server-addr: ${NACOS_SERVER_ADDRS}
        namespace: gclife-${spring.profiles.active}
        username: ${NACOS_SERVER_USER:nacos}
        password: ${NACOS_SERVER_PASSWORD:gclife}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - data-id: application.yml
            refresh: true
          # 灰度发布规则
          - data-id: gray-flow-control.yml
            refresh: true
          # 流量监控规则
          - data-id: sentinel-rule.yml
            refresh: true
          # 审计日志
          - data-id: operation-log-control.yml
            refresh: true

package com.gclife.report.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.*;
import com.gclife.platform.api.PlatformCareerApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.CareerNameResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.request.PolicyStatusResponse;
import com.gclife.product.api.ProductApi;
import com.gclife.report.core.jooq.tables.pojos.ReportAttachmentPo;
import com.gclife.report.core.jooq.tables.pojos.ReportCustomerPo;
import com.gclife.report.model.bo.ReportCustomerBo;
import com.gclife.report.model.config.ReportErrorConfigEnum;
import com.gclife.report.model.config.ReportTermEnum;
import com.gclife.report.model.request.ReportCustomerListRequest;
import com.gclife.report.model.response.ReportCustomerListResponse;
import com.gclife.report.service.ReportAttachmentBaseService;
import com.gclife.report.service.ReportQuarterlyReserveWithdrawalBaseService;
import com.gclife.report.service.ReportQueryBaseService;
import com.gclife.report.service.business.ReportCustomerBusinessService;
import com.gclife.report.validate.transform.LanguageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.report.model.config.ReportErrorConfigEnum.REPORT_POLICY_RENEWAL_IS_NULL;

/**
 * <AUTHOR>
 * create 18-11-16
 * description:
 */
@Slf4j
@Service
public class ReportCustomerBusinessServiceImpl extends BaseBusinessServiceImpl implements ReportCustomerBusinessService {
    @Autowired
    private ReportQueryBaseService reportQueryBaseService;
    @Autowired
    private LanguageUtils languageUtils;
    @Autowired
    private AttachmentApi attachmentServiceInterface;
    @Autowired
    private PlatformCareerApi platformCareerApi;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private ReportQuarterlyReserveWithdrawalBaseService reportQuarterlyReserveWithdrawalBaseService;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private ReportAttachmentBaseService reportAttachmentBaseService;

    @Override
    public ResultObject<BasePageResponse<ReportCustomerListResponse>> queryReportCustomerPageList(Users users, ReportCustomerListRequest reportCustomerListRequest) {
        ResultObject<BasePageResponse<ReportCustomerListResponse>> resultObject = new ResultObject<>();
        List<ReportCustomerListResponse> reportCustomerListResponse = new ArrayList<>();
        try {
            List<ReportCustomerBo> reportCustomerBos = reportQueryBaseService.queryListReportCustomer(reportCustomerListRequest, "list");
            if (!AssertUtils.isNotEmpty(reportCustomerBos)) {
                return resultObject;
            }
            List<String> codeTypes = Arrays.asList(TerminologyTypeEnum.ID_TYPE.name(), TerminologyTypeEnum.COMPANY_ID_TYPE.name());
            ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(users.getLanguage(), codeTypes);
            Map<String, List<SyscodeResponse>> data = mapResultObject.getData();

            //查询客户保单在保状态
            List<String> customerIdList = reportCustomerBos.stream().map(ReportCustomerPo::getCustomerId).collect(Collectors.toList());
            ResultObject<List<PolicyStatusResponse>> listResultObject = policyApi.queryPolicyStatus(customerIdList);
            AssertUtils.isResultObjectError(log, listResultObject);
            List<PolicyStatusResponse> policyStatusResponseList = listResultObject.getData();
            reportCustomerBos.forEach(reportCustomerBo -> {
                ReportCustomerListResponse reportCustomer = (ReportCustomerListResponse) this.converterObject(reportCustomerBo, ReportCustomerListResponse.class);
                if (AssertUtils.isNotEmpty(policyStatusResponseList)) {
                    policyStatusResponseList.stream().filter(policyStatusResponse -> reportCustomerBo.getCustomerId().equals(policyStatusResponse.getCustomerId())).findFirst().ifPresent(policyStatusResponse -> {
                        reportCustomer.setWhether(policyStatusResponse.getWhether());
                    });
                }

                String idType = languageUtils.getCodeName(data.get(TerminologyTypeEnum.ID_TYPE.name()), reportCustomerBo.getIdType());
                if (!AssertUtils.isNotEmpty(idType)) {
                    idType = languageUtils.getCodeName(data.get(TerminologyTypeEnum.COMPANY_ID_TYPE.name()), reportCustomerBo.getIdType());
                }
                reportCustomer.setIdType(idType);
                reportCustomerListResponse.add(reportCustomer);
            });
            //获取总数
            Integer totalLine = AssertUtils.isNotNull(reportCustomerBos) ? reportCustomerBos.get(0).getTotalLine() : null;
            BasePageResponse<ReportCustomerListResponse> basePageResponse = BasePageResponse.getData(reportCustomerListRequest.getCurrentPage(), reportCustomerListRequest.getPageSize(), totalLine, reportCustomerListResponse);
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, ReportErrorConfigEnum.REPORT_QUERY_REPORT_CUSTOMER_DATA_ERROR);
        }
        return resultObject;
    }


    @Override
    public void exportReportCustomerList(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportCustomerListRequest reportCustomerListRequest) {
        try {

            List<String> codeTypes = Arrays.asList(TerminologyTypeEnum.DEGREE.name(),
                    TerminologyTypeEnum.SOCIAL_SECURITY.name(),
                    TerminologyTypeEnum.MARITAL_STATUS.name(),
                    TerminologyTypeEnum.GENDER.name(),
                    TerminologyTypeEnum.ID_TYPE.name(),
                    TerminologyTypeEnum.COMPANY_ID_TYPE.name(),
                    TerminologyTypeEnum.NATIONALITY.name());
            ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), codeTypes);
            Map<String, List<SyscodeResponse>> data = mapResultObject.getData();
            //由输入流得到工作簿
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentServiceInterface.templateGet(ReportTermEnum.IMPORT_EXPORT_REPORT.REPORT_CUSTOMER_TEMPLATE.name());
            URL url = new URL(attachmentRespFcResultObject.getData().getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            //得到工作表
            XSSFSheet sheet = workbook.getSheetAt(0);

            try {
                getLogger().info("receivableExportRequest" + JSON.toJSONString(reportCustomerListRequest));
                getLogger().info("currentLoginUsers" + JSON.toJSONString(currentLoginUsers));
                List<ReportCustomerBo> reportCustomerBos = reportQueryBaseService.queryListReportCustomer(reportCustomerListRequest, "export");
                AssertUtils.isNotEmpty(this.getLogger(), reportCustomerBos, REPORT_POLICY_RENEWAL_IS_NULL);

                //职业
                List<String> careerIds = reportCustomerBos.stream().filter(reportCustomerBo -> AssertUtils.isNotEmpty(reportCustomerBo.getOccupationCode()))
                        .map(ReportCustomerBo::getOccupationCode).collect(Collectors.toList());
                ResultObject<List<CareerNameResponse>> careerNameRespFcObject = platformCareerApi.postCareerName(careerIds);
                List<String> customerIdList = reportCustomerBos.stream().map(ReportCustomerPo::getCustomerId).collect(Collectors.toList());
                ResultObject<List<PolicyStatusResponse>> listResultObject = policyApi.queryPolicyStatus(customerIdList);
                AssertUtils.isResultObjectError(log, listResultObject);
                List<PolicyStatusResponse> policyStatusResponseList = listResultObject.getData();
                int num = 0;
                for (int i = 2; i < sheet.getLastRowNum(); i++) {
                    if (!(reportCustomerBos.size() > num && AssertUtils.isNotNull(reportCustomerBos.get(num)))) {
                        break;
                    }
                    ReportCustomerBo reportCustomerBo = reportCustomerBos.get(num);
                    num++;
                    Row writeRow = sheet.getRow(i);
                    writeRow.getCell(0).setCellValue(num);
                    String name = reportCustomerBo.getName();
                    writeRow.getCell(1).setCellValue(cellValueFormat(name));
                    String birthday = DateUtils.timeStrToString(reportCustomerBo.getBirthday(), DateUtils.FORMATE3);
                    writeRow.getCell(2).setCellValue(cellValueFormat(birthday));
                    String sex = languageUtils.getCodeName(data.get(TerminologyTypeEnum.GENDER.name()), reportCustomerBo.getSex());
                    writeRow.getCell(3).setCellValue(cellValueFormat(sex));
                    String idType = languageUtils.getCodeName(data.get(TerminologyTypeEnum.ID_TYPE.name()), reportCustomerBo.getIdType());
                    if (!AssertUtils.isNotEmpty(idType)) {
                        idType = languageUtils.getCodeName(data.get(TerminologyTypeEnum.COMPANY_ID_TYPE.name()), reportCustomerBo.getIdType());
                    }
                    writeRow.getCell(4).setCellValue(cellValueFormat(idType));
                    String idNo = reportCustomerBo.getIdNo();
                    writeRow.getCell(5).setCellValue(cellValueFormat(idNo));
                    String idExpDate = DateUtils.timeStrToString(reportCustomerBo.getIdExpDate(), DateUtils.FORMATE3);
                    writeRow.getCell(6).setCellValue(cellValueFormat(idExpDate));
                    String marriage = languageUtils.getCodeName(data.get(TerminologyTypeEnum.MARITAL_STATUS.name()), reportCustomerBo.getMarriage());
                    writeRow.getCell(7).setCellValue(cellValueFormat(marriage));
                    String nationality = languageUtils.getCodeName(data.get(TerminologyTypeEnum.NATIONALITY.name()), reportCustomerBo.getNationality());
                    writeRow.getCell(8).setCellValue(cellValueFormat(nationality));
                    writeRow.getCell(9).setCellValue(cellValueFormat(reportCustomerBo.getStature()));
                    writeRow.getCell(10).setCellValue(cellValueFormat(reportCustomerBo.getAvoirdupois()));
                    String degree = languageUtils.getCodeName(data.get(TerminologyTypeEnum.DEGREE.name()), reportCustomerBo.getDegree());
                    writeRow.getCell(11).setCellValue(cellValueFormat(degree));
                    String socialSecurity = languageUtils.getCodeName(data.get(TerminologyTypeEnum.SOCIAL_SECURITY.name()), reportCustomerBo.getSocialSecurity());
                    writeRow.getCell(12).setCellValue(cellValueFormat(socialSecurity));

                    String occupationCode = null;
                    if (AssertUtils.isNotEmpty(reportCustomerBo.getOccupationCode()) && !AssertUtils.isResultObjectListDataNull(careerNameRespFcObject)) {
                        occupationCode = languageUtils.getBaseCareerName(careerNameRespFcObject.getData(), reportCustomerBo.getOccupationCode());
                    }
                    writeRow.getCell(13).setCellValue(cellValueFormat(occupationCode));
                    writeRow.getCell(14).setCellValue(cellValueFormat(reportCustomerBo.getIncome()));
                    writeRow.getCell(15).setCellValue(cellValueFormat(reportCustomerBo.getCompanyName()));
                    writeRow.getCell(16).setCellValue(cellValueFormat(reportCustomerBo.getPosition()));
                    writeRow.getCell(17).setCellValue(cellValueFormat(reportCustomerBo.getMobile()));
                    writeRow.getCell(18).setCellValue(cellValueFormat(reportCustomerBo.getHomePhone()));
                    writeRow.getCell(19).setCellValue(cellValueFormat(reportCustomerBo.getEmail()));
                    writeRow.getCell(20).setCellValue(cellValueFormat(reportCustomerBo.getWechatNo()));
                    writeRow.getCell(21).setCellValue(cellValueFormat(reportCustomerBo.getFacebookNo()));
                    if (AssertUtils.isNotEmpty(policyStatusResponseList)) {
                        policyStatusResponseList.stream().filter(policyStatusResponse -> reportCustomerBo.getCustomerId().equals(policyStatusResponse.getCustomerId())).findFirst().ifPresent(policyStatusResponse -> {
                            reportCustomerBo.setWhether(policyStatusResponse.getWhether());
                        });
                    }
                    writeRow.getCell(22).setCellValue(cellValueFormat(reportCustomerBo.getWhether()));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("业务报表-投保人资料.xlsx", "UTF-8"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                throw new RequestException(error.getiEnum());
            } else {
                throw new RequestException(ReportErrorConfigEnum.REPORT_REPORT_CUSTOMER_ERROR);
            }
        }
    }

    protected static String cellValueFormat(String s) {
        if (AssertUtils.isNotEmpty(s)) {
            return s;
        }
        return "--";
    }

    @Override
    public void exportReserveWithdrawalList(HttpServletResponse httpServletResponse, Users currentLoginUsers, String quarterDate, Users loginUsers) throws IOException {

        ReportAttachmentPo reportAttachmentPo = reportAttachmentBaseService.queryReportAttachmentByTypeAndDate(ReportTermEnum.IMPORT_EXPORT_REPORT.REPORT_RESERVE_WITHDRAWAL_TEMPLATE.name(), quarterDate,loginUsers.getLanguage());
        byte[] attachmentBytes;
        // 下载模板
        ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentServiceInterface.templateGet(ReportTermEnum.IMPORT_EXPORT_REPORT.REPORT_RESERVE_WITHDRAWAL_TEMPLATE.name());
        //由输入流得到工作簿
        AssertUtils.isResultObjectError(this.getLogger(),attachmentRespFcResultObject);
        AttachmentResponse attachmentResponse = attachmentRespFcResultObject.getData();
        if (AssertUtils.isNotNull(reportAttachmentPo)) {
            ResultObject<AttachmentByteResponse> attachmentByteResponseResultObject = attachmentServiceInterface.attachmentByteGet(reportAttachmentPo.getAttachmentId());
            AssertUtils.isResultObjectError(this.getLogger(),attachmentByteResponseResultObject);
            AttachmentByteResponse data = attachmentByteResponseResultObject.getData();
            attachmentBytes = data.getFileByte();
        } else {
            InputStream inputStream = new URL(attachmentResponse.getUrl()).openStream();
            attachmentBytes = ByteToInputStream.input2byte(inputStream);
            inputStream.close();
        }
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("application/x-download");
        httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(attachmentResponse.getTemplateName() + "." + attachmentResponse.getFileSuffix(), "UTF-8"));
        OutputStream outputStream = httpServletResponse.getOutputStream();
        outputStream.write(attachmentBytes);
        outputStream.close();
        attachmentBytes.clone();
    }


}

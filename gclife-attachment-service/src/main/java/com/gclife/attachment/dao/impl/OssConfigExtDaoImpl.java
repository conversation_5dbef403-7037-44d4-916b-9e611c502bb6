package com.gclife.attachment.dao.impl;

import com.gclife.attachment.dao.OssConfigExtDao;
import com.gclife.attachment.model.bo.OssConfigBo;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import static com.gclife.attachment.core.jooq.tables.OssConfig.OSS_CONFIG;
import static com.gclife.attachment.core.jooq.tables.OssUser.OSS_USER;
import static com.gclife.attachment.core.jooq.tables.OssUserRole.OSS_USER_ROLE;


/**
 * <AUTHOR>
 * create 17-10-16
 * description:用于操作oss_config表的实现类
 */
@Repository
public class OssConfigExtDaoImpl extends BaseDaoImpl implements OssConfigExtDao {

    @Override
    public OssConfigBo loadOssConfigBo(String roleId) {
        OssConfigBo ossConfigBo;
        try{
            ossConfigBo = this.getDslContext()
                    .select(OSS_USER.fields())
                    .select(OSS_CONFIG.fields())
                    .from(OSS_USER_ROLE)
                    .innerJoin(OSS_USER).on(OSS_USER.OSS_USER_ID.eq(OSS_USER_ROLE.OSS_USER_ID))
                    .innerJoin(OSS_CONFIG).on(OSS_USER.OSS_CONFIG_ID.eq(OSS_CONFIG.OSS_CONFIG_ID))
                    .where(OSS_USER_ROLE.ROLE_ID.eq(roleId))
                    .and(OSS_CONFIG.ENABLED.eq(AttachmentTermEnum.ENABLED.ENABLED.name()))
                    .fetchOneInto(OssConfigBo.class);
        }catch (Exception e){
            e.printStackTrace();
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_QUERY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_QUERY_ATTACHMENT_ERROR);
        }
        return ossConfigBo;
    }
}
package com.gclife.attachment.dao.impl;

import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.attachment.core.jooq.tables.pojos.ImportExportTemplatePo;
import com.gclife.attachment.dao.AttachmentExtDao;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.gclife.attachment.core.jooq.tables.Attachment.ATTACHMENT;
import static com.gclife.attachment.core.jooq.tables.ImportExportTemplate.IMPORT_EXPORT_TEMPLATE;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 用于操作attachment表的实现类
 */
@Repository
public class AttachmentExtDaoImpl extends BaseDaoImpl implements AttachmentExtDao {


    @Override
    public AttachmentPo loadAttachmentPo(String mediaId) {
        AttachmentPo attachmentPo;
        try {
            attachmentPo = this.getDslContext()
                    .select(ATTACHMENT.fields())
                    .from(ATTACHMENT)
                    .where(ATTACHMENT.ATTACHMENT_ID.eq(mediaId))
                    .fetchOneInto(AttachmentPo.class);

        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_QUERY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_QUERY_ATTACHMENT_ERROR);
        }
        return attachmentPo;
    }

    @Override
    public ImportExportTemplatePo getTemplate(String templateCode) {
        ImportExportTemplatePo importExportTemplatePo = null;
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(IMPORT_EXPORT_TEMPLATE.fields())
                    .from(IMPORT_EXPORT_TEMPLATE);
            List<Condition> conditionList = new ArrayList<>();
            conditionList.add(IMPORT_EXPORT_TEMPLATE.TEMPLATE_CODE.eq(templateCode));
            selectJoinStep.where(conditionList);
            importExportTemplatePo = (ImportExportTemplatePo)selectJoinStep.fetchOneInto(ImportExportTemplatePo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_IMPORT_EXPORT_TEMPLATE_QUERY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_IMPORT_EXPORT_TEMPLATE_QUERY_ATTACHMENT_ERROR);
        }
        return importExportTemplatePo;
    }


    @Override
    public List<AttachmentPo> queryAttachment(List<String> mediaIds) {
        SelectConditionStep<Record> selectConditionStep = this.getDslContext().select(ATTACHMENT.fields()).from(ATTACHMENT).where(ATTACHMENT.ATTACHMENT_ID.in(mediaIds));
        return selectConditionStep.fetchInto(AttachmentPo.class);
    }

    @Override
    public List<AttachmentPo> listAttachmentPoByPk(List<String> attachmentIds) {
        return getDslContext()
                .selectFrom(ATTACHMENT)
                .where(ATTACHMENT.ATTACHMENT_ID.in(attachmentIds))
                .fetchInto(AttachmentPo.class);
    }

    @Override
    public AttachmentPo getAttachmentPoByPk(String attachmentId) {
        return getDslContext()
                .selectFrom(ATTACHMENT)
                .where(ATTACHMENT.ATTACHMENT_ID.eq(attachmentId))
                .fetchOneInto(AttachmentPo.class);
    }
}
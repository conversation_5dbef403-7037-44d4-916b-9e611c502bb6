package com.gclife.attachment.dao.policy.impl.agent;

import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.agent.GcExamCertificateData;
import com.gclife.attachment.validate.transform.PdfTransData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/27
 */
@Repository("GC_EXAM_CERTIFICATE")
public class GcExamCertificateDaoImpl extends BaseBusinessServiceImpl implements PrintDao {
    @Autowired
    private GcExamCertificateData gcExamCertificateData;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private PdfTransData pdfTransData;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.getLogger().info("yhj -> electronicPolicyGeneratorRequest {}", electronicPolicyGeneratorRequest);
        String language = electronicPolicyGeneratorRequest.getLanguage();
        List<PrintObject> certificateData = gcExamCertificateData.getCertificateData(electronicPolicyGeneratorRequest);
        byte[] rtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(electronicPolicyGeneratorRequest.getTemplateId());
        byte[] bytes = PrintCommon.fillData(rtfBytes, certificateData);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(bytes);
        attachmentResponse.setTemplateType("GC_EXAM_CERTIFICATE");
        Boolean convertImageFlag = electronicPolicyGeneratorRequest.getConvertImageFlag();
        if (AssertUtils.isNotNull(convertImageFlag) && convertImageFlag) {
            pdfTransData.pdf2Image(attachmentResponse.getMediaId());
        }
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }
}

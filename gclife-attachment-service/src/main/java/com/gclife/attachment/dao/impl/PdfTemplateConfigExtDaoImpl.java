package com.gclife.attachment.dao.impl;

import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import org.jooq.Condition;
import org.jooq.SelectJoinStep;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.gclife.attachment.core.jooq.Tables.PDF_TEMPLATE_CONFIG;
import static com.gclife.attachment.model.config.AttachmentPdfEnum.POLICY_RENEWAL;

/**
 * <AUTHOR>
 * create 18-1-5
 * description:查询pdf_template_config表的实现类
 */
@Repository
public class PdfTemplateConfigExtDaoImpl extends BaseDaoImpl implements PdfTemplateConfigExtDao {

    /**
     * 查询pdf_template_config表中的记录
     *
     * @param electronicPolicyGeneratorRequest
     * @return PdfTemplateConfigBo
     */
    @Override
    public PdfTemplateConfigBo loadPdfTemplateConfig(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        PdfTemplateConfigBo pdfTemplateConfigBo;
        try {

            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(PDF_TEMPLATE_CONFIG.fields())
                    .from(PDF_TEMPLATE_CONFIG);

            List<Condition> conditionList = new ArrayList<>();
            if (!electronicPolicyGeneratorRequest.getPdfType().equals(POLICY_RENEWAL.name())&&AssertUtils.isNotEmpty(electronicPolicyGeneratorRequest.getProductId())) {
                conditionList.add(PDF_TEMPLATE_CONFIG.PRODUCT_ID.likeRegex(electronicPolicyGeneratorRequest.getProductId()));
            }
            conditionList.add(PDF_TEMPLATE_CONFIG.PDF_TYPE.eq(electronicPolicyGeneratorRequest.getPdfType()));
            conditionList.add(PDF_TEMPLATE_CONFIG.LANGUAGE.eq(electronicPolicyGeneratorRequest.getLanguage()));

            selectJoinStep.where(conditionList);

            selectJoinStep.orderBy(PDF_TEMPLATE_CONFIG.PRODUCT_ID.sortAsc(electronicPolicyGeneratorRequest.getProductId()));

            selectJoinStep.limit(1);

            pdfTemplateConfigBo = (PdfTemplateConfigBo) selectJoinStep.fetchOneInto(PdfTemplateConfigBo.class);
        } catch (Exception e) {
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_QUERY_PDF_TEMPLATE_CONFIG.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_QUERY_PDF_TEMPLATE_CONFIG);
        }
        return pdfTemplateConfigBo;
    }


}

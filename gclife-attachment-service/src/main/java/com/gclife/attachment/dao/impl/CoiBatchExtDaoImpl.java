package com.gclife.attachment.dao.impl;

import com.gclife.attachment.core.jooq.tables.pojos.CoiBatchPo;
import com.gclife.attachment.dao.CoiBatchExtDao;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import org.jooq.Record;
import org.jooq.SelectSeekStep1;
import org.springframework.stereotype.Repository;

import java.util.List;
import static com.gclife.attachment.core.jooq.tables.CoiBatch.COI_BATCH;

@Repository
public class CoiBatchExtDaoImpl extends BaseDaoImpl implements CoiBatchExtDao {
    @Override
    public List<CoiBatchPo> queryAttachment(String batchId) {
        SelectSeekStep1<Record, Long> records = this.getDslContext().select(COI_BATCH.fields()).from(COI_BATCH)
                .where(COI_BATCH.BATCHID.in(batchId)).orderBy(COI_BATCH.ATTACHMENT_SEQ.asc());
        return records.fetchInto(CoiBatchPo.class);
    }

    /*@Override
    public List<CoiBatchPo> queryAttachmentByCondition(String businessId, String language) {
        SelectSeekStep1<Record, Long> records = this.getDslContext().select(COI_BATCH.fields()).from(COI_BATCH)
                .where(COI_BATCH.BUSINESS_ID.eq(businessId).and(COI_BATCH.LANGUAGE.eq(language)))
                .orderBy(COI_BATCH.ATTACHMENT_SEQ.asc());
        return records.fetchInto(CoiBatchPo.class);
    }*/
}

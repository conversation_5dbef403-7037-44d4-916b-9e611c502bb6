package com.gclife.attachment.dao.policy.impl.endorse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.apply.ApplyBo;
import com.gclife.attachment.model.policy.endorse.CustomerPrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.endorse.EndorseCustomerContactChangeData;
import com.gclife.common.util.ByteToInputStream;
import net.sf.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 14:44 2018/9/26
 * @ Description: 客户联系方式变更
 * @ Modified By:
 * @ Version:
 */
@Repository("CUSTOMER_CONTACT_CHANGE")
public class CustomerContactChangeDaoImpl implements PrintDao {

    private static final Logger log = LoggerFactory.getLogger(CustomerContactChangeDaoImpl.class);
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private EndorseCustomerContactChangeData endorseCustomerContactChangeData;
    @Autowired
    private ITextPdfService iTextPdfService;


    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

         this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

         this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

         this.print(electronicPolicyGeneratorRequest);
    }


    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        log.info("---------> yhj 打印客户联系方式变更");
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
//        InputStream inputStream = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId()).getObjectContent();
        InputStream inputStream = new ByteArrayInputStream(attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId()));
        String content = electronicPolicyGeneratorRequest.getContent();
        List<byte[]> byteList = new ArrayList<>();

        List<CustomerPrintBo> customerPrintBoList = JSONObject.parseArray(content, CustomerPrintBo.class);
        byte[] input2byte = ByteToInputStream.input2byte(inputStream);
        for (CustomerPrintBo customerPrintBo : customerPrintBoList) {
            electronicPolicyGeneratorRequest.setContent(JSON.toJSONString(customerPrintBo));
            List<PrintObject> printObjectList = endorseCustomerContactChangeData.getData(electronicPolicyGeneratorRequest);
            byteList.add(PrintCommon.fillData(input2byte, printObjectList));
        }
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.mergePdfFiles(byteList));
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }


}
